import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/auth_providers.dart';

/// شاشة استعادة كلمة المرور، معاد هيكلتها باستخدام Riverpod.
///
/// أصبحت الواجهة الآن "غبية" (Dumb Widget)، ومسؤوليتها الوحيدة هي عرض الحالة
/// وتفويض الأحداث إلى الـ Controller.
class ForgotPasswordScreen extends ConsumerWidget {
  const ForgotPasswordScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مراقبة حالة الـ Controller
    final state = ref.watch(forgotPasswordControllerProvider);
    // قراءة الـ Controller لتفويض الأحداث إليه
    final controller = ref.read(forgotPasswordControllerProvider.notifier);

    // استخدام TextEditingController محلي لإدارة حقل النص
    final emailController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('استعادة كلمة المرور'),
      ),
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'أدخل بريدك الإلكتروني',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.headlineSmall,
              ),
              const SizedBox(height: 8),
              Text(
                'سنرسل لك رابطًا لإعادة تعيين كلمة المرور.',
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 32),
              TextField(
                controller: emailController,
                decoration: const InputDecoration(
                  labelText: 'البريد الإلكتروني',
                  prefixIcon: Icon(Icons.email_outlined),
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.emailAddress,
              ),
              const SizedBox(height: 24),
              // عرض مؤشر التحميل بناءً على الحالة من الـ provider
              state.isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: () {
                        // استدعاء الدالة من الـ Controller عند الضغط
                        controller.sendPasswordResetEmail(emailController.text);
                      },
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                      ),
                      child: const Text('إرسال الرابط'),
                    ),
              // عرض رسالة النجاح أو الخطأ بناءً على الحالة من الـ provider
              if (state.message.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Text(
                    state.message,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: state.isSuccess ? Colors.green : Colors.red,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
