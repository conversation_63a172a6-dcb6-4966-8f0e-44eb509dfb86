
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/admin_screens/widgets/student_form_dialog.dart';
import 'package:school_management_system/providers/services_provider.dart';

class StudentsList extends ConsumerWidget {
  final List<StudentModel> students;

  const StudentsList({super.key, required this.students});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.builder(
      itemCount: students.length,
      itemBuilder: (context, index) {
        final student = students[index];
        return Card(
          margin: const EdgeInsets.all(8.0),
          child: ListTile(
            title: Text(student.name),
            subtitle: Text('${student.studentNumber} - ${student.studentClass}'),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _showStudentFormDialog(context, ref, student: student)),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  onPressed: () => _confirmDelete(context, ref, student),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _showStudentFormDialog(BuildContext context, WidgetRef ref, {StudentModel? student}) {
    showDialog(
      context: context,
      builder: (context) => StudentFormDialog(student: student),
    );
  }

  void _confirmDelete(BuildContext context, WidgetRef ref, StudentModel student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من رغبتك في حذف الطالب ${student.name}؟'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              ref.read(firebaseServiceProvider).deleteStudent(student);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
