import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/conversation_model.dart';

/// نموذج الموعد المتقدم
///
/// يمثل موعد أو اجتماع في النظام المدرسي
/// يدعم جميع أنواع المواعيد والاجتماعات بين أولياء الأمور والمدرسة
///
/// الميزات المتقدمة:
/// - دعم المواعيد الفردية والجماعية
/// - تصنيف المواعيد حسب النوع والغرض
/// - تتبع حالة الموعد (معلق، مؤكد، ملغي، مكتمل)
/// - ربط الموعد بطالب أو مجموعة طلاب
/// - دعم المواعيد المتكررة (يومي، أسبوعي، شهري)
/// - إشعارات تلقائية قبل الموعد
/// - تسجيل ملاحظات ونتائج الاجتماع
/// - تقييم جودة الاجتماع من قبل المشاركين
class AppointmentModel {
  /// معرف الموعد الفريد
  final String id;

  /// عنوان الموعد أو الاجتماع
  final String title;

  /// وصف تفصيلي للموعد والغرض منه
  final String description;

  /// نوع الموعد (اجتماع أولياء أمور، مقابلة شخصية، استشارة، إلخ)
  final AppointmentType type;

  /// فئة الموعد (أكاديمي، سلوكي، إداري، طبي، إلخ)
  final AppointmentCategory category;

  /// أولوية الموعد (منخفضة، عادية، مهمة، عاجلة)
  final AppointmentPriority priority;

  /// حالة الموعد (معلق، مؤكد، ملغي، مكتمل، مؤجل)
  final AppointmentStatus status;

  /// تاريخ ووقت بداية الموعد
  final DateTime startTime;

  /// تاريخ ووقت انتهاء الموعد
  final DateTime endTime;

  /// مدة الموعد بالدقائق (محسوبة تلقائياً)
  int get durationInMinutes => endTime.difference(startTime).inMinutes;

  /// موقع الاجتماع (مكتب المدير، قاعة الاجتماعات، عبر الإنترنت)
  final String location;

  /// رابط الاجتماع الإلكتروني (إن وجد)
  final String? meetingLink;

  /// كلمة مرور الاجتماع الإلكتروني (إن وجدت)
  final String? meetingPassword;

  /// معرف منظم الموعد (عادة من الإدارة أو المعلم)
  final String organizerId;

  /// اسم منظم الموعد
  final String organizerName;

  /// دور منظم الموعد (مدير، معلم، مرشد، إلخ)
  final String organizerRole;

  /// قائمة معرفات المشاركين في الموعد
  final List<String> participantIds;

  /// تفاصيل المشاركين (الأسماء والأدوار)
  final Map<String, ParticipantInfo> participants;

  /// معرف الطالب المرتبط بالموعد (إن وجد)
  final String? studentId;

  /// اسم الطالب المرتبط بالموعد
  final String? studentName;

  /// معرف الصف المرتبط بالموعد (للاجتماعات الجماعية)
  final String? classId;

  /// اسم الصف المرتبط بالموعد
  final String? className;

  /// هل الموعد متكرر؟
  final bool isRecurring;

  /// نمط التكرار (يومي، أسبوعي، شهري، سنوي)
  final RecurrencePattern? recurrencePattern;

  /// تاريخ انتهاء التكرار (إن وجد)
  final DateTime? recurrenceEndDate;

  /// عدد مرات التكرار المتبقية
  final int? remainingRecurrences;

  /// معرف الموعد الأصلي (للمواعيد المتكررة)
  final String? parentAppointmentId;

  /// تاريخ إنشاء الموعد
  final DateTime createdAt;

  /// تاريخ آخر تحديث للموعد
  final DateTime updatedAt;

  /// معرف من قام بإنشاء الموعد
  final String createdBy;

  /// معرف من قام بآخر تحديث
  final String? updatedBy;

  /// تاريخ تأكيد الموعد
  final DateTime? confirmedAt;

  /// معرف من قام بتأكيد الموعد
  final String? confirmedBy;

  /// سبب الإلغاء أو التأجيل (إن وجد)
  final String? cancellationReason;

  /// تاريخ الإلغاء أو التأجيل
  final DateTime? cancelledAt;

  /// معرف من قام بالإلغاء
  final String? cancelledBy;

  /// ملاحظات إضافية على الموعد
  final String? notes;

  /// ملاحظات خاصة بالمنظم
  final String? organizerNotes;

  /// نتائج الاجتماع (بعد انتهائه)
  final String? meetingOutcome;

  /// الإجراءات المطلوبة بعد الاجتماع
  final List<String> actionItems;

  /// المرفقات المرتبطة بالموعد
  final List<AppointmentAttachment> attachments;

  /// تقييمات المشاركين للاجتماع
  final Map<String, AppointmentRating> ratings;

  /// إعدادات التذكير
  final List<ReminderSetting> reminders;

  /// العلامات والتصنيفات الإضافية
  final List<String> tags;

  /// هل الموعد سري أو خاص؟
  final bool isPrivate;

  /// هل يمكن للمشاركين دعوة آخرين؟
  final bool allowGuestInvites;

  /// الحد الأقصى لعدد المشاركين
  final int? maxParticipants;

  /// هل يتطلب الموعد موافقة مسبقة؟
  final bool requiresApproval;

  /// معرف من يجب أن يوافق على الموعد
  final String? approvalRequiredFrom;

  /// حالة الموافقة (معلقة، موافق عليها، مرفوضة)
  final ApprovalStatus? approvalStatus;

  /// تاريخ الموافقة أو الرفض
  final DateTime? approvalDate;

  /// ملاحظات الموافقة أو سبب الرفض
  final String? approvalNotes;

  /// معلومات إضافية (JSON)
  final Map<String, dynamic> metadata;

  const AppointmentModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.category,
    required this.priority,
    required this.status,
    required this.startTime,
    required this.endTime,
    required this.location,
    this.meetingLink,
    this.meetingPassword,
    required this.organizerId,
    required this.organizerName,
    required this.organizerRole,
    required this.participantIds,
    required this.participants,
    this.studentId,
    this.studentName,
    this.classId,
    this.className,
    this.isRecurring = false,
    this.recurrencePattern,
    this.recurrenceEndDate,
    this.remainingRecurrences,
    this.parentAppointmentId,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
    this.updatedBy,
    this.confirmedAt,
    this.confirmedBy,
    this.cancellationReason,
    this.cancelledAt,
    this.cancelledBy,
    this.notes,
    this.organizerNotes,
    this.meetingOutcome,
    this.actionItems = const [],
    this.attachments = const [],
    this.ratings = const {},
    this.reminders = const [],
    this.tags = const [],
    this.isPrivate = false,
    this.allowGuestInvites = false,
    this.maxParticipants,
    this.requiresApproval = false,
    this.approvalRequiredFrom,
    this.approvalStatus,
    this.approvalDate,
    this.approvalNotes,
    this.metadata = const {},
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory AppointmentModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return AppointmentModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      type: AppointmentType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => AppointmentType.parentMeeting,
      ),
      category: AppointmentCategory.values.firstWhere(
        (e) => e.toString() == data['category'],
        orElse: () => AppointmentCategory.academic,
      ),
      priority: AppointmentPriority.values.firstWhere(
        (e) => e.toString() == data['priority'],
        orElse: () => AppointmentPriority.normal,
      ),
      status: AppointmentStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => AppointmentStatus.pending,
      ),
      startTime: (data['startTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endTime: (data['endTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      location: data['location'] ?? '',
      meetingLink: data['meetingLink'],
      meetingPassword: data['meetingPassword'],
      organizerId: data['organizerId'] ?? '',
      organizerName: data['organizerName'] ?? '',
      organizerRole: data['organizerRole'] ?? '',
      participantIds: List<String>.from(data['participantIds'] ?? []),
      participants: (data['participants'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(
          key,
          ParticipantInfo.fromMap(value as Map<String, dynamic>),
        ),
      ),
      studentId: data['studentId'],
      studentName: data['studentName'],
      classId: data['classId'],
      className: data['className'],
      isRecurring: data['isRecurring'] ?? false,
      recurrencePattern:
          data['recurrencePattern'] != null
              ? RecurrencePattern.fromMap(
                data['recurrencePattern'] as Map<String, dynamic>,
              )
              : null,
      recurrenceEndDate: (data['recurrenceEndDate'] as Timestamp?)?.toDate(),
      remainingRecurrences: data['remainingRecurrences'],
      parentAppointmentId: data['parentAppointmentId'],
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdBy: data['createdBy'] ?? '',
      updatedBy: data['updatedBy'],
      confirmedAt: (data['confirmedAt'] as Timestamp?)?.toDate(),
      confirmedBy: data['confirmedBy'],
      cancellationReason: data['cancellationReason'],
      cancelledAt: (data['cancelledAt'] as Timestamp?)?.toDate(),
      cancelledBy: data['cancelledBy'],
      notes: data['notes'],
      organizerNotes: data['organizerNotes'],
      meetingOutcome: data['meetingOutcome'],
      actionItems: List<String>.from(data['actionItems'] ?? []),
      attachments:
          (data['attachments'] as List<dynamic>? ?? [])
              .map(
                (e) => AppointmentAttachment.fromMap(e as Map<String, dynamic>),
              )
              .toList(),
      ratings: (data['ratings'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(
          key,
          AppointmentRating.fromMap(value as Map<String, dynamic>),
        ),
      ),
      reminders:
          (data['reminders'] as List<dynamic>? ?? [])
              .map((e) => ReminderSetting.fromMap(e as Map<String, dynamic>))
              .toList(),
      tags: List<String>.from(data['tags'] ?? []),
      isPrivate: data['isPrivate'] ?? false,
      allowGuestInvites: data['allowGuestInvites'] ?? false,
      maxParticipants: data['maxParticipants'],
      requiresApproval: data['requiresApproval'] ?? false,
      approvalRequiredFrom: data['approvalRequiredFrom'],
      approvalStatus:
          data['approvalStatus'] != null
              ? ApprovalStatus.values.firstWhere(
                (e) => e.toString() == data['approvalStatus'],
                orElse: () => ApprovalStatus.pending,
              )
              : null,
      approvalDate: (data['approvalDate'] as Timestamp?)?.toDate(),
      approvalNotes: data['approvalNotes'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// تحويل النموذج إلى خريطة للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'type': type.toString(),
      'category': category.toString(),
      'priority': priority.toString(),
      'status': status.toString(),
      'startTime': Timestamp.fromDate(startTime),
      'endTime': Timestamp.fromDate(endTime),
      'location': location,
      'meetingLink': meetingLink,
      'meetingPassword': meetingPassword,
      'organizerId': organizerId,
      'organizerName': organizerName,
      'organizerRole': organizerRole,
      'participantIds': participantIds,
      'participants': participants.map(
        (key, value) => MapEntry(key, value.toMap()),
      ),
      'studentId': studentId,
      'studentName': studentName,
      'classId': classId,
      'className': className,
      'isRecurring': isRecurring,
      'recurrencePattern': recurrencePattern?.toMap(),
      'recurrenceEndDate':
          recurrenceEndDate != null
              ? Timestamp.fromDate(recurrenceEndDate!)
              : null,
      'remainingRecurrences': remainingRecurrences,
      'parentAppointmentId': parentAppointmentId,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
      'updatedBy': updatedBy,
      'confirmedAt':
          confirmedAt != null ? Timestamp.fromDate(confirmedAt!) : null,
      'confirmedBy': confirmedBy,
      'cancellationReason': cancellationReason,
      'cancelledAt':
          cancelledAt != null ? Timestamp.fromDate(cancelledAt!) : null,
      'cancelledBy': cancelledBy,
      'notes': notes,
      'organizerNotes': organizerNotes,
      'meetingOutcome': meetingOutcome,
      'actionItems': actionItems,
      'attachments': attachments.map((e) => e.toMap()).toList(),
      'ratings': ratings.map((key, value) => MapEntry(key, value.toMap())),
      'reminders': reminders.map((e) => e.toMap()).toList(),
      'tags': tags,
      'isPrivate': isPrivate,
      'allowGuestInvites': allowGuestInvites,
      'maxParticipants': maxParticipants,
      'requiresApproval': requiresApproval,
      'approvalRequiredFrom': approvalRequiredFrom,
      'approvalStatus': approvalStatus?.toString(),
      'approvalDate':
          approvalDate != null ? Timestamp.fromDate(approvalDate!) : null,
      'approvalNotes': approvalNotes,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من الموعد
  AppointmentModel copyWith({
    String? title,
    String? description,
    AppointmentType? type,
    AppointmentCategory? category,
    AppointmentPriority? priority,
    AppointmentStatus? status,
    DateTime? startTime,
    DateTime? endTime,
    String? location,
    String? meetingLink,
    String? meetingPassword,
    List<String>? participantIds,
    Map<String, ParticipantInfo>? participants,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    DateTime? updatedAt,
    String? updatedBy,
    DateTime? confirmedAt,
    String? confirmedBy,
    String? cancellationReason,
    DateTime? cancelledAt,
    String? cancelledBy,
    String? notes,
    String? organizerNotes,
    String? meetingOutcome,
    List<String>? actionItems,
    List<AppointmentAttachment>? attachments,
    Map<String, AppointmentRating>? ratings,
    List<ReminderSetting>? reminders,
    List<String>? tags,
    bool? isPrivate,
    bool? allowGuestInvites,
    int? maxParticipants,
    ApprovalStatus? approvalStatus,
    DateTime? approvalDate,
    String? approvalNotes,
    Map<String, dynamic>? metadata,
  }) {
    return AppointmentModel(
      id: id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      location: location ?? this.location,
      meetingLink: meetingLink ?? this.meetingLink,
      meetingPassword: meetingPassword ?? this.meetingPassword,
      organizerId: organizerId,
      organizerName: organizerName,
      organizerRole: organizerRole,
      participantIds: participantIds ?? this.participantIds,
      participants: participants ?? this.participants,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      classId: classId ?? this.classId,
      className: className ?? this.className,
      isRecurring: isRecurring,
      recurrencePattern: recurrencePattern,
      recurrenceEndDate: recurrenceEndDate,
      remainingRecurrences: remainingRecurrences,
      parentAppointmentId: parentAppointmentId,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      createdBy: createdBy,
      updatedBy: updatedBy ?? this.updatedBy,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      confirmedBy: confirmedBy ?? this.confirmedBy,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      cancelledBy: cancelledBy ?? this.cancelledBy,
      notes: notes ?? this.notes,
      organizerNotes: organizerNotes ?? this.organizerNotes,
      meetingOutcome: meetingOutcome ?? this.meetingOutcome,
      actionItems: actionItems ?? this.actionItems,
      attachments: attachments ?? this.attachments,
      ratings: ratings ?? this.ratings,
      reminders: reminders ?? this.reminders,
      tags: tags ?? this.tags,
      isPrivate: isPrivate ?? this.isPrivate,
      allowGuestInvites: allowGuestInvites ?? this.allowGuestInvites,
      maxParticipants: maxParticipants ?? this.maxParticipants,
      requiresApproval: requiresApproval,
      approvalRequiredFrom: approvalRequiredFrom,
      approvalStatus: approvalStatus ?? this.approvalStatus,
      approvalDate: approvalDate ?? this.approvalDate,
      approvalNotes: approvalNotes ?? this.approvalNotes,
      metadata: metadata ?? this.metadata,
    );
  }

  /// التحقق من صحة الموعد
  bool get isValid {
    return title.isNotEmpty &&
        description.isNotEmpty &&
        location.isNotEmpty &&
        organizerId.isNotEmpty &&
        organizerName.isNotEmpty &&
        participantIds.isNotEmpty &&
        startTime.isBefore(endTime) &&
        startTime.isAfter(DateTime.now().subtract(const Duration(days: 1)));
  }

  /// التحقق من إمكانية تعديل الموعد
  bool get canBeModified {
    return status == AppointmentStatus.pending ||
        status == AppointmentStatus.confirmed;
  }

  /// التحقق من إمكانية إلغاء الموعد
  bool get canBeCancelled {
    return status != AppointmentStatus.cancelled &&
        status != AppointmentStatus.completed &&
        startTime.isAfter(DateTime.now());
  }

  /// التحقق من انتهاء الموعد
  bool get isExpired {
    return endTime.isBefore(DateTime.now());
  }

  /// التحقق من قرب موعد الاجتماع (خلال الساعة القادمة)
  bool get isUpcoming {
    final now = DateTime.now();
    return startTime.isAfter(now) &&
        startTime.isBefore(now.add(const Duration(hours: 1)));
  }

  /// الحصول على الوقت المتبقي للموعد
  Duration get timeUntilStart {
    return startTime.difference(DateTime.now());
  }

  /// الحصول على نص وصفي لحالة الموعد
  String get statusDescription {
    switch (status) {
      case AppointmentStatus.pending:
        return 'في انتظار التأكيد';
      case AppointmentStatus.confirmed:
        return 'مؤكد';
      case AppointmentStatus.cancelled:
        return 'ملغي';
      case AppointmentStatus.completed:
        return 'مكتمل';
      case AppointmentStatus.postponed:
        return 'مؤجل';
      case AppointmentStatus.inProgress:
        return 'جاري الآن';
    }
  }

  /// الحصول على نص وصفي لنوع الموعد
  String get typeDescription {
    switch (type) {
      case AppointmentType.parentMeeting:
        return 'اجتماع أولياء أمور';
      case AppointmentType.personalInterview:
        return 'مقابلة شخصية';
      case AppointmentType.consultation:
        return 'استشارة';
      case AppointmentType.disciplinaryMeeting:
        return 'اجتماع تأديبي';
      case AppointmentType.academicReview:
        return 'مراجعة أكاديمية';
      case AppointmentType.counselingSession:
        return 'جلسة إرشاد';
      case AppointmentType.medicalCheckup:
        return 'فحص طبي';
      case AppointmentType.specialEvent:
        return 'فعالية خاصة';
      case AppointmentType.groupMeeting:
        return 'اجتماع جماعي';
      case AppointmentType.onlineSession:
        return 'جلسة إلكترونية';
    }
  }
}

// ===================================================================
// الفئات المساعدة والتعدادات
// ===================================================================

/// معلومات المشارك في الموعد
class ParticipantInfo {
  /// معرف المشارك
  final String id;

  /// اسم المشارك
  final String name;

  /// دور المشارك (ولي أمر، معلم، إدارة)
  final ParticipantRole role;

  /// القسم أو المادة (للمعلمين)
  final String? department;

  /// هل المشارك أكد حضوره؟
  final bool hasConfirmed;

  /// تاريخ تأكيد الحضور
  final DateTime? confirmedAt;

  /// ملاحظات المشارك
  final String? notes;

  /// صورة المشارك الشخصية
  final String? avatarUrl;

  const ParticipantInfo({
    required this.id,
    required this.name,
    required this.role,
    this.department,
    this.hasConfirmed = false,
    this.confirmedAt,
    this.notes,
    this.avatarUrl,
  });

  factory ParticipantInfo.fromMap(Map<String, dynamic> map) {
    return ParticipantInfo(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      role: ParticipantRole.values.firstWhere(
        (e) => e.toString() == map['role'],
        orElse: () => ParticipantRole.parent,
      ),
      department: map['department'],
      hasConfirmed: map['hasConfirmed'] ?? false,
      confirmedAt: (map['confirmedAt'] as Timestamp?)?.toDate(),
      notes: map['notes'],
      avatarUrl: map['avatarUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'role': role.toString(),
      'department': department,
      'hasConfirmed': hasConfirmed,
      'confirmedAt':
          confirmedAt != null ? Timestamp.fromDate(confirmedAt!) : null,
      'notes': notes,
      'avatarUrl': avatarUrl,
    };
  }
}

/// نمط التكرار للمواعيد المتكررة
class RecurrencePattern {
  /// نوع التكرار (يومي، أسبوعي، شهري، سنوي)
  final RecurrenceType type;

  /// فترة التكرار (كل كم يوم/أسبوع/شهر)
  final int interval;

  /// أيام الأسبوع للتكرار الأسبوعي
  final List<int> weekdays;

  /// يوم الشهر للتكرار الشهري
  final int? dayOfMonth;

  /// الأسبوع من الشهر (الأول، الثاني، الأخير)
  final int? weekOfMonth;

  /// عدد مرات التكرار الإجمالي
  final int? totalOccurrences;

  const RecurrencePattern({
    required this.type,
    this.interval = 1,
    this.weekdays = const [],
    this.dayOfMonth,
    this.weekOfMonth,
    this.totalOccurrences,
  });

  factory RecurrencePattern.fromMap(Map<String, dynamic> map) {
    return RecurrencePattern(
      type: RecurrenceType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => RecurrenceType.weekly,
      ),
      interval: map['interval'] ?? 1,
      weekdays: List<int>.from(map['weekdays'] ?? []),
      dayOfMonth: map['dayOfMonth'],
      weekOfMonth: map['weekOfMonth'],
      totalOccurrences: map['totalOccurrences'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'interval': interval,
      'weekdays': weekdays,
      'dayOfMonth': dayOfMonth,
      'weekOfMonth': weekOfMonth,
      'totalOccurrences': totalOccurrences,
    };
  }
}

/// مرفق الموعد
class AppointmentAttachment {
  /// معرف المرفق
  final String id;

  /// اسم الملف
  final String name;

  /// رابط الملف
  final String url;

  /// نوع الملف
  final String type;

  /// حجم الملف بالبايت
  final int size;

  /// تاريخ الرفع
  final DateTime uploadedAt;

  /// معرف من قام بالرفع
  final String uploadedBy;

  const AppointmentAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    required this.uploadedAt,
    required this.uploadedBy,
  });

  factory AppointmentAttachment.fromMap(Map<String, dynamic> map) {
    return AppointmentAttachment(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      type: map['type'] ?? '',
      size: map['size'] ?? 0,
      uploadedAt: (map['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      uploadedBy: map['uploadedBy'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'size': size,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'uploadedBy': uploadedBy,
    };
  }
}

/// تقييم الموعد من قبل المشاركين
class AppointmentRating {
  /// معرف المقيم
  final String raterId;

  /// اسم المقيم
  final String raterName;

  /// التقييم من 1 إلى 5
  final int rating;

  /// تعليق على التقييم
  final String? comment;

  /// تاريخ التقييم
  final DateTime ratedAt;

  const AppointmentRating({
    required this.raterId,
    required this.raterName,
    required this.rating,
    this.comment,
    required this.ratedAt,
  });

  factory AppointmentRating.fromMap(Map<String, dynamic> map) {
    return AppointmentRating(
      raterId: map['raterId'] ?? '',
      raterName: map['raterName'] ?? '',
      rating: map['rating'] ?? 1,
      comment: map['comment'],
      ratedAt: (map['ratedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'raterId': raterId,
      'raterName': raterName,
      'rating': rating,
      'comment': comment,
      'ratedAt': Timestamp.fromDate(ratedAt),
    };
  }
}

/// إعدادات التذكير
class ReminderSetting {
  /// نوع التذكير (إشعار، إيميل، رسالة)
  final ReminderType type;

  /// الوقت قبل الموعد بالدقائق
  final int minutesBefore;

  /// هل التذكير مفعل؟
  final bool isEnabled;

  /// رسالة التذكير المخصصة
  final String? customMessage;

  const ReminderSetting({
    required this.type,
    required this.minutesBefore,
    this.isEnabled = true,
    this.customMessage,
  });

  factory ReminderSetting.fromMap(Map<String, dynamic> map) {
    return ReminderSetting(
      type: ReminderType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ReminderType.notification,
      ),
      minutesBefore: map['minutesBefore'] ?? 15,
      isEnabled: map['isEnabled'] ?? true,
      customMessage: map['customMessage'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'minutesBefore': minutesBefore,
      'isEnabled': isEnabled,
      'customMessage': customMessage,
    };
  }
}

// ===================================================================
// التعدادات (Enums)
// ===================================================================

/// أنواع المواعيد
enum AppointmentType {
  parentMeeting, // اجتماع أولياء أمور
  personalInterview, // مقابلة شخصية
  consultation, // استشارة
  disciplinaryMeeting, // اجتماع تأديبي
  academicReview, // مراجعة أكاديمية
  counselingSession, // جلسة إرشاد
  medicalCheckup, // فحص طبي
  specialEvent, // فعالية خاصة
  groupMeeting, // اجتماع جماعي
  onlineSession, // جلسة إلكترونية
}

/// فئات المواعيد
enum AppointmentCategory {
  academic, // أكاديمي
  behavioral, // سلوكي
  administrative, // إداري
  medical, // طبي
  psychological, // نفسي
  social, // اجتماعي
  financial, // مالي
  technical, // تقني
  general, // عام
}

/// أولويات المواعيد
enum AppointmentPriority {
  low, // منخفضة
  normal, // عادية
  high, // مهمة
  urgent, // عاجلة
  critical, // حرجة
}

/// حالات المواعيد
enum AppointmentStatus {
  pending, // معلق
  confirmed, // مؤكد
  cancelled, // ملغي
  completed, // مكتمل
  postponed, // مؤجل
  inProgress, // جاري الآن
}

/// أنواع التكرار
enum RecurrenceType {
  daily, // يومي
  weekly, // أسبوعي
  monthly, // شهري
  yearly, // سنوي
}

/// حالات الموافقة
enum ApprovalStatus {
  pending, // معلقة
  approved, // موافق عليها
  rejected, // مرفوضة
}

/// أنواع التذكير
enum ReminderType {
  notification, // إشعار
  email, // إيميل
  sms, // رسالة نصية
}
