
/// نموذج بيانات الدرجات
class GradeModel {
  final String id;
  final String subject; // المادة
  final double score; // الدرجة المحصلة
  final double maxScore; // الدرجة العظمى
  final String term; // الفصل الدراسي (مثال: "الفصل الأول")

  GradeModel({
    required this.id,
    required this.subject,
    required this.score,
    required this.maxScore,
    required this.term,
  });

  factory GradeModel.fromMap(Map<String, dynamic> data, String documentId) {
    return GradeModel(
      id: documentId,
      subject: data['subject'] ?? '',
      score: (data['score'] ?? 0).toDouble(),
      maxScore: (data['maxScore'] ?? 100).toDouble(),
      term: data['term'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'subject': subject,
      'score': score,
      'maxScore': maxScore,
      'term': term,
    };
  }
}
