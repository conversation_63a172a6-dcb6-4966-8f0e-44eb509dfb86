import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart'; // إضافة هذا الاستيراد
import 'package:multi_select_flutter/multi_select_flutter.dart'; // إضافة هذا الاستيراد
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/models/teacher_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/providers/teacher_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class AddClassDialog extends ConsumerStatefulWidget {
  const AddClassDialog({super.key});

  @override
  ConsumerState<AddClassDialog> createState() => _AddClassDialogState();
}

class _AddClassDialogState extends ConsumerState<AddClassDialog> {
  final _formKey = GlobalKey<FormState>();
  final _classNameController = TextEditingController();
  String? _selectedTeacherId;
  List<StudentModel> _selectedStudents = [];
  int _currentStep = 0;

  @override
  void dispose() {
    _classNameController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate() && _selectedTeacherId != null) {
      try {
        await ref.read(firebaseServiceProvider).addClass(
          _classNameController.text,
          teacherId: _selectedTeacherId,
          studentIds: _selectedStudents.map((s) => s.id).toList(),
        );
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم إنشاء الفصل بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('إضافة فصل جديد'),
      content: SizedBox(
        width: MediaQuery.of(context).size.width * 0.5,
        height: MediaQuery.of(context).size.height * 0.6,
        child: Stepper(
          currentStep: _currentStep,
          onStepContinue: () {
            if (_currentStep == 0) {
              if (_formKey.currentState!.validate()) {
                setState(() => _currentStep++);
              }
            } else if (_currentStep == 1) {
              setState(() => _currentStep++);
            } else {
              _submitForm();
            }
          },
          onStepCancel: () {
            if (_currentStep > 0) {
              setState(() => _currentStep--);
            } else {
              Navigator.of(context).pop();
            }
          },
          steps: [
            _buildStep1ClassInfo(), // تصحيح الاسم
            _buildStep2SelectStudents(), // تصحيح الاسم
            _buildStep3Confirm(), // تصحيح الاسم
          ],
        ),
      ),
    );
  }

  Step _buildStep1ClassInfo() {
    return Step(
      title: const Text('المعلومات الأساسية'),
      isActive: _currentStep >= 0,
      state: _currentStep > 0 ? StepState.complete : StepState.indexed,
      content: Form(
        key: _formKey,
        child: Column(
          children: [
            TextFormField(
              controller: _classNameController,
              decoration: const InputDecoration(labelText: 'اسم الفصل'),
              validator: (value) => value!.isEmpty ? 'الرجاء إدخال اسم الفصل' : null,
            ),
            const SizedBox(height: 16),
            // استخدام TeacherProvider لجلب المعلمين
            Consumer(
              builder: (context, ref, child) {
                final teachersAsyncValue = ref.watch(teachersStreamProvider);
                return teachersAsyncValue.when(
                  data: (teachers) {
                    return DropdownButtonFormField<String>(
                      value: _selectedTeacherId,
                      decoration: const InputDecoration(labelText: 'المعلم المسؤول'),
                      items: teachers.map((teacher) {
                        return DropdownMenuItem(
                          value: teacher.id,
                          child: Text(teacher.name),
                        );
                      }).toList(),
                      onChanged: (value) => setState(() => _selectedTeacherId = value),
                      validator: (value) => value == null ? 'الرجاء اختيار معلم' : null,
                    );
                  },
                  loading: () => const CircularProgressIndicator(),
                  error: (err, stack) => Text('خطأ: $err'),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Step _buildStep2SelectStudents() {
    return Step(
      title: const Text('اختيار الطلاب'),
      isActive: _currentStep >= 1,
      state: _currentStep > 1 ? StepState.complete : StepState.indexed,
      content: Column(
        children: [
          // استخدام StudentProvider لجلب الطلاب
          Consumer(
            builder: (context, ref, child) {
              final studentsAsyncValue = ref.watch(studentsStreamProvider);
              return studentsAsyncValue.when(
                data: (students) {
                  return MultiSelectDialogField<StudentModel>(
                    items: students.map((s) => MultiSelectItem(s, s.name)).toList(),
                    title: const Text("الطلاب"),
                    selectedColor: Theme.of(context).primaryColor,
                    buttonText: const Text("اختر الطلاب"),
                    onConfirm: (results) {
                      setState(() => _selectedStudents = results);
                    },
                  );
                },
                loading: () => const LoadingIndicator(),
                error: (err, stack) => Text('خطأ: $err'),
              );
            },
          ),
        ],
      ),
    );
  }

  Step _buildStep3Confirm() {
    return Step(
      title: const Text('مراجعة وتأكيد'),
      isActive: _currentStep >= 2,
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('اسم الفصل: ${_classNameController.text}'),
          const SizedBox(height: 8),
          Text('المعلم المسؤول: ${_selectedTeacherId ?? "لم يتم الاختيار"}'), // This should ideally show the teacher's name
          const SizedBox(height: 8),
          Text('الطلاب المختارون: ${_selectedStudents.length}'),
          Wrap(
            spacing: 8.0,
            children: _selectedStudents.map((s) => Chip(label: Text(s.name))).toList(),
          )
        ],
      ),
    );
  }
}
