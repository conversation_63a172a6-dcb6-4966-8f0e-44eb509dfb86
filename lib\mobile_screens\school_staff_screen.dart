import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/staff_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة عرض قائمة الكادر التعليمي والإداري بالمدرسة في تطبيق الجوال باستخدام Riverpod
class SchoolStaffScreen extends ConsumerWidget {
  const SchoolStaffScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مشاهدة (watch) الـ provider لجلب بيانات الموظفين
    final staffAsyncValue = ref.watch(publicStaffStreamProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الكادر التعليمي والإداري'),
      ),
      body: staffAsyncValue.when(
        // في حالة تحميل البيانات
        loading: () => const LoadingIndicator(),
        // في حالة حدوث خطأ
        error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
        // في حالة نجاح جلب البيانات
        data: (staffList) {
          if (staffList.isEmpty) {
            return const Center(child: Text('لا يوجد موظفون لعرضهم حالياً.'));
          }

          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: staffList.length,
            itemBuilder: (context, index) {
              final staffMember = staffList[index];
              return _buildStaffMemberCard(context, staffMember);
            },
          );
        },
      ),
    );
  }

  // --- ويدجت بناء بطاقة الموظف ---
  Widget _buildStaffMemberCard(BuildContext context, UserModel staffMember) {
    return Card(
      elevation: 3,
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () {
          // عند النقر، يتم عرض ديالوج بالتفاصيل الكاملة
          _showStaffDetailsDialog(context, staffMember);
        },
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              // --- الصورة الشخصية ---
              CircleAvatar(
                radius: 35,
                backgroundColor: Colors.grey.shade200,
                backgroundImage: (staffMember.profileImageUrl != null && staffMember.profileImageUrl!.isNotEmpty)
                    ? NetworkImage(staffMember.profileImageUrl!)
                    : null,
                child: (staffMember.profileImageUrl == null || staffMember.profileImageUrl!.isEmpty)
                    ? const Icon(Icons.person, size: 35, color: Colors.grey)
                    : null,
              ),
              const SizedBox(width: 16),
              // --- المعلومات الأساسية ---
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      staffMember.name,
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      staffMember.jobTitle ?? 'عضو هيئة التدريس', // قيمة افتراضية
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(color: Theme.of(context).colorScheme.primary),
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios, color: Colors.grey, size: 16),
            ],
          ),
        ),
      ),
    );
  }

  // --- ديالوج عرض التفاصيل الكاملة للموظف ---
  void _showStaffDetailsDialog(BuildContext context, UserModel staffMember) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(15)),
          title: Center(child: Text(staffMember.name, style: const TextStyle(fontWeight: FontWeight.bold))),
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // --- الصورة الشخصية ---
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.grey.shade200,
                  backgroundImage: (staffMember.profileImageUrl != null && staffMember.profileImageUrl!.isNotEmpty)
                      ? NetworkImage(staffMember.profileImageUrl!)
                      : null,
                  child: (staffMember.profileImageUrl == null || staffMember.profileImageUrl!.isEmpty)
                      ? const Icon(Icons.person, size: 50, color: Colors.grey)
                      : null,
                ),
                const SizedBox(height: 16),
                // --- المسمى الوظيفي ---
                Text(
                  staffMember.jobTitle ?? 'عضو هيئة التدريس',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(color: Theme.of(context).colorScheme.primary),
                  textAlign: TextAlign.center,
                ),
                const Divider(height: 24),
                // --- النبذة التعريفية ---
                if (staffMember.bio != null && staffMember.bio!.isNotEmpty)
                  Text(
                    staffMember.bio!,
                    textAlign: TextAlign.center,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(height: 1.5),
                  )
                else
                  const Text(
                    'لا توجد نبذة تعريفية متاحة حالياً.',
                    style: TextStyle(color: Colors.grey),
                  ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إغلاق'),
            )
          ],
        );
      },
    );
  }
}
