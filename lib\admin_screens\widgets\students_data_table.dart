
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/admin_screens/widgets/student_form_dialog.dart';
import 'package:school_management_system/providers/services_provider.dart';

class StudentsDataTable extends ConsumerWidget {
  final List<StudentModel> students;

  const StudentsDataTable({super.key, required this.students});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: DataTable(
        columns: const [
          DataColumn(label: Text('الاسم')),
          DataColumn(label: Text('الرقم الأكاديمي')),
          DataColumn(label: Text('الصف')),
          DataColumn(label: Text('البريد الإلكتروني')),
          DataColumn(label: Text('تاريخ الإنشاء')),
          DataColumn(label: Text('إجراءات')),
        ],
        rows: students.map((student) {
          return DataRow(
            cells: [
              DataCell(Text(student.name)),
              DataCell(Text(student.studentNumber)),
              DataCell(Text(student.studentClass)),
              DataCell(Text(student.email)),
              DataCell(Text(DateFormat('yyyy/MM/dd').format(student.createdAt.toDate()))),
              DataCell(Row(
                children: [
                  IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () => _showStudentFormDialog(context, ref, student: student)),
                  IconButton(
                    icon: const Icon(Icons.delete, color: Colors.red),
                    onPressed: () => _confirmDelete(context, ref, student),
                  ),
                ],
              )),
            ],
          );
        }).toList(),
      ),
    );
  }

  void _showStudentFormDialog(BuildContext context, WidgetRef ref, {StudentModel? student}) {
    showDialog(
      context: context,
      builder: (context) => StudentFormDialog(student: student),
    );
  }

  void _confirmDelete(BuildContext context, WidgetRef ref, StudentModel student) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من رغبتك في حذف الطالب ${student.name}؟'),
        actions: [
          TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              ref.read(firebaseServiceProvider).deleteStudent(student);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
