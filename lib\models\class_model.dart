import 'package:cloud_firestore/cloud_firestore.dart';

/// يمثل هذا الكائن صفاً دراسياً.
class ClassModel {
  final String id;
  final String name;
  final bool isActive;
  final int studentCount;

  ClassModel({
    required this.id,
    required this.name,
    this.isActive = true,
    this.studentCount = 0,
  });

  /// دالة مصنعية لإنشاء كائن ClassModel من مستند Firestore.
  factory ClassModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>? ?? {};
    return ClassModel(
      id: doc.id,
      name: data['name'] as String? ?? 'اسم صف غير محدد',
      isActive: data['isActive'] as bool? ?? true,
      studentCount: (data['students'] as List<dynamic>?)?.length ?? 0,
    );
  }
}
