{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-0a093df7c99e5f0ff93e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-cbe970425f0699bf143b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-5a8a688ceade154b6044.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-cbe970425f0699bf143b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-5a8a688ceade154b6044.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-0a093df7c99e5f0ff93e.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}