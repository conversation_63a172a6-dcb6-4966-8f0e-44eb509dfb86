import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/notification_providers.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class NotificationsScreen extends ConsumerWidget {
  const NotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Watch the state and the controller
    final formState = ref.watch(notificationControllerProvider);
    final formController = ref.read(notificationControllerProvider.notifier);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إرسال إشعارات'),
        automaticallyImplyLeading: false,
      ),
      body: AbsorbPointer(
        absorbing: formState.isLoading,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                initialValue: formState.title,
                onChanged: formController.updateTitle,
                decoration: const InputDecoration(labelText: 'عنوان الإشعار'),
              ),
              const SizedBox(height: 16),
              TextFormField(
                initialValue: formState.message,
                onChanged: formController.updateMessage,
                decoration: const InputDecoration(labelText: 'رسالة الإشعار'),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: formState.targetType,
                decoration: const InputDecoration(labelText: 'إرسال إلى'),
                items: const [
                  DropdownMenuItem(value: 'all', child: Text('الكل')),
                  DropdownMenuItem(value: 'class', child: Text('فصل معين')),
                  DropdownMenuItem(value: 'student', child: Text('طالب معين')),
                ],
                onChanged: formController.selectTargetType,
              ),
              if (formState.targetType == 'class') ...[
                const SizedBox(height: 16),
                _ClassDropdown(),
              ],
              if (formState.targetType == 'student') ...[
                const SizedBox(height: 16),
                _StudentDropdown(),
              ],
              const SizedBox(height: 24),
              Center(
                child: ElevatedButton(
                  onPressed: formState.isLoading
                      ? null
                      : () async {
                          final success = await formController.sendNotification();
                          if (context.mounted) {
                            if (success) {
                              showSuccessSnackBar(context, 'تم إرسال الإشعار بنجاح');
                            } else {
                              showErrorSnackBar(context, 'فشل إرسال الإشعار. الرجاء التأكد من ملء جميع الحقول.');
                            }
                          }
                        },
                  child: formState.isLoading
                      ? const CircularProgressIndicator(color: Colors.white)
                      : const Text('إرسال الإشعار'),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _ClassDropdown extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classesAsyncValue = ref.watch(classesStreamProvider);
    final selectedClassId = ref.watch(notificationControllerProvider).selectedClassId;
    final formController = ref.read(notificationControllerProvider.notifier);

    return classesAsyncValue.when(
      data: (classes) {
        return DropdownButtonFormField<String>(
          value: selectedClassId,
          decoration: const InputDecoration(labelText: 'اختر الفصل'),
          items: classes.map((classModel) {
            return DropdownMenuItem(
              value: classModel.id,
              child: Text(classModel.name),
            );
          }).toList(),
          onChanged: formController.selectClass,
          validator: (v) => v == null ? 'الرجاء اختيار فصل' : null,
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => Text('خطأ: $err'),
    );
  }
}

class _StudentDropdown extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentsAsyncValue = ref.watch(studentsStreamProvider);
    final selectedStudentId = ref.watch(notificationControllerProvider).selectedStudentId;
    final formController = ref.read(notificationControllerProvider.notifier);

    return studentsAsyncValue.when(
      data: (students) {
        return DropdownButtonFormField<String>(
          value: selectedStudentId,
          decoration: const InputDecoration(labelText: 'اختر الطالب'),
          items: students.map((student) {
            return DropdownMenuItem(
              value: student.id,
              child: Text(student.name),
            );
          }).toList(),
          onChanged: formController.selectStudent,
          validator: (v) => v == null ? 'الرجاء اختيار طالب' : null,
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => Text('خطأ: $err'),
    );
  }
}
