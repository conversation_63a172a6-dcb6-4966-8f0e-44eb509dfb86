{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["F:\\baha\\Baha\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\baha\\Baha\\Flutter\\New\\school_management_system\\android\\app\\.cxx\\Debug\\646523pv\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["F:\\baha\\Baha\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "F:\\baha\\Baha\\Flutter\\New\\school_management_system\\android\\app\\.cxx\\Debug\\646523pv\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}