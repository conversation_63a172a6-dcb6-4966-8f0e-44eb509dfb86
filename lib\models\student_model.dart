import 'package:cloud_firestore/cloud_firestore.dart';

/// يمثل هذا الكائن طالباً بجميع خصائصه الأساسية.
class StudentModel {
  final String id; // UID from Firebase Auth
  final String name;
  final String email;
  final String studentNumber;
  final String studentClass; // اسم الصف
  final String? classId; // معرّف الصف
  final Timestamp createdAt;
  final String? imageUrl;
  final String? gender;
  final String? profileImageUrl;
  final String? phoneNumber;
  final DateTime? dateOfBirth;
  final String? address;
  final bool isActive;

  StudentModel({
    required this.id,
    required this.name,
    required this.email,
    required this.studentNumber,
    required this.studentClass,
    this.classId,
    required this.createdAt,
    this.imageUrl,
    this.gender,
    this.profileImageUrl,
    this.phoneNumber,
    this.dateOfBirth,
    this.address,
    this.isActive = true,
  });

  /// دالة مصنعية لإنشاء كائن StudentModel من Map.
  /// تم استخدامها في الكود الأصلي، لذلك سنحتفظ بها مع تحسينها.
  factory StudentModel.fromMap(Map<String, dynamic> data, String documentId) {
    return StudentModel(
      id: documentId,
      name: data['name'] as String? ?? 'اسم غير متوفر',
      email: data['email'] as String? ?? 'بريد إلكتروني غير متوفر',
      studentNumber: data['student_number'] as String? ?? 'رقم غير متوفر',
      studentClass: data['class'] as String? ?? 'صف غير محدد',
      classId: data['classId'] as String?,
      createdAt: data['created_at'] as Timestamp? ?? Timestamp.now(),
      imageUrl: data['imageUrl'] as String?,
      gender: data['gender'] as String?,
      profileImageUrl: data['profileImageUrl'] as String?,
      phoneNumber: data['phoneNumber'] as String?,
      dateOfBirth:
          data['dateOfBirth'] != null
              ? (data['dateOfBirth'] as Timestamp).toDate()
              : null,
      address: data['address'] as String?,
      isActive: data['isActive'] as bool? ?? true,
    );
  }

  /// دالة مصنعية بديلة لإنشاء الكائن من DocumentSnapshot مباشرة.
  factory StudentModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>? ?? {};
    return StudentModel(
      id: doc.id,
      name: data['name'] as String? ?? 'اسم غير متوفر',
      email: data['email'] as String? ?? 'بريد إلكتروني غير متوفر',
      studentNumber: data['student_number'] as String? ?? 'رقم غير متوفر',
      studentClass: data['class'] as String? ?? 'صف غير محدد',
      classId: data['classId'] as String?,
      createdAt: data['created_at'] as Timestamp? ?? Timestamp.now(),
      imageUrl: data['imageUrl'] as String?,
      gender: data['gender'] as String?,
      isActive: data['isActive'] as bool? ?? true,
    );
  }
}
