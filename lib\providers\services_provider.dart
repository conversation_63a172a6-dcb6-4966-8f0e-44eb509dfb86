import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// Provider مركزي لتوفير نسخة وحيدة من [FirebaseService] لجميع أنحاء التطبيق.
///
/// استخدام هذا الـ Provider يضمن أن جميع أجزاء التطبيق تستخدم نفس نسخة الخدمة،
/// ويسهل عملية استبدالها بنسخة وهمية (mock) أثناء الاختبار.
final firebaseServiceProvider = Provider<FirebaseService>((ref) {
  return FirebaseService();
});
