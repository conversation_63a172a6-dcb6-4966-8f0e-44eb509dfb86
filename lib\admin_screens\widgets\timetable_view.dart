import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/session_edit_dialog.dart';
import 'package:school_management_system/models/timetable_model.dart';
import 'package:school_management_system/providers/timetable_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class TimetableView extends ConsumerWidget {
  final String classId;

  const TimetableView({super.key, required this.classId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<String> days = TimetableModel.weekDays;
    final int periods = TimetableModel.periodTimes.length;
    final timetableAsync = ref.watch(timetableStreamProvider(classId));

    return timetableAsync.when(
      data: (timetable) {
        if (timetable == null || !timetable.hasSchedule) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Text('لا يوجد جدول دراسي لهذا الصف بعد.'),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (_) => SessionEditDialog(
                        classId: classId,
                        day: days.first,
                        period: 1,
                        currentSessionData: null,
                      ),
                    );
                  },
                  child: const Text('إضافة حصة جديدة'),
                )
              ],
            ),
          );
        }

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: DataTable(
            border: TableBorder.all(),
            columns: [
              const DataColumn(label: Text('اليوم', style: TextStyle(fontWeight: FontWeight.bold))),
              for (int i = 1; i <= periods; i++)
                DataColumn(label: Text('الحصة $i', style: TextStyle(fontWeight: FontWeight.bold))),
            ],
            rows: days.map((day) {
              return DataRow(
                cells: [
                  DataCell(Text(day, style: const TextStyle(fontWeight: FontWeight.bold))),
                  for (int i = 1; i <= periods; i++)
                    DataCell(
                      _buildSessionCell(context, timetable.getSession(day, i)),
                      onTap: () => showDialog(
                        context: context,
                        builder: (_) => SessionEditDialog(
                          classId: classId,
                          day: day,
                          period: i,
                          currentSessionData: timetable.getSession(day, i)?.toMap(),
                        ),
                      ),
                    ),
                ],
              );
            }).toList(),
          ),
        );
      },
      loading: () => const Center(child: LoadingIndicator()),
      error: (err, stack) => Center(child: Text('خطأ في تحميل الجدول: $err')),
    );
  }

  Widget _buildSessionCell(BuildContext context, TimetableSession? session) {
    if (session == null || !session.isValid) {
      return const Center(child: Text('-'));
    }

    return Container(
      padding: const EdgeInsets.all(8.0),
      color: session.defaultSubjectColor.withOpacity(0.1),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            session.subject,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: session.defaultSubjectColor,
            ),
            textAlign: TextAlign.center,
          ),
          if (session.teacherName != null)
            Text(
              '(${session.teacherName})',
              style: const TextStyle(fontSize: 12, color: Colors.black54),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}
