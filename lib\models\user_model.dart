
/// نموذج البيانات الأساسي للمستخدم
/// يحتوي على الخصائص المشتركة بين جميع أنواع المستخدمين
class UserModel {
  final String id; // المعرّف الفريد للمستخدم (من Firebase Auth)
  final String name; // الاسم الكامل
  final String email; // البريد الإلكتروني
  final String role; // الدور (مثال: 'student', 'admin', 'teacher', 'staff')

  // --- حقول إضافية خاصة بالموظفين ---
  final String? jobTitle; // المسمى الوظيفي (مثال: معلم رياضيات)
  final String? bio; // نبذة تعريفية
  final String? profileImageUrl; // رابط الصورة الشخصية

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.role,
    this.jobTitle,
    this.bio,
    this.profileImageUrl,
  });

  /// دالة لتحويل بيانات المستخدم من Firestore (Map) إلى كائن UserModel
  factory UserModel.fromMap(Map<String, dynamic> data, String documentId) {
    return UserModel(
      id: documentId,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? '',
      jobTitle: data['jobTitle'], // سيكون null إذا لم يكن موجوداً
      bio: data['bio'], // سيكون null إذا لم يكن موجوداً
      profileImageUrl: data['profileImageUrl'], // سيكون null إذا لم يكن موجوداً
    );
  }

  /// دالة لتحويل كائن UserModel إلى Map لتخزينه في Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'role': role,
      'jobTitle': jobTitle,
      'bio': bio,
      'profileImageUrl': profileImageUrl,
    };
  }
}
