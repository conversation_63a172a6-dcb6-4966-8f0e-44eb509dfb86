import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/conversation_model.dart';

/// نموذج الرسالة المتقدم
///
/// يمثل رسالة واحدة داخل محادثة
/// يدعم النصوص والملفات المرفقة والردود والتفاعلات
///
/// الميزات المتقدمة:
/// - دعم الملفات المرفقة (صور، مستندات، صوت)
/// - تتبع حالة القراءة والتسليم
/// - الردود على رسائل محددة
/// - التفاعلات (إعجاب، عدم إعجاب)
/// - تحرير وحذف الرسائل
/// - الرسائل المجدولة
/// - التشفير والأمان
class MessageModel {
  /// معرف الرسالة الفريد
  final String id;

  /// معرف المحادثة التي تنتمي إليها الرسالة
  final String conversationId;

  /// معرف مرسل الرسالة
  final String senderId;

  /// اسم مرسل الرسالة
  final String senderName;

  /// دور مرسل الرسالة
  final ParticipantRole senderRole;

  /// نص الرسالة الأساسي
  final String content;

  /// نوع الرسالة (نص، صورة، ملف، صوت، فيديو)
  final MessageType type;

  /// حالة الرسالة (مرسلة، مستلمة، مقروءة، فشل)
  final MessageStatus status;

  /// تاريخ إرسال الرسالة
  final DateTime sentAt;

  /// تاريخ تسليم الرسالة
  final DateTime? deliveredAt;

  /// تاريخ قراءة الرسالة
  final DateTime? readAt;

  /// معرف الرسالة المرد عليها (إن وجدت)
  final String? replyToMessageId;

  /// نص الرسالة المرد عليها (للعرض السريع)
  final String? replyToContent;

  /// معرف مرسل الرسالة المرد عليها
  final String? replyToSenderId;

  /// قائمة الملفات المرفقة
  final List<MessageAttachment> attachments;

  /// قائمة المستلمين وحالة القراءة لكل منهم
  final Map<String, MessageReadStatus> readStatus;

  /// التفاعلات مع الرسالة (إعجاب، عدم إعجاب)
  final Map<String, MessageReaction> reactions;

  /// هل الرسالة محررة؟
  final bool isEdited;

  /// تاريخ آخر تحرير
  final DateTime? editedAt;

  /// هل الرسالة محذوفة؟
  final bool isDeleted;

  /// تاريخ الحذف
  final DateTime? deletedAt;

  /// معرف من قام بالحذف
  final String? deletedBy;

  /// هل الرسالة مهمة؟
  final bool isImportant;

  /// هل الرسالة مثبتة؟
  final bool isPinned;

  /// أولوية الرسالة
  final MessagePriority priority;

  /// العلامات والتصنيفات
  final List<String> tags;

  /// معلومات إضافية (JSON)
  final Map<String, dynamic> metadata;

  const MessageModel({
    required this.id,
    required this.conversationId,
    required this.senderId,
    required this.senderName,
    required this.senderRole,
    required this.content,
    required this.type,
    required this.status,
    required this.sentAt,
    this.deliveredAt,
    this.readAt,
    this.replyToMessageId,
    this.replyToContent,
    this.replyToSenderId,
    this.attachments = const [],
    this.readStatus = const {},
    this.reactions = const {},
    this.isEdited = false,
    this.editedAt,
    this.isDeleted = false,
    this.deletedAt,
    this.deletedBy,
    this.isImportant = false,
    this.isPinned = false,
    this.priority = MessagePriority.normal,
    this.tags = const [],
    this.metadata = const {},
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory MessageModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return MessageModel(
      id: doc.id,
      conversationId: data['conversationId'] ?? '',
      senderId: data['senderId'] ?? '',
      senderName: data['senderName'] ?? '',
      senderRole: ParticipantRole.values.firstWhere(
        (e) => e.toString() == data['senderRole'],
        orElse: () => ParticipantRole.parent,
      ),
      content: data['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => MessageType.text,
      ),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => MessageStatus.sent,
      ),
      sentAt: (data['sentAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      deliveredAt: (data['deliveredAt'] as Timestamp?)?.toDate(),
      readAt: (data['readAt'] as Timestamp?)?.toDate(),
      replyToMessageId: data['replyToMessageId'],
      replyToContent: data['replyToContent'],
      replyToSenderId: data['replyToSenderId'],
      attachments:
          (data['attachments'] as List<dynamic>? ?? [])
              .map((e) => MessageAttachment.fromMap(e as Map<String, dynamic>))
              .toList(),
      readStatus: (data['readStatus'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(
          key,
          MessageReadStatus.fromMap(value as Map<String, dynamic>),
        ),
      ),
      reactions: (data['reactions'] as Map<String, dynamic>? ?? {}).map(
        (key, value) => MapEntry(
          key,
          MessageReaction.fromMap(value as Map<String, dynamic>),
        ),
      ),
      isEdited: data['isEdited'] ?? false,
      editedAt: (data['editedAt'] as Timestamp?)?.toDate(),
      isDeleted: data['isDeleted'] ?? false,
      deletedAt: (data['deletedAt'] as Timestamp?)?.toDate(),
      deletedBy: data['deletedBy'],
      isImportant: data['isImportant'] ?? false,
      isPinned: data['isPinned'] ?? false,
      priority: MessagePriority.values.firstWhere(
        (e) => e.toString() == data['priority'],
        orElse: () => MessagePriority.normal,
      ),
      tags: List<String>.from(data['tags'] ?? []),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// تحويل النموذج إلى خريطة للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'conversationId': conversationId,
      'senderId': senderId,
      'senderName': senderName,
      'senderRole': senderRole.toString(),
      'content': content,
      'type': type.toString(),
      'status': status.toString(),
      'sentAt': Timestamp.fromDate(sentAt),
      'deliveredAt':
          deliveredAt != null ? Timestamp.fromDate(deliveredAt!) : null,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
      'replyToMessageId': replyToMessageId,
      'replyToContent': replyToContent,
      'replyToSenderId': replyToSenderId,
      'attachments': attachments.map((e) => e.toMap()).toList(),
      'readStatus': readStatus.map(
        (key, value) => MapEntry(key, value.toMap()),
      ),
      'reactions': reactions.map((key, value) => MapEntry(key, value.toMap())),
      'isEdited': isEdited,
      'editedAt': editedAt != null ? Timestamp.fromDate(editedAt!) : null,
      'isDeleted': isDeleted,
      'deletedAt': deletedAt != null ? Timestamp.fromDate(deletedAt!) : null,
      'deletedBy': deletedBy,
      'isImportant': isImportant,
      'isPinned': isPinned,
      'priority': priority.toString(),
      'tags': tags,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من الرسالة
  MessageModel copyWith({
    String? content,
    MessageStatus? status,
    DateTime? deliveredAt,
    DateTime? readAt,
    List<MessageAttachment>? attachments,
    Map<String, MessageReadStatus>? readStatus,
    Map<String, MessageReaction>? reactions,
    bool? isEdited,
    DateTime? editedAt,
    bool? isDeleted,
    DateTime? deletedAt,
    String? deletedBy,
    bool? isImportant,
    bool? isPinned,
    MessagePriority? priority,
    List<String>? tags,
    Map<String, dynamic>? metadata,
  }) {
    return MessageModel(
      id: id,
      conversationId: conversationId,
      senderId: senderId,
      senderName: senderName,
      senderRole: senderRole,
      content: content ?? this.content,
      type: type,
      status: status ?? this.status,
      sentAt: sentAt,
      deliveredAt: deliveredAt ?? this.deliveredAt,
      readAt: readAt ?? this.readAt,
      replyToMessageId: replyToMessageId,
      replyToContent: replyToContent,
      replyToSenderId: replyToSenderId,
      attachments: attachments ?? this.attachments,
      readStatus: readStatus ?? this.readStatus,
      reactions: reactions ?? this.reactions,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      isDeleted: isDeleted ?? this.isDeleted,
      deletedAt: deletedAt ?? this.deletedAt,
      deletedBy: deletedBy ?? this.deletedBy,
      isImportant: isImportant ?? this.isImportant,
      isPinned: isPinned ?? this.isPinned,
      priority: priority ?? this.priority,
      tags: tags ?? this.tags,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// مرفق الرسالة
class MessageAttachment {
  final String id;
  final String name;
  final String url;
  final AttachmentType type;
  final int size;
  final String? mimeType;
  final String? thumbnailUrl;

  const MessageAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    this.mimeType,
    this.thumbnailUrl,
  });

  factory MessageAttachment.fromMap(Map<String, dynamic> map) {
    return MessageAttachment(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      type: AttachmentType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => AttachmentType.file,
      ),
      size: map['size'] ?? 0,
      mimeType: map['mimeType'],
      thumbnailUrl: map['thumbnailUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type.toString(),
      'size': size,
      'mimeType': mimeType,
      'thumbnailUrl': thumbnailUrl,
    };
  }
}

/// حالة قراءة الرسالة
class MessageReadStatus {
  final String userId;
  final bool isRead;
  final DateTime? readAt;

  const MessageReadStatus({
    required this.userId,
    required this.isRead,
    this.readAt,
  });

  factory MessageReadStatus.fromMap(Map<String, dynamic> map) {
    return MessageReadStatus(
      userId: map['userId'] ?? '',
      isRead: map['isRead'] ?? false,
      readAt: (map['readAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'isRead': isRead,
      'readAt': readAt != null ? Timestamp.fromDate(readAt!) : null,
    };
  }
}

/// تفاعل مع الرسالة
class MessageReaction {
  final String userId;
  final ReactionType type;
  final DateTime reactedAt;

  const MessageReaction({
    required this.userId,
    required this.type,
    required this.reactedAt,
  });

  factory MessageReaction.fromMap(Map<String, dynamic> map) {
    return MessageReaction(
      userId: map['userId'] ?? '',
      type: ReactionType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ReactionType.like,
      ),
      reactedAt: (map['reactedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'userId': userId,
      'type': type.toString(),
      'reactedAt': Timestamp.fromDate(reactedAt),
    };
  }
}

/// أنواع الرسائل
enum MessageType {
  text, // نص
  image, // صورة
  file, // ملف
  audio, // صوت
  video, // فيديو
  location, // موقع
  contact, // جهة اتصال
  system, // رسالة نظام
}

/// حالات الرسائل
enum MessageStatus {
  sending, // جاري الإرسال
  sent, // مرسلة
  delivered, // مستلمة
  read, // مقروءة
  failed, // فشل الإرسال
}

/// أولويات الرسائل
enum MessagePriority {
  low, // منخفضة
  normal, // عادية
  high, // مهمة
  urgent, // عاجلة
}

/// أنواع المرفقات
enum AttachmentType {
  image, // صورة
  document, // مستند
  audio, // صوت
  video, // فيديو
  file, // ملف عام
}

/// أنواع التفاعلات
enum ReactionType {
  like, // إعجاب
  dislike, // عدم إعجاب
  love, // حب
  laugh, // ضحك
  angry, // غضب
  sad, // حزن
}
