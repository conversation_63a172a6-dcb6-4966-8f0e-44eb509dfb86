import 'package:flutter/material.dart';
import 'package:school_management_system/teacher_screens/teacher_home_page.dart';
import 'package:school_management_system/teacher_screens/exam_syllabus_management_screen.dart';
import 'package:school_management_system/teacher_screens/grade_entry_screen.dart';
import 'package:school_management_system/teacher_screens/teacher_exam_schedule_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// لوحة التحكم الرئيسية للمعلمين
/// 
/// هذه الشاشة تعمل كلوحة تحكم رئيسية للمعلمين وتحتوي على
/// جميع الوظائف والشاشات المخصصة للمعلمين
/// 
/// الوظائف الرئيسية:
/// - الصفحة الرئيسية للمعلم
/// - إدارة مناهج الامتحانات
/// - إدخال الدرجات
/// - جدول امتحانات المعلم
/// - إدارة الصفوف والطلاب
/// - التقارير والإحصائيات
/// 
/// التصميم:
/// - تصميم متجاوب يعمل على الويب والجوال
/// - قائمة تنقل جانبية للشاشات الكبيرة
/// - قائمة منسدلة للشاشات الصغيرة
/// - ألوان مميزة للمعلمين (أخضر)
class TeacherMainLayout extends StatefulWidget {
  const TeacherMainLayout({super.key});

  @override
  State<TeacherMainLayout> createState() => _TeacherMainLayoutState();
}

class _TeacherMainLayoutState extends State<TeacherMainLayout> {
  
  /// فهرس الصفحة المحددة حالياً
  int _selectedIndex = 0;

  /// قائمة الصفحات التي يمكن للمعلم التنقل بينها
  final List<Widget> _pages = [
    const TeacherHomePage(), // 0: الصفحة الرئيسية للمعلم
    const ExamSyllabusManagementScreen(), // 1: إدارة مناهج الامتحانات
    const GradeEntryScreen(), // 2: إدخال الدرجات
    const TeacherExamScheduleScreen(), // 3: جدول امتحانات المعلم
    // يمكن إضافة المزيد من الشاشات هنا
  ];

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 1000;

        return Scaffold(
          appBar: AppBar(
            title: const Text(
              'لوحة تحكم المعلم',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            backgroundColor: Colors.green[800],
            elevation: 2,
            actions: [
              // زر الإشعارات
              IconButton(
                icon: const Icon(Icons.notifications, color: Colors.white),
                tooltip: 'الإشعارات',
                onPressed: () {
                  // TODO: فتح شاشة الإشعارات
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('سيتم تطبيق الإشعارات قريباً'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
              ),
              
              // زر تسجيل الخروج
              IconButton(
                icon: const Icon(Icons.logout, color: Colors.white),
                tooltip: 'تسجيل الخروج',
                onPressed: () {
                  _showLogoutDialog();
                },
              ),
            ],
            leading: isSmallScreen
                ? Builder(
                    builder: (context) => IconButton(
                      icon: const Icon(Icons.menu, color: Colors.white),
                      onPressed: () => Scaffold.of(context).openDrawer(),
                    ),
                  )
                : null,
          ),
          
          // قائمة منسدلة للشاشات الصغيرة
          drawer: isSmallScreen ? _buildDrawer() : null,
          
          // محتوى الشاشة
          body: Row(
            children: [
              // قائمة التنقل الجانبية للشاشات الكبيرة
              if (!isSmallScreen)
                SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    child: IntrinsicHeight(
                      child: NavigationRail(
                        selectedIndex: _selectedIndex,
                        onDestinationSelected: (int index) {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        labelType: NavigationRailLabelType.all,
                        backgroundColor: Colors.green[50],
                        selectedIconTheme: IconThemeData(
                          color: Colors.green[800],
                        ),
                        selectedLabelTextStyle: TextStyle(
                          color: Colors.green[800],
                          fontWeight: FontWeight.bold,
                        ),
                        destinations: _buildNavDestinations(),
                      ),
                    ),
                  ),
                ),
              
              // خط فاصل
              if (!isSmallScreen) 
                VerticalDivider(
                  thickness: 1, 
                  width: 1,
                  color: Colors.green[200],
                ),
              
              // محتوى الصفحة المحددة
              Expanded(child: _pages[_selectedIndex]),
            ],
          ),
        );
      },
    );
  }

  /// بناء وجهات التنقل للقائمة الجانبية
  List<NavigationRailDestination> _buildNavDestinations() {
    return [
      // الصفحة الرئيسية
      const NavigationRailDestination(
        icon: Icon(Icons.home_outlined),
        selectedIcon: Icon(Icons.home),
        label: Text('الرئيسية'),
      ),
      
      // إدارة مناهج الامتحانات
      const NavigationRailDestination(
        icon: Icon(Icons.book_outlined),
        selectedIcon: Icon(Icons.book),
        label: Text('مناهج الامتحانات'),
      ),
      
      // إدخال الدرجات
      const NavigationRailDestination(
        icon: Icon(Icons.grade_outlined),
        selectedIcon: Icon(Icons.grade),
        label: Text('إدخال الدرجات'),
      ),
      
      // جدول امتحانات المعلم
      const NavigationRailDestination(
        icon: Icon(Icons.schedule_outlined),
        selectedIcon: Icon(Icons.schedule),
        label: Text('جدول امتحاناتي'),
      ),
    ];
  }

  /// بناء القائمة المنسدلة للشاشات الصغيرة
  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          // رأس القائمة
          DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.green[800],
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.school,
                  color: Colors.white,
                  size: 48,
                ),
                SizedBox(height: 8),
                Text(
                  'لوحة تحكم المعلم',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'نظام إدارة المدرسة',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // عناصر القائمة
          _buildDrawerItem(
            icon: Icons.home,
            title: 'الصفحة الرئيسية',
            index: 0,
          ),
          _buildDrawerItem(
            icon: Icons.book,
            title: 'مناهج الامتحانات',
            index: 1,
          ),
          _buildDrawerItem(
            icon: Icons.grade,
            title: 'إدخال الدرجات',
            index: 2,
          ),
          _buildDrawerItem(
            icon: Icons.schedule,
            title: 'جدول امتحاناتي',
            index: 3,
          ),
          
          const Divider(),
          
          // الإشعارات
          ListTile(
            leading: const Icon(Icons.notifications_outlined),
            title: const Text('الإشعارات'),
            onTap: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('سيتم تطبيق الإشعارات قريباً'),
                  backgroundColor: Colors.blue,
                ),
              );
            },
          ),
          
          // تسجيل الخروج
          ListTile(
            leading: const Icon(Icons.logout),
            title: const Text('تسجيل الخروج'),
            onTap: () {
              Navigator.pop(context);
              _showLogoutDialog();
            },
          ),
        ],
      ),
    );
  }

  /// بناء عنصر واحد في القائمة المنسدلة
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required int index,
  }) {
    final isSelected = _selectedIndex == index;
    
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected ? Colors.green[800] : null,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected ? Colors.green[800] : null,
          fontWeight: isSelected ? FontWeight.bold : null,
        ),
      ),
      selected: isSelected,
      selectedTileColor: Colors.green[50],
      onTap: () {
        setState(() {
          _selectedIndex = index;
        });
        Navigator.pop(context); // إغلاق القائمة المنسدلة
      },
    );
  }

  /// عرض حوار تأكيد تسجيل الخروج
  void _showLogoutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تسجيل الخروج'),
        content: const Text('هل أنت متأكد من تسجيل الخروج؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              FirebaseService().signOut();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text(
              'تسجيل الخروج',
              style: TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }
}
