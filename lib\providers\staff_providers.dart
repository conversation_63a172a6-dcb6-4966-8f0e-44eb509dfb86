import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة الموظفين (المعلمين والإداريين) كـ Stream.
final staffStreamProvider = StreamProvider.autoDispose<List<UserModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // ملاحظة: هذا يتطلب تعديل getCollection أو إنشاء دالة مخصصة في FirebaseService
  // للتعامل مع where queries. للتبسيط الآن، سأفترض وجود دالة كهذه.
  // في الواقع، يجب تعديل FirebaseService لإضافة دالة getStaff().
  // لكن لأغراض إعادة الهيكلة، سنستخدم getCollection ونقوم بالفلترة لاحقًا.
  return firebaseService.getCollection(
    path: 'users',
    builder: (data, documentId) => UserModel.fromMap(data, documentId),
  ).map((users) => users.where((user) => user.role == 'teacher' || user.role == 'staff').toList());
});

/// Provider لتخزين نص البحث الحالي للموظفين.
final staffSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لفلترة قائمة الموظفين بناءً على نص البحث.
///
/// يعتمد على [staffStreamProvider] و [staffSearchQueryProvider].
/// يتم إعادة حسابه تلقائيًا عند تغيير أي منهما.
final filteredStaffProvider = Provider.autoDispose<List<UserModel>>((ref) {
  final staffAsyncValue = ref.watch(staffStreamProvider);
  final searchQuery = ref.watch(staffSearchQueryProvider);

  // في حالة عدم وجود بيانات أو وجود خطأ، أرجع قائمة فارغة.
  if (!staffAsyncValue.hasValue) {
    return [];
  }

  final staffList = staffAsyncValue.value!;

  if (searchQuery.isEmpty) {
    return staffList;
  }

  return staffList.where((user) {
    final nameLower = user.name.toLowerCase();
    final emailLower = user.email.toLowerCase();
    final queryLower = searchQuery.toLowerCase();
    return nameLower.contains(queryLower) || emailLower.contains(queryLower);
  }).toList();
});

/// Provider لجلب قائمة الموظفين العامة (لشاشة الموبايل).
final publicStaffStreamProvider = StreamProvider.autoDispose<List<UserModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getUsersByRoleStream(['teacher', 'staff']);
});
