import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/exam_schedule_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة إدارة جداول الامتحانات
///
/// هذه الشاشة تسمح للإدارة بإنشاء وإدارة جداول الامتحانات
/// وتشمل جدولة المواعيد وتخصيص القاعات والمراقبين
///
/// الوظائف الرئيسية:
/// - إنشاء جدول امتحان جديد
/// - تحديد المواعيد والأوقات لكل مادة
/// - تخصيص القاعات والمراقبين
/// - تجنب التعارض في الجداول
/// - طباعة ونشر الجداول
class ExamSchedulesManagementScreen extends ConsumerStatefulWidget {
  const ExamSchedulesManagementScreen({super.key});

  @override
  ConsumerState<ExamSchedulesManagementScreen> createState() =>
      _ExamSchedulesManagementScreenState();
}

class _ExamSchedulesManagementScreenState
    extends ConsumerState<ExamSchedulesManagementScreen>
    with TickerProviderStateMixin {
  // متحكمات التبويبات
  late TabController _tabController;

  // متحكمات النماذج
  final _formKey = GlobalKey<FormState>();
  final _examNameController = TextEditingController();
  final _searchController = TextEditingController();

  // متغيرات الحالة
  String _selectedAcademicYear = '2023-2024';
  String _selectedSemester = 'الفصل الأول';
  ExamType _selectedExamType = ExamType.monthly;
  DateTime _selectedStartDate = DateTime.now();
  DateTime _selectedEndDate = DateTime.now().add(const Duration(days: 7));
  List<String> _selectedClassIds = [];

  @override
  void initState() {
    super.initState();
    // إنشاء متحكم التبويبات مع 3 تبويبات
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _examNameController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'إدارة جداول الامتحانات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.add_circle_outline), text: 'إنشاء جدول جديد'),
            Tab(icon: Icon(Icons.calendar_today), text: 'الجداول الحالية'),
            Tab(icon: Icon(Icons.history), text: 'الجداول السابقة'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في الجداول',
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () => _showScheduleSettings(),
            tooltip: 'إعدادات الجدولة',
          ),
        ],
      ),

      // محتوى التبويبات
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب إنشاء جدول جديد
          _buildCreateScheduleTab(),

          // تبويب الجداول الحالية
          _buildCurrentSchedulesTab(),

          // تبويب الجداول السابقة
          _buildPastSchedulesTab(),
        ],
      ),
    );
  }

  /// بناء تبويب إنشاء جدول جديد
  ///
  /// يحتوي على نموذج لإدخال بيانات الامتحان الجديد
  /// مع خطوات متدرجة لإنشاء الجدول
  Widget _buildCreateScheduleTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            const Text(
              'إنشاء جدول امتحان جديد',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'املأ البيانات التالية لإنشاء جدول امتحان جديد',
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
            const SizedBox(height: 24),

            // قسم المعلومات الأساسية
            _buildBasicInfoSection(),

            const SizedBox(height: 24),

            // قسم التواريخ والأوقات
            _buildDateTimeSection(),

            const SizedBox(height: 24),

            // قسم اختيار الصفوف
            _buildClassSelectionSection(),

            const SizedBox(height: 32),

            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم المعلومات الأساسية
  ///
  /// يحتوي على حقول اسم الامتحان ونوعه والسنة الدراسية
  Widget _buildBasicInfoSection() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  'المعلومات الأساسية',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // حقل اسم الامتحان
            TextFormField(
              controller: _examNameController,
              decoration: const InputDecoration(
                labelText: 'اسم الامتحان *',
                hintText: 'مثال: امتحان الفصل الأول 2024',
                prefixIcon: Icon(Icons.edit),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال اسم الامتحان';
                }
                if (value.trim().length < 5) {
                  return 'اسم الامتحان يجب أن يكون 5 أحرف على الأقل';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // اختيار نوع الامتحان
            DropdownButtonFormField<ExamType>(
              value: _selectedExamType,
              decoration: const InputDecoration(
                labelText: 'نوع الامتحان *',
                prefixIcon: Icon(Icons.category),
                border: OutlineInputBorder(),
              ),
              items:
                  ExamType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(type.arabicName),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedExamType = value;
                  });
                }
              },
            ),
            const SizedBox(height: 16),

            // صف للسنة الدراسية والفصل
            Row(
              children: [
                // السنة الدراسية
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedAcademicYear,
                    decoration: const InputDecoration(
                      labelText: 'السنة الدراسية *',
                      prefixIcon: Icon(Icons.date_range),
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['2023-2024', '2024-2025', '2025-2026'].map((year) {
                          return DropdownMenuItem(
                            value: year,
                            child: Text(year),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedAcademicYear = value;
                        });
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),

                // الفصل الدراسي
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: _selectedSemester,
                    decoration: const InputDecoration(
                      labelText: 'الفصل الدراسي *',
                      prefixIcon: Icon(Icons.school),
                      border: OutlineInputBorder(),
                    ),
                    items:
                        ['الفصل الأول', 'الفصل الثاني', 'الفصل الصيفي'].map((
                          semester,
                        ) {
                          return DropdownMenuItem(
                            value: semester,
                            child: Text(semester),
                          );
                        }).toList(),
                    onChanged: (value) {
                      if (value != null) {
                        setState(() {
                          _selectedSemester = value;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التواريخ والأوقات
  ///
  /// يحتوي على اختيار تاريخ البداية والنهاية للامتحانات
  Widget _buildDateTimeSection() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(Icons.access_time, color: Colors.green[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  'التواريخ والأوقات',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // صف لتاريخ البداية والنهاية
            Row(
              children: [
                // تاريخ البداية
                Expanded(
                  child: InkWell(
                    onTap: () => _selectStartDate(),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[400]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تاريخ البداية *',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.blue[600],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_selectedStartDate.day}/${_selectedStartDate.month}/${_selectedStartDate.year}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),

                // تاريخ النهاية
                Expanded(
                  child: InkWell(
                    onTap: () => _selectEndDate(),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[400]!),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'تاريخ النهاية *',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              Icon(
                                Icons.calendar_today,
                                size: 16,
                                color: Colors.red[600],
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '${_selectedEndDate.day}/${_selectedEndDate.month}/${_selectedEndDate.year}',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // معلومات إضافية عن المدة
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, color: Colors.blue[600], size: 16),
                  const SizedBox(width: 8),
                  Text(
                    'مدة الامتحانات: ${_selectedEndDate.difference(_selectedStartDate).inDays + 1} يوم',
                    style: TextStyle(
                      color: Colors.blue[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم اختيار الصفوف
  ///
  /// يعرض قائمة بالصفوف المتاحة للاختيار من بينها
  Widget _buildClassSelectionSection() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // عنوان القسم
            Row(
              children: [
                Icon(Icons.class_, color: Colors.purple[600], size: 20),
                const SizedBox(width: 8),
                const Text(
                  'اختيار الصفوف المشاركة',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'اختر الصفوف التي ستشارك في هذا الامتحان',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
            const SizedBox(height: 16),

            // قائمة الصفوف المتاحة (مؤقتة - يجب جلبها من قاعدة البيانات)
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  [
                    'الصف الأول',
                    'الصف الثاني',
                    'الصف الثالث',
                    'الصف الرابع',
                    'الصف الخامس',
                    'الصف السادس',
                    'الصف السابع',
                    'الصف الثامن',
                    'الصف التاسع',
                  ].map((className) {
                    final isSelected = _selectedClassIds.contains(className);
                    return FilterChip(
                      label: Text(className),
                      selected: isSelected,
                      onSelected: (selected) {
                        setState(() {
                          if (selected) {
                            _selectedClassIds.add(className);
                          } else {
                            _selectedClassIds.remove(className);
                          }
                        });
                      },
                      selectedColor: Colors.blue[100],
                      checkmarkColor: Colors.blue[700],
                    );
                  }).toList(),
            ),

            // معلومات عن الصفوف المختارة
            if (_selectedClassIds.isNotEmpty) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.green[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      color: Colors.green[600],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تم اختيار ${_selectedClassIds.length} صف للمشاركة',
                      style: TextStyle(
                        color: Colors.green[700],
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  ///
  /// يحتوي على أزرار الحفظ والإلغاء والمعاينة
  Widget _buildActionButtons() {
    return Row(
      children: [
        // زر الإلغاء
        Expanded(
          child: OutlinedButton(
            onPressed: () => _resetForm(),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.grey[400]!),
            ),
            child: const Text(
              'إلغاء',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(width: 12),

        // زر المعاينة
        Expanded(
          child: OutlinedButton(
            onPressed:
                _selectedClassIds.isNotEmpty ? () => _previewSchedule() : null,
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 16),
              side: BorderSide(color: Colors.blue[400]!),
            ),
            child: const Text(
              'معاينة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        ),
        const SizedBox(width: 12),

        // زر الحفظ
        Expanded(
          flex: 2,
          child: ElevatedButton(
            onPressed:
                _selectedClassIds.isNotEmpty ? () => _createSchedule() : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child: const Text(
              'إنشاء الجدول',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// بناء تبويب الجداول الحالية
  ///
  /// يعرض قائمة بالجداول المجدولة حالياً
  Widget _buildCurrentSchedulesTab() {
    final upcomingExamsAsync = ref.watch(upcomingExamsStreamProvider);

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(upcomingExamsStreamProvider);
      },
      child: upcomingExamsAsync.when(
        loading: () => const Center(child: LoadingIndicator()),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل الجداول',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.red[700],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    error.toString(),
                    style: TextStyle(color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
        data: (exams) {
          if (exams.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.calendar_today_outlined,
                    size: 64,
                    color: Colors.grey[400],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد جداول مجدولة حالياً',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'يمكنك إنشاء جدول جديد من التبويب الأول',
                    style: TextStyle(color: Colors.grey[500]),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: exams.length,
            itemBuilder: (context, index) {
              final exam = exams[index];
              return _buildScheduleCard(exam, isUpcoming: true);
            },
          );
        },
      ),
    );
  }

  /// بناء تبويب الجداول السابقة
  ///
  /// يعرض قائمة بالجداول المكتملة سابقاً
  Widget _buildPastSchedulesTab() {
    final allExamsAsync = ref.watch(allExamsStreamProvider);

    return RefreshIndicator(
      onRefresh: () async {
        ref.invalidate(allExamsStreamProvider);
      },
      child: allExamsAsync.when(
        loading: () => const Center(child: LoadingIndicator()),
        error:
            (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    'خطأ في تحميل الجداول السابقة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.red[700],
                    ),
                  ),
                ],
              ),
            ),
        data: (exams) {
          // تصفية الامتحانات المكتملة فقط
          final completedExams =
              exams.where((exam) => exam.isCompleted).toList();

          if (completedExams.isEmpty) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.history, size: 64, color: Colors.grey[400]),
                  const SizedBox(height: 16),
                  Text(
                    'لا توجد جداول سابقة',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: completedExams.length,
            itemBuilder: (context, index) {
              final exam = completedExams[index];
              return _buildScheduleCard(exam, isUpcoming: false);
            },
          );
        },
      ),
    );
  }

  /// بناء بطاقة جدول امتحان
  ///
  /// تعرض معلومات الجدول في شكل بطاقة أنيقة
  Widget _buildScheduleCard(ExamModel exam, {required bool isUpcoming}) {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة مع اسم الامتحان وحالته
            Row(
              children: [
                Expanded(
                  child: Text(
                    exam.name,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: isUpcoming ? Colors.blue[100] : Colors.green[100],
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    isUpcoming ? 'قادم' : 'مكتمل',
                    style: TextStyle(
                      color: isUpcoming ? Colors.blue[700] : Colors.green[700],
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // معلومات الامتحان
            Row(
              children: [
                Icon(Icons.category, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  exam.type.arabicName,
                  style: TextStyle(color: Colors.grey[700], fontSize: 14),
                ),
                const SizedBox(width: 16),
                Icon(Icons.school, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  exam.semester,
                  style: TextStyle(color: Colors.grey[700], fontSize: 14),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // التواريخ
            Row(
              children: [
                Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 8),
                Text(
                  'من ${exam.startDate.day}/${exam.startDate.month} إلى ${exam.endDate.day}/${exam.endDate.month}',
                  style: TextStyle(color: Colors.grey[700], fontSize: 14),
                ),
                const Spacer(),
                Text(
                  '${exam.classIds.length} صف',
                  style: TextStyle(
                    color: Colors.blue[600],
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // أزرار الإجراءات
            Row(
              children: [
                // زر عرض التفاصيل
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _viewScheduleDetails(exam.id),
                    icon: const Icon(Icons.visibility, size: 16),
                    label: const Text('عرض التفاصيل'),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // زر التعديل (للجداول القادمة فقط)
                if (isUpcoming) ...[
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _editSchedule(exam.id),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('تعديل'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue[600],
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ] else ...[
                  // زر طباعة التقرير (للجداول المكتملة)
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _printScheduleReport(exam.id),
                      icon: const Icon(Icons.print, size: 16),
                      label: const Text('طباعة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green[600],
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// اختيار تاريخ البداية
  Future<void> _selectStartDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedStartDate,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedStartDate) {
      setState(() {
        _selectedStartDate = picked;
        // التأكد من أن تاريخ النهاية بعد تاريخ البداية
        if (_selectedEndDate.isBefore(_selectedStartDate)) {
          _selectedEndDate = _selectedStartDate.add(const Duration(days: 7));
        }
      });
    }
  }

  /// اختيار تاريخ النهاية
  Future<void> _selectEndDate() async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _selectedEndDate,
      firstDate: _selectedStartDate,
      lastDate: _selectedStartDate.add(const Duration(days: 30)),
      locale: const Locale('ar'),
    );

    if (picked != null && picked != _selectedEndDate) {
      setState(() {
        _selectedEndDate = picked;
      });
    }
  }

  /// إعادة تعيين النموذج
  void _resetForm() {
    setState(() {
      _examNameController.clear();
      _selectedExamType = ExamType.monthly;
      _selectedAcademicYear = '2023-2024';
      _selectedSemester = 'الفصل الأول';
      _selectedStartDate = DateTime.now();
      _selectedEndDate = DateTime.now().add(const Duration(days: 7));
      _selectedClassIds.clear();
    });
  }

  /// معاينة الجدول قبل الحفظ
  void _previewSchedule() {
    // TODO: تطبيق شاشة معاينة الجدول
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق معاينة الجدول قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// إنشاء الجدول الجديد
  Future<void> _createSchedule() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedClassIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار صف واحد على الأقل'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    // إنشاء نموذج الامتحان الجديد
    final exam = ExamModel(
      id: '', // سيتم تعيينه من Firebase
      name: _examNameController.text.trim(),
      academicYear: _selectedAcademicYear,
      semester: _selectedSemester,
      type: _selectedExamType,
      startDate: _selectedStartDate,
      endDate: _selectedEndDate,
      classIds: _selectedClassIds,
      status: ExamStatus.scheduled,
      description: 'تم إنشاؤه من شاشة إدارة الجداول',
      createdAt: DateTime.now(),
      createdBy: 'current_user_id', // TODO: جلب من المصادقة
    );

    try {
      // إنشاء الامتحان باستخدام المزود
      await ref.read(examCreationStateProvider.notifier).createExam(exam);

      // التحقق من نجاح العملية
      final creationState = ref.read(examCreationStateProvider);
      if (creationState.createdExamId != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إنشاء الجدول بنجاح'),
            backgroundColor: Colors.green,
          ),
        );

        // إعادة تعيين النموذج والانتقال للتبويب الثاني
        _resetForm();
        _tabController.animateTo(1);
      } else if (creationState.error != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(creationState.error!),
            backgroundColor: Colors.red,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إنشاء الجدول: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// عرض تفاصيل الجدول
  void _viewScheduleDetails(String examId) {
    Navigator.pushNamed(
      context,
      '/admin/exam-schedule-details',
      arguments: examId,
    );
  }

  /// تعديل الجدول
  void _editSchedule(String examId) {
    Navigator.pushNamed(
      context,
      '/admin/edit-exam-schedule',
      arguments: examId,
    );
  }

  /// طباعة تقرير الجدول
  void _printScheduleReport(String examId) {
    // TODO: تطبيق طباعة التقرير
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق طباعة التقرير قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في الجداول'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن جدول...',
                prefixIcon: Icon(Icons.search),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  // TODO: تطبيق البحث
                  Navigator.pop(context);
                },
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض إعدادات الجدولة
  void _showScheduleSettings() {
    Navigator.pushNamed(context, '/admin/schedule-settings');
  }
}
