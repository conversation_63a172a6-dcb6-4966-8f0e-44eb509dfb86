import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة المسؤولين من Firestore.
final adminsStreamProvider = StreamProvider.autoDispose<List<UserModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // سنفترض أن دالة `getUsersByRoleStream` ستتم إضافتها إلى FirebaseService
  return firebaseService.getUsersByRoleStream(['admin']);
});

/// Provider لتخزين نص البحث عن المسؤولين.
final adminSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لفلترة قائمة المسؤولين بناءً على نص البحث.
final filteredAdminsProvider = Provider.autoDispose<List<UserModel>>((ref) {
  final adminsAsyncValue = ref.watch(adminsStreamProvider);
  final searchQuery = ref.watch(adminSearchQueryProvider).toLowerCase();

  return adminsAsyncValue.when(
    data: (admins) {
      if (searchQuery.isEmpty) {
        return admins;
      }
      return admins.where((admin) {
        final nameLower = admin.name.toLowerCase();
        final emailLower = admin.email.toLowerCase();
        return nameLower.contains(searchQuery) || emailLower.contains(searchQuery);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
