import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/lesson_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:carousel_slider/carousel_slider.dart';
import 'package:url_launcher/url_launcher.dart';

/// شاشة عرض قائمة الدروس النموذجية مع ميزات البحث والتصفية
class StudentLessonsScreen extends ConsumerWidget {
  const StudentLessonsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lessons = ref.watch(filteredLessonsProvider);
    final subjectsAsyncValue = ref.watch(lessonSubjectsProvider);

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        body: Column(
          children: [
            // --- قسم البحث والتصفية ---
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  TextField(
                    onChanged: (value) => ref
                        .read(lessonSearchQueryProvider.notifier)
                        .state = value,
                    decoration: InputDecoration(
                      labelText: 'ابحث عن درس...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  subjectsAsyncValue.when(
                    data: (subjects) => DropdownButtonFormField<String>(
                      value: ref.watch(selectedLessonSubjectProvider),
                      hint: const Text('تصفية حسب المادة'),
                      onChanged: (value) => ref
                          .read(selectedLessonSubjectProvider.notifier)
                          .state = value,
                      items: [
                        const DropdownMenuItem(
                          value: null,
                          child: Text('كل المواد'),
                        ),
                        ...subjects.map((subject) {
                          return DropdownMenuItem(
                            value: subject,
                            child: Text(subject),
                          );
                        }),
                      ],
                      decoration: InputDecoration(
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                    loading: () => const SizedBox.shrink(),
                    error: (e, s) => const Text('خطأ في جلب المواد'),
                  ),
                ],
              ),
            ),
            // --- قائمة الدروس ---
            Expanded(
              child: lessons.isEmpty
                  ? const Center(child: Text('لا توجد دروس مطابقة.'))
                  : ListView.builder(
                      padding: const EdgeInsets.all(8.0),
                      itemCount: lessons.length,
                      itemBuilder: (context, index) {
                        final lesson = lessons[index];
                        return CustomCard(
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundImage: (lesson.imageUrls.isNotEmpty)
                                  ? NetworkImage(lesson.imageUrls.first)
                                  : null,
                              child: (lesson.imageUrls.isEmpty)
                                  ? const Icon(Icons.book_online)
                                  : null,
                            ),
                            title: Text(lesson.title,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold)),
                            subtitle: Text('المادة: ${lesson.subject}'),
                            onTap: () {
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder: (context) =>
                                      LessonDetailsScreen(lesson: lesson),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }
}

/// شاشة تفاصيل الدرس
class LessonDetailsScreen extends StatelessWidget {
  final LessonModel lesson;

  const LessonDetailsScreen({super.key, required this.lesson});

  // دالة لفتح الروابط الخارجية
  Future<void> _launchUrl(String urlString) async {
    final Uri url = Uri.parse(urlString);
    if (!await launchUrl(url)) {
      throw Exception('Could not launch $url');
    }
  }

  @override
  Widget build(BuildContext context) {
    // تهيئة محرر النصوص لعرض الوصف
    quill.QuillController descriptionController;
    try {
      final descriptionJson = jsonDecode(lesson.description);
      descriptionController = quill.QuillController(
        document: quill.Document.fromJson(descriptionJson),
        selection: const TextSelection.collapsed(offset: 0),
      );
    } catch (e) {
      descriptionController = quill.QuillController(
        document: quill.Document()..insert(0, lesson.description),
        selection: const TextSelection.collapsed(offset: 0),
      );
    }

    return Directionality(
      textDirection: TextDirection.rtl,
      child: Scaffold(
        appBar: AppBar(
          title: Text(lesson.title),
        ),
        body: SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // --- العنوان الرئيسي ---
              Text(
                lesson.title,
                style: Theme.of(context)
                    .textTheme
                    .headlineSmall
                    ?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'المادة: ${lesson.subject}',
                style: Theme.of(context).textTheme.titleMedium,
              ),
              const Divider(height: 24),

              // --- معرض الصور ---
              if (lesson.imageUrls.isNotEmpty)
                CarouselSlider(
                  options: CarouselOptions(
                    autoPlay: true,
                    aspectRatio: 16 / 9,
                    enlargeCenterPage: true,
                  ),
                  items: lesson.imageUrls
                      .map((item) => Container(
                            margin: const EdgeInsets.all(5.0),
                            child: ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(5.0)),
                                child: Image.network(item,
                                    fit: BoxFit.cover, width: 1000.0)),
                          ))
                      .toList(),
                ),
              if (lesson.imageUrls.isNotEmpty) const SizedBox(height: 16),

              // --- رابط الفيديو ---
              if (lesson.videoUrl != null && lesson.videoUrl!.isNotEmpty)
                ElevatedButton.icon(
                  icon: const Icon(Icons.play_circle_fill),
                  label: const Text('مشاهدة الفيديو'),
                  onPressed: () => _launchUrl(lesson.videoUrl!),
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 40),
                  ),
                ),
              if (lesson.videoUrl != null && lesson.videoUrl!.isNotEmpty)
                const SizedBox(height: 16),

              // --- الوصف المنسق ---
              Text(
                'شرح الدرس:',
                style: Theme.of(context).textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade200),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: SizedBox(
                  height: 300, // ارتفاع تقديري لعرض المحتوى
                  child: quill.QuillEditor.basic(
                    controller: descriptionController,
                  ),
                ),
              ),
              const SizedBox(height: 24),

              // --- المرفقات ---
              if (lesson.attachments.isNotEmpty)
                Text(
                  'المرفقات:',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              if (lesson.attachments.isNotEmpty) const SizedBox(height: 8),
              ...lesson.attachments.map((attachment) {
                return Card(
                  child: ListTile(
                    leading: const Icon(Icons.attach_file),
                    title: Text(attachment['name'] ?? 'ملف'),
                    trailing: const Icon(Icons.download),
                    onTap: () => _launchUrl(attachment['url']!),
                  ),
                );
              }).toList(),
            ],
          ),
        ),
      ),
    );
  }
}
