
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات الدروس النموذجية
class LessonModel {
  final String id;
  final String title; // عنوان الدرس
  final String description; // شرح الدرس (محتوى Quill Editor بصيغة JSON)
  final String subject; // المادة الدراسية
  final String? classId; // معرّف الصف (اختياري للدروس العامة)
  final String? videoUrl; // رابط فيديو (يوتيوب، فيميو، الخ) - اختياري
  final List<String> imageUrls; // قائمة روابط الصور التوضيحية للدرس
  // قائمة من الخرائط لتخزين الملفات المرفقة، كل خريطة تحتوي على اسم الملف ورابطه
  final List<Map<String, String>> attachments;
  final Timestamp createdAt; // تاريخ إنشاء الدرس للترتيب

  LessonModel({
    required this.id,
    required this.title,
    required this.description,
    required this.subject,
    this.classId,
    this.videoUrl,
    this.imageUrls = const [],
    this.attachments = const [],
    required this.createdAt,
  });

  /// دالة لتحويل البيانات من Firestore إلى كائن LessonModel
  factory LessonModel.fromMap(Map<String, dynamic> data, String documentId) {
    return LessonModel(
      id: documentId,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      subject: data['subject'] ?? '',
      classId: data['classId'],
      videoUrl: data['videoUrl'],
      imageUrls: data['imageUrls'] != null ? List<String>.from(data['imageUrls']) : [],
      attachments: data['attachments'] != null
          ? List<Map<String, String>>.from(
              (data['attachments'] as List).map((item) => Map<String, String>.from(item))
            )
          : [],
      createdAt: data['createdAt'] ?? Timestamp.now(),
    );
  }

  /// دالة لتحويل الكائن إلى Map لتخزينه في Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'subject': subject,
      'classId': classId,
      'videoUrl': videoUrl,
      'imageUrls': imageUrls,
      'attachments': attachments,
      'createdAt': createdAt,
    };
  }
}
