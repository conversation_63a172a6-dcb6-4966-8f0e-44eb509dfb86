import 'package:flutter/material.dart';
import 'package:school_management_system/admin_screens/about_school_screen.dart';
import 'package:school_management_system/admin_screens/admins_management_screen.dart';
import 'package:school_management_system/admin_screens/content_management_screen.dart';
import 'package:school_management_system/admin_screens/notifications_screen.dart';
import 'package:school_management_system/admin_screens/staff_management_screen.dart';
import 'package:school_management_system/admin_screens/student_feedback_screen.dart';
import 'package:school_management_system/admin_screens/student_reports_screen.dart';
import 'package:school_management_system/admin_screens/subjects_management_screen.dart';
import 'package:school_management_system/admin_screens/classes_management_screen.dart';
import 'package:school_management_system/admin_screens/timetables_management_screen.dart';
import 'package:school_management_system/admin_screens/admin_home_page.dart';
// استيراد شاشات الامتحانات الجديدة
import 'package:school_management_system/admin_screens/exams_dashboard_screen.dart';
import 'package:school_management_system/admin_screens/exam_schedules_management_screen.dart';
import 'package:school_management_system/admin_screens/exam_settings_screen.dart';
// استيراد شاشة التواصل المتقدمة
import 'package:school_management_system/admin_screens/advanced_messaging_screen.dart';
import 'package:school_management_system/models/conversation_model.dart';

/// الشاشة الرئيسية للوحة تحكم المسؤول.
///
/// هذه الشاشة تعمل كحاوية أساسية وتستخدم `NavigationRail` (قائمة تنقل جانبية)
/// للسماح للمسؤول بالانتقال بين الأقسام المختلفة في لوحة التحكم.
class AdminDashboardScreen extends StatefulWidget {
  const AdminDashboardScreen({super.key});

  @override
  State<AdminDashboardScreen> createState() => _AdminDashboardScreenState();
}

class _AdminDashboardScreenState extends State<AdminDashboardScreen> {
  // متغير لتتبع القسم المحدد حاليًا في قائمة التنقل.
  int _selectedIndex = 0;

  // قائمة الصفحات (الويدجتس) التي يتم عرضها بناءً على اختيار المسؤول.
  // كل صفحة تمثل قسمًا رئيسيًا في لوحة التحكم.
  static const List<Widget> _adminPages = <Widget>[
    AdminHomePage(), // 0: الصفحة الرئيسية للوحة التحكم
    ContentManagementScreen(), // 1: إدارة المحتوى
    StaffManagementScreen(), // 2: إدارة الموظفين
    StudentReportsScreen(), // 3: تقارير الطلاب
    StudentFeedbackScreen(), // 4: ملاحظات الطلاب
    NotificationsScreen(), // 5: الإشعارات
    AdminsManagementScreen(), // 6: إدارة المسؤولين
    AboutSchoolScreen(), // 7: عن المدرسة
    SubjectsManagementScreen(), // 8: إدارة المواد
    ClassesManagementScreen(), // 9: إدارة الفصول
    TimetablesManagementScreen(), // 10: إدارة الجداول
    // شاشات الامتحانات الجديدة
    ExamsDashboardScreen(), // 11: لوحة معلومات الامتحانات
    ExamSchedulesManagementScreen(), // 12: إدارة جداول الامتحانات
    ExamSettingsScreen(), // 13: إعدادات نظام الامتحانات
    // شاشة التواصل المتقدمة
    AdvancedMessagingScreen(
      adminId: 'admin_001', // TODO: استخدام معرف المدير الفعلي
      adminName: 'مدير النظام', // TODO: استخدام اسم المدير الفعلي
      adminRole: ParticipantRole.admin,
    ), // 14: إدارة التواصل المتقدمة
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // عنوان يظهر في الشريط العلوي، يتغير بناءً على القسم المحدد.
        title: const Text('لوحة تحكم مدير النظام'),
        // يمكن إضافة أيقونات إجراءات هنا، مثل تسجيل الخروج
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'تسجيل الخروج',
            onPressed: () {
              // TODO: Implement logout functionality
              // Navigator.of(context).pushReplacement(...);
            },
          ),
        ],
      ),
      body: Directionality(
        // ضمان اتجاه النص من اليمين لليسار للتوافق مع اللغة العربية.
        textDirection: TextDirection.rtl,
        child: Row(
          children: <Widget>[
            // قائمة التنقل الجانبية التي تظهر على يسار الشاشة في نسخة الويب.
            NavigationRail(
              // `selectedIndex` يحدد أي وجهة (قسم) هي النشطة حاليًا.
              selectedIndex: _selectedIndex,
              // دالة تُستدعى عند اختيار وجهة جديدة.
              onDestinationSelected: (int index) {
                // تحديث حالة الويدجت لإعادة بناء الواجهة مع الصفحة الجديدة.
                setState(() {
                  _selectedIndex = index;
                });
              },
              // تصميم الأيقونات والعناوين في قائمة التنقل.
              labelType: NavigationRailLabelType.all,
              // قائمة وجهات التنقل (الأقسام).
              destinations: const <NavigationRailDestination>[
                NavigationRailDestination(
                  icon: Icon(Icons.dashboard_outlined),
                  selectedIcon: Icon(Icons.dashboard),
                  label: Text('الرئيسية'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.article_outlined),
                  selectedIcon: Icon(Icons.article),
                  label: Text('المحتوى'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.people_outline),
                  selectedIcon: Icon(Icons.people),
                  label: Text('الموظفون'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.bar_chart_outlined),
                  selectedIcon: Icon(Icons.bar_chart),
                  label: Text('التقارير'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.feedback_outlined),
                  selectedIcon: Icon(Icons.feedback),
                  label: Text('الملاحظات'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.notifications_outlined),
                  selectedIcon: Icon(Icons.notifications),
                  label: Text('الإشعارات'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.admin_panel_settings_outlined),
                  selectedIcon: Icon(Icons.admin_panel_settings),
                  label: Text('المسؤولون'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.school_outlined),
                  selectedIcon: Icon(Icons.school),
                  label: Text('المدرسة'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.book_outlined),
                  selectedIcon: Icon(Icons.book),
                  label: Text('المواد'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.class_outlined),
                  selectedIcon: Icon(Icons.class_),
                  label: Text('الفصول'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.schedule_outlined),
                  selectedIcon: Icon(Icons.schedule),
                  label: Text('الجداول'),
                ),
                // وجهات تنقل الامتحانات الجديدة
                NavigationRailDestination(
                  icon: Icon(Icons.quiz_outlined),
                  selectedIcon: Icon(Icons.quiz),
                  label: Text('الامتحانات'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.calendar_today_outlined),
                  selectedIcon: Icon(Icons.calendar_today),
                  label: Text('جداول الامتحانات'),
                ),
                NavigationRailDestination(
                  icon: Icon(Icons.settings_outlined),
                  selectedIcon: Icon(Icons.settings),
                  label: Text('إعدادات الامتحانات'),
                ),
                // وجهة تنقل التواصل المتقدمة
                NavigationRailDestination(
                  icon: Icon(Icons.forum_outlined),
                  selectedIcon: Icon(Icons.forum),
                  label: Text('التواصل المتقدم'),
                ),
              ],
            ),
            // خط فاصل بصري بين قائمة التنقل والمحتوى الرئيسي.
            const VerticalDivider(thickness: 1, width: 1),
            // الجزء الرئيسي من الشاشة الذي يعرض محتوى القسم المحدد.
            // `Expanded` تجعله يأخذ كل المساحة المتبقية.
            Expanded(child: _adminPages[_selectedIndex]),
          ],
        ),
      ),
    );
  }
}
