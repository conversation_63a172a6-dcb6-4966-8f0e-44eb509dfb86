
import 'package:flutter/material.dart';

/// بطاقة عرض مخصصة وقابلة لإعادة الاستخدام
/// توفر تصميمًا موحدًا لعرض المحتوى في التطبيق
class CustomCard extends StatelessWidget {
  final Widget child; // المحتوى الذي سيوضع داخل البطاقة
  final VoidCallback? onTap; // دالة تُستدعى عند الضغط على البطاقة (اختياري)
  final Color? color; // لون خلفية البطاقة (اختياري)

  const CustomCard({super.key, required this.child, this.onTap, this.color});

  @override
  Widget build(BuildContext context) {
    return Card(
      color: color, // تطبيق اللون الممرر
      // التحكم في شكل البطاقة
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0), // حواف دائرية
      ),
      elevation: 3, // ظل خفيف لإعطاء عمق
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      
      // استخدام InkWell لجعل البطاقة قابلة للضغط وإظهار تأثير عند اللمس
      child: InkWell(
        borderRadius: BorderRadius.circular(12.0),
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: child,
        ),
      ),
    );
  }
}
