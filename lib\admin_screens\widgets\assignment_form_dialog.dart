import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:file_picker/file_picker.dart';

class AssignmentFormDialog extends ConsumerStatefulWidget {
  final AssignmentModel? assignment;

  const AssignmentFormDialog({super.key, this.assignment});

  @override
  ConsumerState<AssignmentFormDialog> createState() => _AssignmentFormDialogState();
}

class _AssignmentFormDialogState extends ConsumerState<AssignmentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _subjectController;
  late TextEditingController _teacherController;
  DateTime? _dueDate;
  AssignmentPriority _priority = AssignmentPriority.medium;
  AssignmentStatus _status = AssignmentStatus.pending;
  List<AssignmentAttachment> _attachments = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final assignment = widget.assignment;
    _titleController = TextEditingController(text: assignment?.title);
    _descriptionController = TextEditingController(text: assignment?.description);
    _subjectController = TextEditingController(text: assignment?.subjectName);
    _teacherController = TextEditingController(text: assignment?.teacherName);
    _dueDate = assignment?.dueDate;
    _priority = assignment?.priority ?? AssignmentPriority.medium;
    if (assignment != null) {
      _status = assignment.currentStatus;
    }
    _attachments = List.from(assignment?.attachments ?? []);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _subjectController.dispose();
    _teacherController.dispose();
    super.dispose();
  }

  Future<void> _selectDueDate(BuildContext context) async {
    final picked = await showDatePicker(
      context: context,
      initialDate: _dueDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime(2101),
    );
    if (picked != null && picked != _dueDate) {
      setState(() {
        _dueDate = picked;
      });
    }
  }

  /// إضافة مرفق جديد
  Future<void> _addAttachment() async {
    final result = await FilePicker.platform.pickFiles(
      allowMultiple: false,
      type: FileType.any,
    );

    if (result != null && result.files.isNotEmpty) {
      final file = result.files.first;
      final attachment = AssignmentAttachment(
        name: file.name,
        url: '', // سيتم رفعه لاحقاً
        type: AttachmentType.fromExtension(file.name),
        size: file.size,
        uploadedAt: DateTime.now(),
      );

      setState(() {
        _attachments.add(attachment);
      });
    }
  }


  /// حذف مرفق
  void _removeAttachment(int index) {
    setState(() {
      _attachments.removeAt(index);
    });
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate() && _dueDate != null) {
      setState(() => _isLoading = true);
      try {
        final assignment = AssignmentModel(
          id: widget.assignment?.id ?? '',
          title: _titleController.text,
          description: _descriptionController.text,
          subjectName: _subjectController.text,
          teacherName: _teacherController.text.isEmpty
              ? null
              : _teacherController.text,
          dueDate: _dueDate!,
          priority: _priority,
          attachments: _attachments,
          createdAt:
              widget.assignment?.createdAt ??
                  Timestamp.now(),
        );

        final firebaseService = ref.read(firebaseServiceProvider);
        if (widget.assignment == null) {
          await firebaseService.addAssignmentEnhanced(assignment);
        } else {
          await firebaseService.updateAssignmentEnhanced(assignment);
        }
        
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم حفظ الواجب بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: ${e.toString()}')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Container(
        width: MediaQuery.of(context).size.width * 0.9,
        height: MediaQuery.of(context).size.height * 0.8,
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان
            Row(
              children: [
                Icon(
                  widget.assignment == null ? Icons.add : Icons.edit,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.assignment == null ? 'إضافة واجب جديد' : 'تعديل الواجب',
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
            
            const Divider(),
            
            // النموذج
            Expanded(
              child: Form(
                key: _formKey,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // العنوان والوصف
                      TextFormField(
                        controller: _titleController,
                        decoration: InputDecoration(
                          labelText: 'عنوان الواجب *',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(Icons.title),
                        ),
                        validator: (v) => v!.isEmpty ? 'العنوان مطلوب' : null,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      TextFormField(
                        controller: _descriptionController,
                        decoration: InputDecoration(
                          labelText: 'وصف الواجب',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          prefixIcon: const Icon(Icons.description),
                          alignLabelWithHint: true,
                        ),
                        maxLines: 3,
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // المادة والمعلم
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _subjectController,
                              decoration: InputDecoration(
                                labelText: 'المادة *',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: const Icon(Icons.subject),
                              ),
                              validator: (v) => v!.isEmpty ? 'المادة مطلوبة' : null,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextFormField(
                              controller: _teacherController,
                              decoration: InputDecoration(
                                labelText: 'اسم المعلم',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: const Icon(Icons.person),
                              ),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // الأولوية والحالة
                      Row(
                        children: [
                          Expanded(
                            child: DropdownButtonFormField<AssignmentPriority>(
                              value: _priority,
                              decoration: InputDecoration(
                                labelText: 'الأولوية',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: Icon(_priority.icon, color: _priority.color),
                              ),
                              items: AssignmentPriority.values.map((priority) =>
                                DropdownMenuItem(
                                  value: priority,
                                  child: Row(
                                    children: [
                                      Icon(priority.icon, color: priority.color, size: 16),
                                      const SizedBox(width: 8),
                                      Text(priority.arabicName),
                                    ],
                                  ),
                                ),
                              ).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _priority = value!;
                                });
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: DropdownButtonFormField<AssignmentStatus>(
                              value: _status,
                              decoration: InputDecoration(
                                labelText: 'الحالة',
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                prefixIcon: Icon(_getStatusIcon(_status),
                                    color: _getStatusColor(_status)),
                              ),
                              items: AssignmentStatus.values
                                  .map((status) => DropdownMenuItem(
                                        value: status,
                                        child: Row(
                                          children: [
                                            Icon(_getStatusIcon(status),
                                                color: _getStatusColor(status),
                                                size: 16),
                                            const SizedBox(width: 8),
                                            Text(status.arabicName),
                                          ],
                                        ),
                                      ))
                                  .toList(),
                              onChanged: (value) {
                                setState(() {
                                  _status = value!;
                                });
                              },
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 16),
                      
                      // تاريخ التسليم
                      InkWell(
                        onTap: () => _selectDueDate(context),
                        child: Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.calendar_today),
                              const SizedBox(width: 12),
                              Text(
                                _dueDate == null
                                    ? 'اختر تاريخ التسليم *'
                                    : 'تاريخ التسليم: ${DateFormat.yMMMMEEEEd('ar').format(_dueDate!)}',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: _dueDate == null ? Colors.grey.shade600 : Colors.black87,
                                ),
                              ),
                              const Spacer(),
                              const Icon(Icons.arrow_drop_down),
                            ],
                          ),
                        ),
                      ),
                      
                      const SizedBox(height: 24),
                      
                      // المرفقات
                      Row(
                        children: [
                          const Text(
                            'المرفقات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          ElevatedButton.icon(
                            onPressed: _addAttachment,
                            icon: const Icon(Icons.attach_file, size: 16),
                            label: const Text('إضافة مرفق'),
                            style: ElevatedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 12, vertical: 8),
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 12),
                      
                      // قائمة المرفقات
                      if (_attachments.isNotEmpty)
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _attachments.length,
                            separatorBuilder: (context, index) => const Divider(height: 1),
                            itemBuilder: (context, index) {
                              final attachment = _attachments[index];
                              return ListTile(
                                leading: Icon(
                                  attachment.type.icon,
                                ),
                                title: Text(attachment.name),
                                subtitle: Text(
                                  '${(attachment.size! / 1024).toStringAsFixed(1)} KB',
                                ),
                                trailing: IconButton(
                                  icon: const Icon(Icons.delete, color: Colors.red),
                                  onPressed: () => _removeAttachment(index),
                                ),
                              );
                            },
                          ),
                        ),
                      
                      if (_attachments.isEmpty)
                        Container(
                          padding: const EdgeInsets.all(24),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade300, style: BorderStyle.solid),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Center(
                            child: Column(
                              children: [
                                Icon(Icons.attach_file, size: 48, color: Colors.grey.shade400),
                                const SizedBox(height: 8),
                                Text(
                                  'لا توجد مرفقات',
                                  style: TextStyle(color: Colors.grey.shade600),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
            
            const Divider(),
            
            // أزرار الإجراءات
            Row(
              children: [
                const Spacer(),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                const SizedBox(width: 12),
                ElevatedButton(
                  onPressed: _isLoading ? null : _submit,
                  child: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child:
                              CircularProgressIndicator(strokeWidth: 2),
                        )
                      : Text(widget.assignment == null ? 'إضافة' : 'حفظ'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AssignmentStatus status) {
    return status.icon;
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(AssignmentStatus status) {
    return status.color;
  }
}
