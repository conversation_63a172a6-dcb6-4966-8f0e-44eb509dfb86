import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';

/// خدمة متخصصة في التعامل مع كل ما يخص الصفوف في Firestore.
class ClassService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب قائمة الصفوف مع الاستماع للتغيرات الفورية.
  Stream<List<ClassModel>> getClasses() {
    return _firestore.collection('classes').orderBy('name').snapshots().map((
      snapshot,
    ) {
      return snapshot.docs.map((doc) => ClassModel.fromFirestore(doc)).toList();
    });
  }

  /// جلب تفاصيل فصل معين.
  Stream<DocumentSnapshot> getClassDetails(String classId) {
    return _firestore.collection('classes').doc(classId).snapshots();
  }

  /// جلب قائمة الطلاب بناءً على قائمة من الـ IDs.
  Future<List<StudentModel>> getStudentsByIds(List<String> ids) async {
    if (ids.isEmpty) return [];
    final studentDocs =
        await _firestore
            .collection('students')
            .where(FieldPath.documentId, whereIn: ids)
            .get();
    return studentDocs.docs
        .map((doc) => StudentModel.fromFirestore(doc))
        .toList();
  }

  /// إضافة فصل جديد.
  Future<void> addClass(
    String name, {
    String? teacherId,
    List<String>? studentIds,
  }) async {
    await _firestore.collection('classes').add({
      'name': name,
      'teacherId': teacherId,
      'students': studentIds ?? [],
      'createdAt': Timestamp.now(),
    });
  }

  /// تحديث فصل موجود.
  Future<void> updateClass(
    String classId,
    String name, {
    String? teacherId,
  }) async {
    await _firestore.collection('classes').doc(classId).update({
      'name': name,
      'teacherId': teacherId,
    });
  }

  /// حذف فصل.
  Future<void> deleteClass(String classId) async {
    // TODO: يجب أيضاً تحديث بيانات كل طالب كان في هذا الفصل (إزالة classId).
    await _firestore.collection('classes').doc(classId).delete();
  }

  /// تعيين/تحديث قائمة الطلاب في فصل معين.
  Future<void> assignStudentsToClass(
    String classId,
    List<String> studentIds,
  ) async {
    // TODO: يجب معالجة الفروقات (من تم إضافته ومن تم حذفه) لتحديث بيانات الطلاب أيضاً.
    await _firestore.collection('classes').doc(classId).update({
      'students': studentIds,
    });
  }
}
