import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/timetable_providers.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/shared_widgets/subject_dropdown.dart';
import 'package:school_management_system/widgets/shared_widgets/teacher_dropdown.dart';

class SessionEditDialog extends ConsumerStatefulWidget {
  final String classId;
  final String day;
  final int period;
  final dynamic currentSessionData;

  const SessionEditDialog({
    super.key,
    required this.classId,
    required this.day,
    required this.period,
    this.currentSessionData,
  });

  @override
  ConsumerState<SessionEditDialog> createState() => _SessionEditDialogState();
}

class _SessionEditDialogState extends ConsumerState<SessionEditDialog> {
  String? _selectedSubject;
  String? _selectedTeacherId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.currentSessionData != null && widget.currentSessionData is Map) {
      _selectedSubject = widget.currentSessionData['subject'];
      _selectedTeacherId = widget.currentSessionData['teacherId'];
    }
  }

  Future<void> _submit() async {
    setState(() => _isLoading = true);
    try {
      await ref.read(timetableControllerProvider).updateSession(
            widget.classId,
            widget.day,
            widget.period,
            _selectedSubject,
            _selectedTeacherId,
          );
      Navigator.of(context).pop();
      showSuccessSnackBar(context, 'تم تحديث الحصة بنجاح');
    } catch (e) {
      showErrorSnackBar(context, e.toString());
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _clearSession() async {
    setState(() => _isLoading = true);
    try {
      await ref.read(timetableControllerProvider).updateSession(
            widget.classId,
            widget.day,
            widget.period,
            null,
            null,
          );
      Navigator.of(context).pop();
      showSuccessSnackBar(context, 'تم مسح الحصة بنجاح');
    } catch (e) {
      showErrorSnackBar(context, 'فشل مسح الحصة: $e');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('تعديل حصة يوم ${widget.day} - الحصة ${widget.period}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SubjectDropdown(
              classId: widget.classId,
              selectedValue: _selectedSubject,
              onChanged: (value) => setState(() => _selectedSubject = value),
            ),
            const SizedBox(height: 20),
            TeacherDropdown(
              selectedValue: _selectedTeacherId,
              onChanged: (value) => setState(() => _selectedTeacherId = value),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        TextButton(
          onPressed: _isLoading ? null : _clearSession,
          child: const Text('مسح الحصة', style: TextStyle(color: Colors.red)),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submit,
          child: _isLoading
              ? const SizedBox(width: 20, height: 20, child: CircularProgressIndicator())
              : const Text('حفظ'),
        ),
      ],
    );
  }
}
