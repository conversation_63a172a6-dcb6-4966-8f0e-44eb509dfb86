import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

// TODO: نقل منطق العمليات (add, update) إلى ViewModel/Controller خاص بالموظفين.

class StaffFormDialog extends ConsumerStatefulWidget {
  final UserModel? staff;
  const StaffFormDialog({super.key, this.staff});

  @override
  ConsumerState<StaffFormDialog> createState() => _StaffFormDialogState();
}

class _StaffFormDialogState extends ConsumerState<StaffFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;
  late final TextEditingController _jobTitleController;
  late final TextEditingController _bioController;
  
  String _selectedRole = 'teacher';
  XFile? _imageFile;
  String? _existingImageUrl;
  bool get _isEditing => widget.staff != null;

  @override
  void initState() {
    super.initState();
    final staff = widget.staff;
    _nameController = TextEditingController(text: staff?.name);
    _emailController = TextEditingController(text: staff?.email);
    _passwordController = TextEditingController();
    _jobTitleController = TextEditingController(text: staff?.jobTitle);
    _bioController = TextEditingController(text: staff?.bio);
    _selectedRole = staff?.role ?? 'teacher';
    _existingImageUrl = staff?.profileImageUrl;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _jobTitleController.dispose();
    _bioController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      // إظهار مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const Center(child: CircularProgressIndicator()),
      );

      final firebaseService = ref.read(firebaseServiceProvider);
      
      try {
        if (_isEditing) {
          // TODO: استبدال هذا بمناداة دالة في ViewModel/Controller
          await firebaseService.updateStaff( // نفترض وجود هذه الدالة
            staff: widget.staff!,
            name: _nameController.text,
            jobTitle: _jobTitleController.text,
            bio: _bioController.text,
            role: _selectedRole,
            newImage: _imageFile,
          );
        } else {
          // TODO: استبدال هذا بمناداة دالة في ViewModel/Controller
          await firebaseService.addStaff( // نفترض وجود هذه الدالة
            name: _nameController.text,
            email: _emailController.text,
            password: _passwordController.text,
            jobTitle: _jobTitleController.text,
            bio: _bioController.text,
            role: _selectedRole,
            image: _imageFile,
          );
        }
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        Navigator.of(context).pop(); // إغلاق الـ Dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_isEditing ? 'تم تحديث البيانات بنجاح' : 'تمت إضافة الموظف بنجاح'), backgroundColor: Colors.green),
        );
      } catch (e) {
        Navigator.of(context).pop(); // إغلاق مؤشر التحميل
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e'), backgroundColor: Colors.red),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل بيانات الموظف' : 'إضافة موظف جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // --- قسم الصورة الشخصية ---
              Stack(
                alignment: Alignment.bottomRight,
                children: [
                  CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey.shade200,
                    backgroundImage: _imageFile != null
                        ? FileImage(File(_imageFile!.path))
                        : (_existingImageUrl != null && _existingImageUrl!.isNotEmpty
                            ? NetworkImage(_existingImageUrl!)
                            : null) as ImageProvider?,
                    child: (_imageFile == null && (_existingImageUrl == null || _existingImageUrl!.isEmpty))
                        ? const Icon(Icons.person, size: 50, color: Colors.grey)
                        : null,
                  ),
                  IconButton(
                    icon: const CircleAvatar(
                      radius: 18,
                      backgroundColor: Colors.blue,
                      child: Icon(Icons.edit, color: Colors.white, size: 18),
                    ),
                    onPressed: () async {
                      final pickedImage = await ref.read(firebaseServiceProvider).pickImage();
                      if (pickedImage != null) {
                        setState(() {
                          _imageFile = pickedImage;
                        });
                      }
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // --- حقول البيانات ---
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                validator: (value) => value!.isEmpty ? 'الرجاء إدخال الاسم' : null,
              ),
              if (!_isEditing) ...[
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                  validator: (value) => value!.isEmpty || !value.contains('@') ? 'بريد إلكتروني غير صالح' : null,
                ),
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(labelText: 'كلمة المرور'),
                  obscureText: true,
                  validator: (value) => value!.length < 6 ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل' : null,
                ),
              ],
              TextFormField(
                controller: _jobTitleController,
                decoration: const InputDecoration(labelText: 'المسمى الوظيفي (مثال: معلم رياضيات)'),
                validator: (value) => value!.isEmpty ? 'الرجاء إدخال المسمى الوظيفي' : null,
              ),
              TextFormField(
                controller: _bioController,
                decoration: const InputDecoration(labelText: 'نبذة تعريفية'),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedRole,
                decoration: const InputDecoration(labelText: 'الدور والصلاحيات', border: OutlineInputBorder()),
                items: ['teacher', 'staff']
                    .map((role) => DropdownMenuItem(
                          value: role,
                          child: Text(role == 'teacher' ? 'معلم' : 'إداري'),
                        ))
                    .toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() => _selectedRole = value);
                  }
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(onPressed: _submit, child: const Text('حفظ')),
      ],
    );
  }
}
