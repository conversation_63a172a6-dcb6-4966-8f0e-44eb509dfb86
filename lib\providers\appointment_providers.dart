import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/appointment_model.dart';
import 'package:school_management_system/services/appointment_service.dart';

/// مزودات إدارة المواعيد والاجتماعات
///
/// تحتوي على جميع مزودات الحالة المطلوبة لإدارة المواعيد في التطبيق
/// بما في ذلك إنشاء وتعديل وحذف المواعيد، وإدارة المشاركين، والتذكيرات
///
/// الميزات الرئيسية:
/// - إدارة حالة المواعيد في الوقت الفعلي
/// - تتبع التغييرات والتحديثات
/// - إدارة الفلاتر والبحث
/// - تخزين مؤقت للبيانات
/// - معالجة الأخطاء والاستثناءات
/// - دعم التحديث التلقائي

// ===================================================================
// مزودات الخدمات الأساسية
// ===================================================================

/// مزود خدمة إدارة المواعيد
///
/// يوفر مثيل واحد من خدمة المواعيد لاستخدامه في جميع أنحاء التطبيق
final appointmentServiceProvider = Provider<AppointmentService>((ref) {
  return AppointmentService();
});

// ===================================================================
// مزودات البيانات الأساسية
// ===================================================================

/// مزود قائمة مواعيد المستخدم
///
/// يجلب جميع المواعيد التي يشارك فيها المستخدم أو ينظمها
/// مع دعم الفلترة حسب التاريخ والحالة والنوع
///
/// [userId] معرف المستخدم
/// [filters] فلاتر البحث والتصفية
final userAppointmentsProvider =
    StreamProvider.family<List<AppointmentModel>, UserAppointmentsParams>((
      ref,
      params,
    ) {
      final appointmentService = ref.watch(appointmentServiceProvider);

      return appointmentService.getUserAppointments(
        params.userId,
        startDate: params.startDate,
        endDate: params.endDate,
        status: params.status,
        type: params.type,
        limit: params.limit,
      );
    });

/// مزود تفاصيل موعد محدد
///
/// يجلب تفاصيل موعد واحد بناءً على معرفه
///
/// [appointmentId] معرف الموعد
final appointmentDetailsProvider =
    FutureProvider.family<AppointmentModel?, String>((
      ref,
      appointmentId,
    ) async {
      final appointmentService = ref.watch(appointmentServiceProvider);
      return await appointmentService.getAppointment(appointmentId);
    });

/// مزود المواعيد القادمة للمستخدم
///
/// يجلب المواعيد القادمة خلال الأسبوع القادم
///
/// [userId] معرف المستخدم
final upcomingAppointmentsProvider =
    StreamProvider.family<List<AppointmentModel>, String>((ref, userId) {
      final appointmentService = ref.watch(appointmentServiceProvider);

      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));

      return appointmentService.getUserAppointments(
        userId,
        startDate: now,
        endDate: nextWeek,
        status: AppointmentStatus.confirmed,
        limit: 20,
      );
    });

/// مزود المواعيد اليوم للمستخدم
///
/// يجلب المواعيد المجدولة لليوم الحالي
///
/// [userId] معرف المستخدم
final todayAppointmentsProvider =
    StreamProvider.family<List<AppointmentModel>, String>((ref, userId) {
      final appointmentService = ref.watch(appointmentServiceProvider);

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      return appointmentService.getUserAppointments(
        userId,
        startDate: startOfDay,
        endDate: endOfDay,
        limit: 10,
      );
    });

// ===================================================================
// مزودات الإحصائيات والتحليلات
// ===================================================================

/// مزود إحصائيات المواعيد للمستخدم
///
/// يحسب إحصائيات شاملة عن مواعيد المستخدم
///
/// [userId] معرف المستخدم
final appointmentStatsProvider =
    FutureProvider.family<AppointmentStats, String>((ref, userId) async {
      final appointmentService = ref.watch(appointmentServiceProvider);

      // جلب جميع المواعيد للشهر الحالي
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      final appointments =
          await appointmentService
              .getUserAppointments(
                userId,
                startDate: startOfMonth,
                endDate: endOfMonth,
                limit: 1000,
              )
              .first;

      return _calculateAppointmentStats(appointments);
    });

/// مزود المواعيد المعلقة للموافقة
///
/// يجلب المواعيد التي تحتاج موافقة من المستخدم
///
/// [userId] معرف المستخدم
final pendingApprovalsProvider =
    StreamProvider.family<List<AppointmentModel>, String>((ref, userId) {
      final appointmentService = ref.watch(appointmentServiceProvider);

      return appointmentService.getUserAppointments(
        userId,
        status: AppointmentStatus.pending,
        limit: 50,
      );
    });

// ===================================================================
// مزودات إدارة الحالة المحلية
// ===================================================================

/// مزود حالة إنشاء موعد جديد
///
/// يدير حالة نموذج إنشاء الموعد الجديد
final createAppointmentStateProvider =
    StateNotifierProvider<CreateAppointmentNotifier, CreateAppointmentState>((
      ref,
    ) {
      final appointmentService = ref.watch(appointmentServiceProvider);
      return CreateAppointmentNotifier(appointmentService);
    });

/// مزود فلاتر المواعيد
///
/// يدير حالة فلاتر البحث والتصفية
final appointmentFiltersProvider =
    StateNotifierProvider<AppointmentFiltersNotifier, AppointmentFilters>((
      ref,
    ) {
      return AppointmentFiltersNotifier();
    });

/// مزود الموعد المحدد حالياً
///
/// يتتبع الموعد المحدد في الواجهة
final selectedAppointmentProvider = StateProvider<AppointmentModel?>(
  (ref) => null,
);

/// مزود حالة التحميل العامة
///
/// يتتبع حالة التحميل للعمليات المختلفة
final appointmentLoadingProvider = StateProvider<bool>((ref) => false);

/// مزود رسائل الخطأ
///
/// يدير رسائل الخطأ المتعلقة بالمواعيد
final appointmentErrorProvider = StateProvider<String?>((ref) => null);

// ===================================================================
// الفئات المساعدة
// ===================================================================

/// معاملات جلب مواعيد المستخدم
class UserAppointmentsParams {
  /// معرف المستخدم
  final String userId;

  /// تاريخ البداية للفلترة
  final DateTime? startDate;

  /// تاريخ النهاية للفلترة
  final DateTime? endDate;

  /// حالة المواعيد للفلترة
  final AppointmentStatus? status;

  /// نوع المواعيد للفلترة
  final AppointmentType? type;

  /// عدد المواعيد المطلوب إرجاعها
  final int limit;

  const UserAppointmentsParams({
    required this.userId,
    this.startDate,
    this.endDate,
    this.status,
    this.type,
    this.limit = 50,
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is UserAppointmentsParams &&
        other.userId == userId &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.status == status &&
        other.type == type &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    return userId.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        status.hashCode ^
        type.hashCode ^
        limit.hashCode;
  }
}

/// إحصائيات المواعيد
class AppointmentStats {
  /// إجمالي عدد المواعيد
  final int totalAppointments;

  /// عدد المواعيد المؤكدة
  final int confirmedAppointments;

  /// عدد المواعيد المعلقة
  final int pendingAppointments;

  /// عدد المواعيد الملغية
  final int cancelledAppointments;

  /// عدد المواعيد المكتملة
  final int completedAppointments;

  /// متوسط مدة المواعيد بالدقائق
  final double averageDuration;

  /// أكثر نوع موعد شيوعاً
  final AppointmentType? mostCommonType;

  /// أكثر فئة موعد شيوعاً
  final AppointmentCategory? mostCommonCategory;

  const AppointmentStats({
    required this.totalAppointments,
    required this.confirmedAppointments,
    required this.pendingAppointments,
    required this.cancelledAppointments,
    required this.completedAppointments,
    required this.averageDuration,
    this.mostCommonType,
    this.mostCommonCategory,
  });
}

/// حالة إنشاء موعد جديد
class CreateAppointmentState {
  /// هل العملية جارية؟
  final bool isLoading;

  /// رسالة الخطأ (إن وجدت)
  final String? error;

  /// هل تم إنشاء الموعد بنجاح؟
  final bool isSuccess;

  /// معرف الموعد المنشأ
  final String? appointmentId;

  const CreateAppointmentState({
    this.isLoading = false,
    this.error,
    this.isSuccess = false,
    this.appointmentId,
  });

  CreateAppointmentState copyWith({
    bool? isLoading,
    String? error,
    bool? isSuccess,
    String? appointmentId,
  }) {
    return CreateAppointmentState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isSuccess: isSuccess ?? this.isSuccess,
      appointmentId: appointmentId ?? this.appointmentId,
    );
  }
}

/// فلاتر المواعيد
class AppointmentFilters {
  /// تاريخ البداية
  final DateTime? startDate;

  /// تاريخ النهاية
  final DateTime? endDate;

  /// حالة المواعيد
  final AppointmentStatus? status;

  /// نوع المواعيد
  final AppointmentType? type;

  /// فئة المواعيد
  final AppointmentCategory? category;

  /// أولوية المواعيد
  final AppointmentPriority? priority;

  /// نص البحث
  final String? searchQuery;

  const AppointmentFilters({
    this.startDate,
    this.endDate,
    this.status,
    this.type,
    this.category,
    this.priority,
    this.searchQuery,
  });

  AppointmentFilters copyWith({
    DateTime? startDate,
    DateTime? endDate,
    AppointmentStatus? status,
    AppointmentType? type,
    AppointmentCategory? category,
    AppointmentPriority? priority,
    String? searchQuery,
  }) {
    return AppointmentFilters(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      type: type ?? this.type,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }
}

// ===================================================================
// مدراء الحالة (State Notifiers)
// ===================================================================

/// مدير حالة إنشاء موعد جديد
///
/// يدير عملية إنشاء موعد جديد مع معالجة الأخطاء والتحقق من صحة البيانات
class CreateAppointmentNotifier extends StateNotifier<CreateAppointmentState> {
  /// خدمة إدارة المواعيد
  final AppointmentService _appointmentService;

  CreateAppointmentNotifier(this._appointmentService)
    : super(const CreateAppointmentState());

  /// إنشاء موعد جديد
  ///
  /// يقوم بإنشاء موعد جديد مع جميع التفاصيل المطلوبة
  /// ويعالج الأخطاء ويحدث الحالة وفقاً لذلك
  Future<void> createAppointment({
    required String title,
    required String description,
    required AppointmentType type,
    required AppointmentCategory category,
    required AppointmentPriority priority,
    required DateTime startTime,
    required DateTime endTime,
    required String location,
    required String organizerId,
    required String organizerName,
    required String organizerRole,
    required List<String> participantIds,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    String? meetingLink,
    String? meetingPassword,
    String? notes,
    bool isRecurring = false,
    RecurrencePattern? recurrencePattern,
    DateTime? recurrenceEndDate,
    bool requiresApproval = false,
    String? approvalRequiredFrom,
    List<String> tags = const [],
    bool isPrivate = false,
    bool allowGuestInvites = false,
    int? maxParticipants,
    List<ReminderSetting> reminders = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    // تحديث الحالة لبدء التحميل
    state = state.copyWith(isLoading: true, error: null, isSuccess: false);

    try {
      // إنشاء الموعد باستخدام الخدمة
      final appointmentId = await _appointmentService.createAppointment(
        title: title,
        description: description,
        type: type,
        category: category,
        priority: priority,
        startTime: startTime,
        endTime: endTime,
        location: location,
        organizerId: organizerId,
        organizerName: organizerName,
        organizerRole: organizerRole,
        participantIds: participantIds,
        studentId: studentId,
        studentName: studentName,
        classId: classId,
        className: className,
        meetingLink: meetingLink,
        meetingPassword: meetingPassword,
        notes: notes,
        isRecurring: isRecurring,
        recurrencePattern: recurrencePattern,
        recurrenceEndDate: recurrenceEndDate,
        requiresApproval: requiresApproval,
        approvalRequiredFrom: approvalRequiredFrom,
        tags: tags,
        isPrivate: isPrivate,
        allowGuestInvites: allowGuestInvites,
        maxParticipants: maxParticipants,
        reminders: reminders,
        metadata: metadata,
      );

      // تحديث الحالة عند النجاح
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        appointmentId: appointmentId,
      );
    } catch (e) {
      // تحديث الحالة عند حدوث خطأ
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
        isSuccess: false,
      );
    }
  }

  /// إعادة تعيين الحالة
  ///
  /// يعيد الحالة إلى القيم الافتراضية
  void reset() {
    state = const CreateAppointmentState();
  }
}

/// مدير حالة فلاتر المواعيد
///
/// يدير فلاتر البحث والتصفية للمواعيد
class AppointmentFiltersNotifier extends StateNotifier<AppointmentFilters> {
  AppointmentFiltersNotifier() : super(const AppointmentFilters());

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime? startDate) {
    state = state.copyWith(startDate: startDate);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime? endDate) {
    state = state.copyWith(endDate: endDate);
  }

  /// تحديث حالة المواعيد
  void updateStatus(AppointmentStatus? status) {
    state = state.copyWith(status: status);
  }

  /// تحديث نوع المواعيد
  void updateType(AppointmentType? type) {
    state = state.copyWith(type: type);
  }

  /// تحديث فئة المواعيد
  void updateCategory(AppointmentCategory? category) {
    state = state.copyWith(category: category);
  }

  /// تحديث أولوية المواعيد
  void updatePriority(AppointmentPriority? priority) {
    state = state.copyWith(priority: priority);
  }

  /// تحديث نص البحث
  void updateSearchQuery(String? searchQuery) {
    state = state.copyWith(searchQuery: searchQuery);
  }

  /// إعادة تعيين جميع الفلاتر
  void resetFilters() {
    state = const AppointmentFilters();
  }

  /// تطبيق فلاتر متعددة دفعة واحدة
  void applyFilters({
    DateTime? startDate,
    DateTime? endDate,
    AppointmentStatus? status,
    AppointmentType? type,
    AppointmentCategory? category,
    AppointmentPriority? priority,
    String? searchQuery,
  }) {
    state = AppointmentFilters(
      startDate: startDate,
      endDate: endDate,
      status: status,
      type: type,
      category: category,
      priority: priority,
      searchQuery: searchQuery,
    );
  }
}

// ===================================================================
// الدوال المساعدة
// ===================================================================

/// حساب إحصائيات المواعيد
///
/// يحسب إحصائيات شاملة من قائمة المواعيد المعطاة
AppointmentStats _calculateAppointmentStats(
  List<AppointmentModel> appointments,
) {
  if (appointments.isEmpty) {
    return const AppointmentStats(
      totalAppointments: 0,
      confirmedAppointments: 0,
      pendingAppointments: 0,
      cancelledAppointments: 0,
      completedAppointments: 0,
      averageDuration: 0,
    );
  }

  // حساب عدد المواعيد حسب الحالة
  int confirmed = 0;
  int pending = 0;
  int cancelled = 0;
  int completed = 0;

  // حساب مجموع المدد
  int totalDuration = 0;

  // تتبع أنواع وفئات المواعيد
  final typeCount = <AppointmentType, int>{};
  final categoryCount = <AppointmentCategory, int>{};

  for (final appointment in appointments) {
    // عد المواعيد حسب الحالة
    switch (appointment.status) {
      case AppointmentStatus.confirmed:
        confirmed++;
        break;
      case AppointmentStatus.pending:
        pending++;
        break;
      case AppointmentStatus.cancelled:
        cancelled++;
        break;
      case AppointmentStatus.completed:
        completed++;
        break;
      default:
        break;
    }

    // حساب المدة
    totalDuration += appointment.durationInMinutes;

    // عد الأنواع والفئات
    typeCount[appointment.type] = (typeCount[appointment.type] ?? 0) + 1;
    categoryCount[appointment.category] =
        (categoryCount[appointment.category] ?? 0) + 1;
  }

  // العثور على أكثر نوع وفئة شيوعاً
  AppointmentType? mostCommonType;
  AppointmentCategory? mostCommonCategory;

  if (typeCount.isNotEmpty) {
    mostCommonType =
        typeCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  if (categoryCount.isNotEmpty) {
    mostCommonCategory =
        categoryCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  return AppointmentStats(
    totalAppointments: appointments.length,
    confirmedAppointments: confirmed,
    pendingAppointments: pending,
    cancelledAppointments: cancelled,
    completedAppointments: completed,
    averageDuration: totalDuration / appointments.length,
    mostCommonType: mostCommonType,
    mostCommonCategory: mostCommonCategory,
  );
}
