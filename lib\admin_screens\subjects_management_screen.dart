import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/subject_form_dialog.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/subject_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/providers/subject_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class SubjectsManagementScreen extends ConsumerWidget {
  const SubjectsManagementScreen({super.key});

  void _showSubjectDialog(BuildContext context, {Subject? subject}) {
    showDialog(
      context: context,
      builder: (context) => SubjectFormDialog(subject: subject),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final subjectsAsync = ref.watch(subjectsStreamProvider);
    final classesAsync = ref.watch(classesForSubjectsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المواد الدراسية'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          IconButton(
            icon: const Icon(Icons.analytics),
            tooltip: 'إحصائيات المواد',
            onPressed: () => _showSubjectStatistics(context, ref),
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير البيانات',
            onPressed: () => _exportSubjectData(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: subjectsAsync.when(
              data:
                  (subjects) => classesAsync.when(
                    data:
                        (classes) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            _buildQuickStat(
                              'إجمالي المواد',
                              '${subjects.length}',
                              Icons.book,
                              Colors.blue,
                            ),
                            _buildQuickStat(
                              'المواد العامة',
                              '${subjects.where((s) => s.classId == 'all').length}',
                              Icons.public,
                              Colors.green,
                            ),
                            _buildQuickStat(
                              'المواد المخصصة',
                              '${subjects.where((s) => s.classId != 'all').length}',
                              Icons.class_,
                              Colors.orange,
                            ),
                            _buildQuickStat(
                              'عدد الفصول',
                              '${classes.length}',
                              Icons.school,
                              Colors.purple,
                            ),
                          ],
                        ),
                    loading: () => const SizedBox.shrink(),
                    error: (_, __) => const SizedBox.shrink(),
                  ),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: subjectsAsync.when(
              data:
                  (subjects) => classesAsync.when(
                    data: (classes) {
                      final classMap = {for (var c in classes) c.id: c.name};
                      classMap['all'] = 'مادة عامة';

                      if (subjects.isEmpty) {
                        return _buildEmptyState(context);
                      }

                      return SingleChildScrollView(
                        padding: const EdgeInsets.all(16),
                        child: Card(
                          elevation: 2,
                          child: SingleChildScrollView(
                            scrollDirection: Axis.horizontal,
                            child: DataTable(
                              headingRowColor: MaterialStateProperty.all(
                                Colors.grey.shade100,
                              ),
                              columns: const [
                                DataColumn(
                                  label: Text(
                                    'اسم المادة',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'الصف',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                DataColumn(
                                  label: Text(
                                    'إجراءات',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                              rows:
                                  subjects.map((subject) {
                                    final className =
                                        classMap[subject.classId] ??
                                        subject.classId;
                                    return DataRow(
                                      cells: [
                                        DataCell(Text(subject.name)),
                                        DataCell(Text(className)),
                                        DataCell(
                                          Row(
                                            children: [
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.edit,
                                                  color: Colors.blue,
                                                ),
                                                onPressed:
                                                    () => _showSubjectDialog(
                                                      context,
                                                      subject: subject,
                                                    ),
                                              ),
                                              IconButton(
                                                icon: const Icon(
                                                  Icons.delete,
                                                  color: Colors.red,
                                                ),
                                                onPressed: () async {
                                                  // Optional: Add a confirmation dialog here
                                                  try {
                                                    await ref
                                                        .read(
                                                          firebaseServiceProvider,
                                                        )
                                                        .deleteSubject(
                                                          subject.id,
                                                        );
                                                    ScaffoldMessenger.of(
                                                      context,
                                                    ).showSnackBar(
                                                      const SnackBar(
                                                        content: Text(
                                                          'تم حذف المادة بنجاح',
                                                        ),
                                                        backgroundColor:
                                                            Colors.green,
                                                      ),
                                                    );
                                                  } catch (e) {
                                                    ScaffoldMessenger.of(
                                                      context,
                                                    ).showSnackBar(
                                                      SnackBar(
                                                        content: Text(
                                                          'فشل حذف المادة: $e',
                                                        ),
                                                        backgroundColor:
                                                            Colors.red,
                                                      ),
                                                    );
                                                  }
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                            ),
                          ),
                        ),
                      );
                    },
                    loading: () => const LoadingIndicator(),
                    error:
                        (err, stack) =>
                            ErrorMessage(message: 'فشل تحميل الفصول: $err'),
                  ),
              loading: () => const LoadingIndicator(),
              error:
                  (err, stack) =>
                      ErrorMessage(message: 'فشل تحميل المواد: $err'),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showSubjectDialog(context),
        tooltip: 'إضافة مادة جديدة',
        icon: const Icon(Icons.add),
        label: const Text('إضافة مادة'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withOpacity(0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.book, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد مواد دراسية',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أضف مادة جديدة للبدء',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _showSubjectDialog(context),
            icon: const Icon(Icons.add),
            label: const Text('إضافة مادة جديدة'),
          ),
        ],
      ),
    );
  }

  /// عرض إحصائيات المواد
  void _showSubjectStatistics(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.6,
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.analytics, color: Colors.blue),
                      const SizedBox(width: 12),
                      const Text(
                        'إحصائيات المواد الدراسية',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const Divider(),

                  const Expanded(
                    child: Center(
                      child: Text(
                        'ميزة الإحصائيات التفصيلية قيد التطوير',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// تصدير بيانات المواد
  void _exportSubjectData(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ تصدير بيانات المواد
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التصدير قيد التطوير')));
  }
}
