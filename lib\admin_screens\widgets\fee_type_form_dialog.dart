import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/fee_type_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

class FeeTypeFormDialog extends ConsumerStatefulWidget {
  final FeeTypeModel? feeType;

  const FeeTypeFormDialog({super.key, this.feeType});

  @override
  ConsumerState<FeeTypeFormDialog> createState() => _FeeTypeFormDialogState();
}

class _FeeTypeFormDialogState extends ConsumerState<FeeTypeFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _amountController;
  late TextEditingController _descriptionController;
  late bool _isRequired;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    final feeType = widget.feeType;
    _nameController = TextEditingController(text: feeType?.name ?? '');
    _amountController = TextEditingController(text: feeType?.defaultValue.toString() ?? '');
    _descriptionController = TextEditingController(text: feeType?.description ?? '');
    _isRequired = feeType?.isRequired ?? false;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _amountController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        final firebaseService = ref.read(firebaseServiceProvider);
        final feeTypeModel = FeeTypeModel(
          id: widget.feeType?.id ?? '', // ID is ignored on add
          name: _nameController.text,
          defaultValue: double.parse(_amountController.text),
          description: _descriptionController.text,
          isRequired: _isRequired,
        );

        if (widget.feeType == null) {
          await firebaseService.addFeeType(feeTypeModel);
        } else {
          await firebaseService.updateFeeType(feeTypeModel);
        }

        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('تم حفظ نوع الرسوم بنجاح')),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('حدث خطأ: ${e.toString()}')),
          );
        }
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.feeType == null ? 'إضافة نوع رسوم جديد' : 'تعديل نوع الرسوم'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'اسم الرسوم'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الاسم مطلوب';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _amountController,
                decoration: const InputDecoration(labelText: 'القيمة الافتراضية'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty || double.tryParse(value) == null) {
                    return 'أدخل مبلغًا صحيحًا';
                  }
                  return null;
                },
              ),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'الوصف (اختياري)'),
                maxLines: 3,
              ),
              CheckboxListTile(
                title: const Text('إلزامي'),
                value: _isRequired,
                onChanged: (bool? value) {
                  setState(() {
                    _isRequired = value ?? false;
                  });
                },
              ),
            ],
          ),
        ),
      ),
      actions: [
        if (_isLoading) const CircularProgressIndicator(),
        if (!_isLoading) ...[
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _submit,
            child: const Text('حفظ'),
          ),
        ],
      ],
    );
  }
}
