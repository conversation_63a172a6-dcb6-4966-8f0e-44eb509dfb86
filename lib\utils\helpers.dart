import 'package:flutter/material.dart';

/// يعرض SnackBar بنجاح.
void showSuccessSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.green,
    ),
  );
}

/// يعرض SnackBar بخطأ.
void showErrorSnackBar(BuildContext context, String message) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      content: Text(message),
      backgroundColor: Colors.red,
    ),
  );
}

/// يعرض ديالوج تأكيد.
Future<bool> showConfirmationDialog({
  required BuildContext context,
  required String title,
  required String content,
  String confirmText = 'تأكيد',
}) async {
  final result = await showDialog<bool>(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: <Widget>[
          TextButton(
            child: const Text('إلغاء'),
            onPressed: () {
              Navigator.of(dialogContext).pop(false); // إرجاع false
            },
          ),
          ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: Text(confirmText),
            onPressed: () {
              Navigator.of(dialogContext).pop(true); // إرجاع true
            },
          ),
        ],
      );
    },
  );
  return result ?? false; // إرجاع false إذا تم إغلاق الديالوج بدون اختيار
}
