
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// ويدجت مشترك لعرض قائمة منسدلة بالمعلمين
class TeacherDropdown extends StatelessWidget {
  final String? selectedValue;
  final void Function(String?) onChanged;
  final String? Function(String?)? validator;

  const TeacherDropdown({
    super.key,
    required this.selectedValue,
    required this.onChanged,
    this.validator,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: 'teacher')
          .snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const Center(child: CircularProgressIndicator());
        }

        final teachers = snapshot.data!.docs.map((doc) {
          final teacherName = doc['name'] as String;
          final teacherId = doc.id;
          return DropdownMenuItem<String>(
            value: teacherId,
            child: Text(teacherName),
          );
        }).toList();

        return DropdownButtonFormField<String>(
          value: selectedValue,
          onChanged: onChanged,
          validator: validator ?? (value) => value == null ? 'الرجاء اختيار المعلم' : null,
          items: teachers,
          decoration: const InputDecoration(
            labelText: 'المعلم',
            border: OutlineInputBorder(),
          ),
          hint: const Text('اختر المعلم'),
        );
      },
    );
  }
}
