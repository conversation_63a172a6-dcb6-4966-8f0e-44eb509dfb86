import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/attendance_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/services/firebase_service.dart';

//======================================================================
// Providers for Admin Attendance Management Screen
// (مزودات الخدمة الخاصة بشاشة إدارة الحضور في لوحة التحكم)
//======================================================================

/// مزود حالة لتخزين نص البحث المدخل من قبل المسؤول لفلترة الطلاب.
/// يتم التخلص منه تلقائيًا عند عدم الحاجة إليه للحفاظ على الذاكرة.
final attendanceSearchQueryProvider = StateProvider.autoDispose<String>((ref) {
  // القيمة الأولية هي نص فارغ
  return '';
});

/// مزود حالة لتخزين التاريخ المختار لفلترة الحضور
final selectedAttendanceDateProvider = StateProvider<DateTime?>((ref) => null);

/// مزود حالة لتخزين حالة الحضور المختارة للفلترة
final selectedAttendanceStatusProvider = StateProvider<AttendanceStatus?>(
  (ref) => null,
);

/// مزود يقوم بفلترة قائمة الطلاب بناءً على الفصل المختار ونص البحث والمرشحات.
/// يعتمد على `classStudentsStreamProvider` لجلب الطلاب و `attendanceSearchQueryProvider` لنص البحث.
final filteredAttendanceStudentsProvider =
    Provider.autoDispose<List<StudentModel>>((ref) {
      // مراقبة قائمة الطلاب القادمة من الـ stream الخاص بالفصل المختار
      final classId = ref.watch(selectedClassIdProvider);
      if (classId == null) return [];

      final students =
          ref.watch(classStudentsStreamProvider(classId)).asData?.value ?? [];

      // مراقبة نص البحث وتحويله إلى أحرف صغيرة لتسهيل المقارنة
      final searchQuery =
          ref.watch(attendanceSearchQueryProvider).toLowerCase();

      var filteredStudents = students;

      // تطبيق البحث النصي
      if (searchQuery.isNotEmpty) {
        filteredStudents =
            filteredStudents.where((student) {
              return student.name.toLowerCase().contains(searchQuery) ||
                  student.studentNumber.toLowerCase().contains(searchQuery);
            }).toList();
      }

      // ترتيب الطلاب حسب الاسم
      filteredStudents.sort((a, b) => a.name.compareTo(b.name));

      return filteredStudents;
    });

//======================================================================
// Providers for Student Attendance Screen
// (مزودات الخدمة الخاصة بشاشة عرض الحضور للطالب في تطبيق الموبايل)
//======================================================================

/// مزود حالة لإدارة اليوم الذي يتم التركيز عليه في التقويم.
/// القيمة الأولية هي تاريخ اليوم الحالي.
final focusedDayProvider = StateProvider.autoDispose<DateTime>((ref) {
  return DateTime.now();
});

/// مزود حالة لإدارة اليوم الذي تم اختياره من قبل المستخدم في التقويم.
/// القيمة الأولية هي تاريخ اليوم الحالي.
final selectedDayProvider = StateProvider.autoDispose<DateTime?>((ref) {
  return DateTime.now();
});

/// مزود Stream محسن لجلب سجل الحضور مع النموذج الجديد
/// يحول البيانات الخام إلى كائنات AttendanceRecord منظمة
final studentAttendanceProvider = StreamProvider.autoDispose
    .family<List<AttendanceRecord>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);

      // جلب البيانات الخام وتحويلها إلى نماذج منظمة
      return firebaseService.getStudentAttendance(studentId).map((rawRecords) {
        return rawRecords
            .map((data) => AttendanceRecord.fromMap(data))
            .toList();
      });
    });

/// مزود لإحصائيات الحضور الشاملة
/// يحسب جميع الإحصائيات المفيدة من سجل الحضور
final attendanceStatsProvider = Provider.autoDispose
    .family<AttendanceStats?, String>((ref, studentId) {
      final attendanceRecords =
          ref.watch(studentAttendanceProvider(studentId)).asData?.value;
      return attendanceRecords != null
          ? AttendanceStats(records: attendanceRecords)
          : null;
    });

/// مزود لسجلات الحضور المفلترة حسب الشهر
/// يعرض سجلات شهر معين فقط
final monthlyAttendanceProvider = Provider.autoDispose.family<
  List<AttendanceRecord>,
  ({String studentId, int year, int month})
>((ref, params) {
  final attendanceRecords =
      ref.watch(studentAttendanceProvider(params.studentId)).asData?.value ??
      [];

  return attendanceRecords.where((record) {
    return record.date.year == params.year && record.date.month == params.month;
  }).toList();
});

/// مزود لسجلات الحضور المفلترة حسب الأسبوع
/// يعرض سجلات أسبوع معين فقط
final weeklyAttendanceProvider = Provider.autoDispose.family<
  List<AttendanceRecord>,
  ({String studentId, DateTime weekStart})
>((ref, params) {
  final attendanceRecords =
      ref.watch(studentAttendanceProvider(params.studentId)).asData?.value ??
      [];
  final weekEnd = params.weekStart.add(const Duration(days: 6));

  return attendanceRecords.where((record) {
    return record.date.isAfter(
          params.weekStart.subtract(const Duration(days: 1)),
        ) &&
        record.date.isBefore(weekEnd.add(const Duration(days: 1)));
  }).toList();
});

/// مزود لحالة عرض الحضور (شهري، أسبوعي، إحصائيات)
final attendanceViewModeProvider = StateProvider<AttendanceViewMode>(
  (ref) => AttendanceViewMode.monthly,
);

/// مزود للشهر المختار في عرض الحضور
final selectedMonthProvider = StateProvider<DateTime>((ref) => DateTime.now());

/// مزود للأسبوع المختار في عرض الحضور
final selectedWeekProvider = StateProvider<DateTime>((ref) {
  final now = DateTime.now();
  // العثور على بداية الأسبوع (الأحد)
  final weekday = now.weekday == 7 ? 0 : now.weekday;
  return now.subtract(Duration(days: weekday));
});

/// مزود لحالة البحث في سجل الحضور
final attendanceSearchProvider = StateProvider<String>((ref) => '');

/// مزود للسجلات المفلترة بناءً على البحث
final filteredAttendanceProvider = Provider.autoDispose
    .family<List<AttendanceRecord>, String>((ref, studentId) {
      final attendanceRecords =
          ref.watch(studentAttendanceProvider(studentId)).asData?.value ?? [];
      final searchQuery =
          ref.watch(attendanceSearchProvider).toLowerCase().trim();

      if (searchQuery.isEmpty) {
        return attendanceRecords;
      }

      return attendanceRecords.where((record) {
        return record.status.arabicName.toLowerCase().contains(searchQuery) ||
            (record.notes?.toLowerCase().contains(searchQuery) ?? false) ||
            record.date.toString().contains(searchQuery);
      }).toList();
    });

/// مزود لسجل الحضور اليومي (اليوم الحالي)
final todayAttendanceProvider = Provider.autoDispose
    .family<AttendanceRecord?, String>((ref, studentId) {
      final attendanceRecords =
          ref.watch(studentAttendanceProvider(studentId)).asData?.value ?? [];
      final today = DateTime.now();

      try {
        return attendanceRecords.firstWhere((record) {
          return record.date.year == today.year &&
              record.date.month == today.month &&
              record.date.day == today.day;
        });
      } catch (e) {
        return null; // لا يوجد سجل لليوم الحالي
      }
    });

/// مزود لإحصائيات الحضور الشهرية
final monthlyStatsProvider = Provider.autoDispose
    .family<Map<String, int>, ({String studentId, int year, int month})>((
      ref,
      params,
    ) {
      final stats = ref.watch(attendanceStatsProvider(params.studentId));
      return stats?.getMonthlyStats(params.year, params.month) ?? {};
    });

/// مزود لحالة التقويم (اليوم المركز عليه)
final calendarFocusedDayProvider = StateProvider.autoDispose<DateTime>(
  (ref) => DateTime.now(),
);

/// مزود لحالة التقويم (اليوم المختار)
final calendarSelectedDayProvider = StateProvider.autoDispose<DateTime?>(
  (ref) => DateTime.now(),
);

/// مزود لنمط عرض التقويم
final calendarFormatProvider = StateProvider<CalendarFormat>(
  (ref) => CalendarFormat.month,
);

/// تعداد لأنماط عرض الحضور
enum AttendanceViewMode {
  monthly('شهري'),
  weekly('أسبوعي'),
  statistics('إحصائيات'),
  calendar('تقويم');

  const AttendanceViewMode(this.arabicName);
  final String arabicName;
}

/// تعداد لأنماط عرض التقويم
enum CalendarFormat {
  month('شهري'),
  twoWeeks('أسبوعين'),
  week('أسبوعي');

  const CalendarFormat(this.arabicName);
  final String arabicName;
}
