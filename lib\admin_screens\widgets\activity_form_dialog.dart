import 'dart:typed_data';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

class ActivityFormDialog extends ConsumerStatefulWidget {
  final ActivityModel? activity;

  const ActivityFormDialog({super.key, this.activity});

  @override
  ConsumerState<ActivityFormDialog> createState() => _ActivityFormDialogState();
}

class _ActivityFormDialogState extends ConsumerState<ActivityFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _videoUrlController;
  DateTime? _selectedDate;
  final List<XFile> _newImages = [];
  late List<String> _existingImageUrls;

  @override
  void initState() {
    super.initState();
    final activity = widget.activity;
    _titleController = TextEditingController(text: activity?.title);
    _descriptionController = TextEditingController(text: activity?.description);
    _videoUrlController = TextEditingController(text: activity?.videoUrl);
    _selectedDate = activity?.date;
    _existingImageUrls = List.from(activity?.imageUrls ?? []);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _videoUrlController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate() && _selectedDate != null) {
      showDialog(
        context: context,
        builder: (_) => const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      final firebaseService = ref.read(firebaseServiceProvider);
      final isEditing = widget.activity != null;

      try {
        final finalImageUrls = await _manageImages();

        final activityData = {
          'title': _titleController.text,
          'description': _descriptionController.text,
          'date': _selectedDate,
          'videoUrl': _videoUrlController.text,
          'imageUrls': finalImageUrls,
          'attachments': [],
        };

        if (isEditing) {
          await firebaseService.updateDocument(
              'activities', widget.activity!.id, activityData);
        } else {
          activityData['createdAt'] = FieldValue.serverTimestamp();
          await firebaseService.addDocument('activities', activityData);
        }

        Navigator.of(context).pop(); // Close loading
        Navigator.of(context).pop(); // Close dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing
                ? 'تم تحديث النشاط بنجاح'
                : 'تم إضافة النشاط بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        Navigator.of(context).pop(); // Close loading
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<List<String>> _manageImages() async {
    final firebaseService = ref.read(firebaseServiceProvider);
    final isEditing = widget.activity != null;
    List<String> finalImageUrls = List.from(_existingImageUrls);

    if (isEditing) {
      final originalUrls = Set<String>.from(widget.activity!.imageUrls);
      final currentUrls = Set<String>.from(_existingImageUrls);
      final urlsToDelete = originalUrls.difference(currentUrls);
      for (final url in urlsToDelete) {
        await firebaseService.deleteImage(url);
      }
    }

    if (_newImages.isNotEmpty) {
      final newUploadedUrls = await firebaseService.uploadMultipleImages(
          _newImages, 'activity_images');
      finalImageUrls.addAll(newUploadedUrls);
    }

    return finalImageUrls;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.activity == null ? 'إضافة نشاط جديد' : 'تعديل النشاط'),
      content: Form(
        key: _formKey,
        child: SizedBox(
          width: double.maxFinite,
          height: MediaQuery.of(context).size.height * 0.7,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(labelText: 'عنوان النشاط'),
                  validator: (v) => v!.isEmpty ? 'العنوان مطلوب' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(labelText: 'وصف تفصيلي للنشاط'),
                  maxLines: 4,
                  validator: (v) => v!.isEmpty ? 'الوصف مطلوب' : null,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _videoUrlController,
                  decoration: const InputDecoration(labelText: 'رابط فيديو (اختياري)'),
                ),
                const SizedBox(height: 20),
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        _selectedDate == null
                            ? 'الرجاء اختيار تاريخ النشاط'
                            : 'التاريخ: ${DateFormat.yMMMd('ar').format(_selectedDate!)}',
                        style: TextStyle(
                          color: _selectedDate == null ? Colors.red : null,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.calendar_today),
                      onPressed: () async {
                        final pickedDate = await showDatePicker(
                          context: context,
                          initialDate: _selectedDate ?? DateTime.now(),
                          firstDate: DateTime.now(),
                          lastDate: DateTime(2101),
                        );
                        if (pickedDate != null) {
                          setState(() => _selectedDate = pickedDate);
                        }
                      },
                    ),
                  ],
                ),
                const Divider(height: 20),
                const Text('صور النشاط', style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildImageManager(),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: Text(widget.activity == null ? 'إضافة' : 'حفظ التعديلات'),
        ),
      ],
    );
  }

  Widget _buildImageManager() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_existingImageUrls.isEmpty && _newImages.isEmpty)
          const Center(child: Text('لم يتم اختيار صور بعد.')),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 100,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _existingImageUrls.length + _newImages.length,
          itemBuilder: (context, index) {
            Widget imageWidget;
            bool isExistingUrl = index < _existingImageUrls.length;

            if (isExistingUrl) {
              imageWidget = Image.network(_existingImageUrls[index], fit: BoxFit.cover);
            } else {
              final fileIndex = index - _existingImageUrls.length;
              imageWidget = FutureBuilder<Uint8List>(
                future: _newImages[fileIndex].readAsBytes(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return Image.memory(snapshot.data!, fit: BoxFit.cover);
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                },
              );
            }

            return Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: imageWidget,
                ),
                Positioned(
                  top: -8,
                  right: -8,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: Colors.red,
                      radius: 12,
                      child: Icon(Icons.close, color: Colors.white, size: 14),
                    ),
                    onPressed: () {
                      setState(() {
                        if (isExistingUrl) {
                          _existingImageUrls.removeAt(index);
                        } else {
                          _newImages.removeAt(index - _existingImageUrls.length);
                        }
                      });
                    },
                  ),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 16),
        Center(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add_a_photo),
            label: const Text('اختيار صور'),
            onPressed: () async {
              final pickedImages =
                  await ref.read(firebaseServiceProvider).pickMultipleImages();
              if (pickedImages.isNotEmpty) {
                setState(() {
                  _newImages.addAll(pickedImages);
                });
              }
            },
          ),
        ),
      ],
    );
  }
}
