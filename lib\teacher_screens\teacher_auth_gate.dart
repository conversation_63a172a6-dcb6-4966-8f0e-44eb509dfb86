import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:school_management_system/teacher_screens/teacher_main_layout.dart';
import 'package:school_management_system/teacher_screens/teacher_login_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// بوابة المصادقة للمعلمين
/// 
/// هذا الويدجت يتحقق من حالة تسجيل الدخول للمعلم
/// ويوجهه إلى الواجهة المناسبة:
/// - إذا لم يكن مسجل دخول: شاشة تسجيل الدخول
/// - إذا كان مسجل دخول وله دور معلم: لوحة تحكم المعلم
/// - إذا كان مسجل دخول بدور آخر: رسالة خطأ
/// 
/// الأمان:
/// - التحقق من دور المستخدم في قاعدة البيانات
/// - منع الوصول غير المصرح به
/// - تسجيل خروج تلقائي للمستخدمين غير المصرح لهم
class TeacherAuthGate extends StatelessWidget {
  const TeacherAuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseService().authStateChanges,
      builder: (context, authSnapshot) {
        // في حالة انتظار التحقق من حالة المصادقة
        if (authSnapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: Center(child: LoadingIndicator()),
          );
        }

        // إذا كان المستخدم مسجل دخوله
        if (authSnapshot.hasData) {
          // التحقق من صلاحيات المستخدم
          return FutureBuilder<String?>(
            future: FirebaseService().getUserRole(authSnapshot.data!.uid),
            builder: (context, roleSnapshot) {
              // في حالة انتظار التحقق من الدور
              if (roleSnapshot.connectionState == ConnectionState.waiting) {
                return const Scaffold(
                  body: Center(child: LoadingIndicator()),
                );
              }

              // إذا كان المستخدم معلم، اسمح له بالدخول
              if (roleSnapshot.hasData && roleSnapshot.data == 'teacher') {
                return const TeacherMainLayout();
              }

              // إذا لم يكن معلم أو حدث خطأ، اعرض رسالة وارجعه لشاشة الدخول
              return Scaffold(
                appBar: AppBar(
                  title: const Text('خطأ في الصلاحيات'),
                  backgroundColor: Colors.red[600],
                ),
                body: Center(
                  child: Padding(
                    padding: const EdgeInsets.all(24.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.error_outline,
                          size: 64,
                          color: Colors.red[400],
                        ),
                        const SizedBox(height: 24),
                        const Text(
                          'ليس لديك صلاحية للوصول إلى لوحة تحكم المعلمين',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'هذه الصفحة مخصصة للمعلمين فقط. إذا كنت معلماً، تأكد من أن حسابك مفعل بالصلاحيات المناسبة.',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 32),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // زر تسجيل الخروج
                            ElevatedButton.icon(
                              onPressed: () {
                                FirebaseService().signOut();
                              },
                              icon: const Icon(Icons.logout),
                              label: const Text('تسجيل الخروج'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.red[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                              ),
                            ),
                            const SizedBox(width: 16),
                            
                            // زر المساعدة
                            OutlinedButton.icon(
                              onPressed: () {
                                _showHelpDialog(context);
                              },
                              icon: const Icon(Icons.help_outline),
                              label: const Text('المساعدة'),
                              style: OutlinedButton.styleFrom(
                                foregroundColor: Colors.blue[600],
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        }

        // إذا لم يكن مسجل دخول، اعرض شاشة تسجيل الدخول
        return const TeacherLoginScreen();
      },
    );
  }

  /// عرض حوار المساعدة
  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(Icons.help_outline, color: Colors.blue),
            SizedBox(width: 8),
            Text('المساعدة'),
          ],
        ),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إذا كنت معلماً ولا تستطيع الوصول:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 12),
            Text('• تأكد من أن حسابك مفعل من قبل الإدارة'),
            SizedBox(height: 8),
            Text('• تحقق من أن دورك محدد كـ "معلم" في النظام'),
            SizedBox(height: 8),
            Text('• تواصل مع الإدارة لتفعيل حسابك'),
            SizedBox(height: 8),
            Text('• تأكد من استخدام البريد الإلكتروني الصحيح'),
            SizedBox(height: 16),
            Text(
              'للمساعدة الفنية:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text('تواصل مع قسم تقنية المعلومات في المدرسة'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('موافق'),
          ),
        ],
      ),
    );
  }
}
