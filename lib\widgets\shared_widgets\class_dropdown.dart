import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

class ClassDropdown extends StatelessWidget {
  final String? selectedValue;
  final Function(String?) onChanged;

  const ClassDropdown({
    super.key,
    required this.selectedValue,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance.collection('classes').orderBy('name').snapshots(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        var classes = snapshot.data!.docs.map((doc) {
          return DropdownMenuItem<String>(
            value: doc.id,
            child: Text(doc['name']),
          );
        }).toList();

        return DropdownButtonFormField<String>(
          value: selectedValue,
          onChanged: onChanged,
          items: classes,
          decoration: const InputDecoration(
            labelText: 'الصف',
            border: OutlineInputBorder(),
          ),
          validator: (value) => value == null ? 'الرجاء اختيار الصف' : null,
        );
      },
    );
  }
}
