import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/fee_type_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة جميع الطلاب لعرضهم في شاشة الرسوم.
final studentsForFeesProvider = StreamProvider.autoDispose<List<StudentModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // Assuming a method getStudentsStream exists in FirebaseService
  // This might need to be created.
  return firebaseService.getCollection(
    path: 'students',
    builder: (data, id) => StudentModel.fromMap(data, id),
  );
});

/// Provider لتخزين نص البحث عن الطلاب.
final feeSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لفلترة قائمة الطلاب بناءً على نص البحث.
final filteredStudentsForFeesProvider = Provider.autoDispose<List<StudentModel>>((ref) {
  final studentsAsyncValue = ref.watch(studentsForFeesProvider);
  final searchQuery = ref.watch(feeSearchQueryProvider).toLowerCase();

  return studentsAsyncValue.when(
    data: (students) {
      if (searchQuery.isEmpty) {
        return students;
      }
      return students.where((student) {
        final nameMatches = student.name.toLowerCase().contains(searchQuery);
        final numberMatches = student.studentNumber?.contains(searchQuery) ?? false;
        return nameMatches || numberMatches;
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider لجلب التفاصيل المالية لطالب معين.
/// يستخدم family للسماح بتمرير studentId.
final studentFinancialDetailsProvider = StreamProvider.autoDispose.family<Map<String, dynamic>, String>((ref, studentId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getStudentFinancialDetails(studentId);
});

// ================== Fee Types Providers ==================

/// Provider لجلب قائمة أنواع الرسوم.
final feeTypesStreamProvider = StreamProvider.autoDispose<List<FeeTypeModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getFeeTypesStream();
});

/// Provider لتخزين نص البحث لأنواع الرسوم.
final feeTypeSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لفلترة قائمة أنواع الرسوم بناءً على نص البحث.
final filteredFeeTypesProvider = Provider.autoDispose<List<FeeTypeModel>>((ref) {
  final feeTypesAsyncValue = ref.watch(feeTypesStreamProvider);
  final searchQuery = ref.watch(feeTypeSearchQueryProvider).toLowerCase();

  return feeTypesAsyncValue.when(
    data: (feeTypes) {
      if (searchQuery.isEmpty) {
        return feeTypes;
      }
      return feeTypes.where((feeType) {
        return feeType.name.toLowerCase().contains(searchQuery);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
