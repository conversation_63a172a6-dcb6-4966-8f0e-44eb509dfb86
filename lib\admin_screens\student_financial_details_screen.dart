import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class StudentFinancialDetailsScreen extends StatefulWidget {
  final StudentModel student;
  const StudentFinancialDetailsScreen({Key? key, required this.student})
    : super(key: key);
  @override
  _StudentFinancialDetailsScreenState createState() =>
      _StudentFinancialDetailsScreenState();
}

class _StudentFinancialDetailsScreenState
    extends State<StudentFinancialDetailsScreen> {
  final double totalFees = 5000; // Example: Total annual fees

  void _showAddPaymentDialog() {
    final amountController = TextEditingController();
    final notesController = TextEditingController();
    DateTime selectedDate = DateTime.now();

    showDialog(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setDialogState) {
            return AlertDialog(
              title: const Text('تسجيل دفعة جديدة'),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: amountController,
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(labelText: 'مبلغ الدفعة'),
                  ),
                  TextField(
                    controller: notesController,
                    decoration: const InputDecoration(
                      labelText: 'ملاحظات (اختياري)',
                    ),
                  ),
                  const SizedBox(height: 20),
                  Row(
                    children: [
                      Text(
                        'تاريخ الدفعة: ${DateFormat.yMd().format(selectedDate)}',
                      ),
                      const Spacer(),
                      IconButton(
                        icon: const Icon(Icons.calendar_today),
                        onPressed: () async {
                          final pickedDate = await showDatePicker(
                            context: context,
                            initialDate: selectedDate,
                            firstDate: DateTime(2020),
                            lastDate: DateTime.now(),
                          );
                          if (pickedDate != null) {
                            setDialogState(() {
                              selectedDate = pickedDate;
                            });
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('إلغاء'),
                ),
                ElevatedButton(
                  onPressed: () {
                    final amount = double.tryParse(amountController.text);
                    if (amount != null && amount > 0) {
                      FirebaseFirestore.instance
                          .collection('students')
                          .doc(widget.student.id)
                          .collection('payments')
                          .add({
                            'amount': amount,
                            'date': Timestamp.fromDate(selectedDate),
                            'notes': notesController.text,
                            'recordedBy':
                                'admin', // ToDo: Add current admin name
                          });
                      Navigator.of(context).pop();
                    }
                  },
                  child: const Text('حفظ'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text('السجل المالي لـ ${widget.student.name}')),
      body: StreamBuilder<QuerySnapshot>(
        stream:
            FirebaseFirestore.instance
                .collection('students')
                .doc(widget.student.id)
                .collection('payments')
                .orderBy('date', descending: true)
                .snapshots(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const LoadingIndicator();
          }
          final payments = snapshot.data?.docs ?? [];
          final double totalPaid = payments.fold(
            0,
            (sum, doc) => sum + (doc['amount'] ?? 0),
          );
          final double remaining = totalFees - totalPaid;
          return Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: GridView.count(
                  crossAxisCount: 3,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  children: [
                    _buildFinancialSummaryCard(
                      'إجمالي الرسوم',
                      totalFees,
                      Colors.blue,
                    ),
                    _buildFinancialSummaryCard(
                      'إجمالي المدفوع',
                      totalPaid,
                      Colors.green,
                    ),
                    _buildFinancialSummaryCard(
                      'المبلغ المتبقي',
                      remaining,
                      Colors.red,
                    ),
                  ],
                ),
              ),
              const Divider(),
              Expanded(
                child: ListView.builder(
                  itemCount: payments.length,
                  itemBuilder: (context, index) {
                    final payment = payments[index];
                    final paymentDate = (payment['date'] as Timestamp).toDate();
                    final notes = payment['notes'] as String? ?? '';
                    return ListTile(
                      leading: const Icon(Icons.payment),
                      title: Text('مبلغ: ${payment['amount']} د.أ'),
                      subtitle: Text(
                        'تاريخ: ${DateFormat.yMd().format(paymentDate)}'
                        '${notes.isNotEmpty ? "\nملاحظات: $notes" : ""}',
                      ),
                      isThreeLine: notes.isNotEmpty,
                    );
                  },
                ),
              ),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddPaymentDialog,
        child: const Icon(Icons.add),
        tooltip: 'تسجيل دفعة جديدة',
      ),
    );
  }

  Widget _buildFinancialSummaryCard(String title, double amount, Color color) {
    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              amount.toStringAsFixed(2),
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(title, textAlign: TextAlign.center),
          ],
        ),
      ),
    );
  }
}
