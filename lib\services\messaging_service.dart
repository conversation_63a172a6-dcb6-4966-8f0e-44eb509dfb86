import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:school_management_system/models/conversation_model.dart';
import 'package:school_management_system/models/message_model.dart';
import 'dart:io';

/// خدمة التواصل المتقدمة
/// 
/// تدير جميع عمليات التواصل بين أولياء الأمور والمدرسة
/// تشمل إنشاء المحادثات، إرسال الرسائل، إدارة الملفات المرفقة
/// 
/// الميزات الرئيسية:
/// - إدارة المحادثات متعددة الأطراف
/// - إرسال واستقبال الرسائل في الوقت الفعلي
/// - دعم الملفات المرفقة والصور
/// - تتبع حالة القراءة والتسليم
/// - البحث في المحادثات والرسائل
/// - الإشعارات الفورية
/// - أرشفة وتنظيم المحادثات
class MessagingService {
  /// مرجع مجموعة المحادثات في Firestore
  final CollectionReference _conversationsRef = 
      FirebaseFirestore.instance.collection('conversations');
  
  /// مرجع مجموعة الرسائل في Firestore
  final CollectionReference _messagesRef = 
      FirebaseFirestore.instance.collection('messages');
  
  /// مرجع Firebase Storage للملفات المرفقة
  final FirebaseStorage _storage = FirebaseStorage.instance;

  // ===================================================================
  // إدارة المحادثات
  // ===================================================================

  /// إنشاء محادثة جديدة
  /// 
  /// [subject] عنوان المحادثة
  /// [description] وصف المحادثة
  /// [type] نوع المحادثة
  /// [priority] أولوية المحادثة
  /// [participantIds] قائمة معرفات المشاركين
  /// [participants] تفاصيل المشاركين
  /// [createdBy] معرف منشئ المحادثة
  /// [studentId] معرف الطالب المرتبط (اختياري)
  /// [studentName] اسم الطالب المرتبط (اختياري)
  Future<String> createConversation({
    required String subject,
    required String description,
    required ConversationType type,
    required ConversationPriority priority,
    required List<String> participantIds,
    required Map<String, ParticipantInfo> participants,
    required String createdBy,
    String? studentId,
    String? studentName,
    List<String> tags = const [],
  }) async {
    try {
      final now = DateTime.now();
      
      final conversation = ConversationModel(
        id: '', // سيتم تعيينه تلقائياً
        subject: subject,
        description: description,
        type: type,
        priority: priority,
        status: ConversationStatus.active,
        participantIds: participantIds,
        participants: participants,
        studentId: studentId,
        studentName: studentName,
        createdBy: createdBy,
        createdAt: now,
        updatedAt: now,
        tags: tags,
      );

      final docRef = await _conversationsRef.add(conversation.toMap());
      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إنشاء المحادثة: $e');
    }
  }

  /// الحصول على محادثة بالمعرف
  Future<ConversationModel?> getConversation(String conversationId) async {
    try {
      final doc = await _conversationsRef.doc(conversationId).get();
      if (doc.exists) {
        return ConversationModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('فشل في جلب المحادثة: $e');
    }
  }

  /// الحصول على محادثات المستخدم
  Stream<List<ConversationModel>> getUserConversations(String userId) {
    return _conversationsRef
        .where('participantIds', arrayContains: userId)
        .where('isArchived', isEqualTo: false)
        .orderBy('updatedAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ConversationModel.fromFirestore(doc))
            .toList());
  }

  /// البحث في المحادثات
  Stream<List<ConversationModel>> searchConversations(
    String userId,
    String query,
  ) {
    return _conversationsRef
        .where('participantIds', arrayContains: userId)
        .where('isArchived', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ConversationModel.fromFirestore(doc))
            .where((conv) =>
                conv.subject.toLowerCase().contains(query.toLowerCase()) ||
                conv.description.toLowerCase().contains(query.toLowerCase()) ||
                conv.lastMessageText?.toLowerCase().contains(query.toLowerCase()) == true)
            .toList());
  }

  /// تحديث محادثة
  Future<void> updateConversation(
    String conversationId,
    Map<String, dynamic> updates,
  ) async {
    try {
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
      await _conversationsRef.doc(conversationId).update(updates);
    } catch (e) {
      throw Exception('فشل في تحديث المحادثة: $e');
    }
  }

  /// أرشفة محادثة
  Future<void> archiveConversation(
    String conversationId,
    String archivedBy,
  ) async {
    try {
      await updateConversation(conversationId, {
        'isArchived': true,
        'archivedAt': Timestamp.fromDate(DateTime.now()),
        'archivedBy': archivedBy,
        'status': ConversationStatus.archived.toString(),
      });
    } catch (e) {
      throw Exception('فشل في أرشفة المحادثة: $e');
    }
  }

  /// إغلاق محادثة
  Future<void> closeConversation(String conversationId) async {
    try {
      await updateConversation(conversationId, {
        'status': ConversationStatus.closed.toString(),
      });
    } catch (e) {
      throw Exception('فشل في إغلاق المحادثة: $e');
    }
  }

  // ===================================================================
  // إدارة الرسائل
  // ===================================================================

  /// إرسال رسالة جديدة
  /// 
  /// [conversationId] معرف المحادثة
  /// [senderId] معرف المرسل
  /// [senderName] اسم المرسل
  /// [senderRole] دور المرسل
  /// [content] محتوى الرسالة
  /// [type] نوع الرسالة
  /// [replyToMessageId] معرف الرسالة المرد عليها (اختياري)
  /// [attachments] قائمة المرفقات (اختياري)
  /// [priority] أولوية الرسالة
  Future<String> sendMessage({
    required String conversationId,
    required String senderId,
    required String senderName,
    required ParticipantRole senderRole,
    required String content,
    MessageType type = MessageType.text,
    String? replyToMessageId,
    List<MessageAttachment> attachments = const [],
    MessagePriority priority = MessagePriority.normal,
    List<String> tags = const [],
  }) async {
    try {
      final now = DateTime.now();
      
      // إنشاء الرسالة
      final message = MessageModel(
        id: '', // سيتم تعيينه تلقائياً
        conversationId: conversationId,
        senderId: senderId,
        senderName: senderName,
        senderRole: senderRole,
        content: content,
        type: type,
        status: MessageStatus.sent,
        sentAt: now,
        attachments: attachments,
        priority: priority,
        tags: tags,
      );

      // حفظ الرسالة
      final docRef = await _messagesRef.add(message.toMap());
      
      // تحديث المحادثة بآخر رسالة
      await _updateConversationLastMessage(
        conversationId,
        docRef.id,
        content,
        now,
        senderId,
      );

      return docRef.id;
    } catch (e) {
      throw Exception('فشل في إرسال الرسالة: $e');
    }
  }

  /// الحصول على رسائل محادثة
  Stream<List<MessageModel>> getConversationMessages(
    String conversationId, {
    int limit = 50,
  }) {
    return _messagesRef
        .where('conversationId', isEqualTo: conversationId)
        .where('isDeleted', isEqualTo: false)
        .orderBy('sentAt', descending: true)
        .limit(limit)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageModel.fromFirestore(doc))
            .toList());
  }

  /// البحث في الرسائل
  Stream<List<MessageModel>> searchMessages(
    String conversationId,
    String query,
  ) {
    return _messagesRef
        .where('conversationId', isEqualTo: conversationId)
        .where('isDeleted', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => MessageModel.fromFirestore(doc))
            .where((msg) =>
                msg.content.toLowerCase().contains(query.toLowerCase()))
            .toList());
  }

  /// تحديث حالة قراءة الرسالة
  Future<void> markMessageAsRead(
    String messageId,
    String userId,
  ) async {
    try {
      final now = DateTime.now();
      await _messagesRef.doc(messageId).update({
        'readStatus.$userId': {
          'userId': userId,
          'isRead': true,
          'readAt': Timestamp.fromDate(now),
        },
        'status': MessageStatus.read.toString(),
        'readAt': Timestamp.fromDate(now),
      });
    } catch (e) {
      throw Exception('فشل في تحديث حالة القراءة: $e');
    }
  }

  /// تحرير رسالة
  Future<void> editMessage(
    String messageId,
    String newContent,
  ) async {
    try {
      await _messagesRef.doc(messageId).update({
        'content': newContent,
        'isEdited': true,
        'editedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('فشل في تحرير الرسالة: $e');
    }
  }

  /// حذف رسالة
  Future<void> deleteMessage(
    String messageId,
    String deletedBy,
  ) async {
    try {
      await _messagesRef.doc(messageId).update({
        'isDeleted': true,
        'deletedAt': Timestamp.fromDate(DateTime.now()),
        'deletedBy': deletedBy,
      });
    } catch (e) {
      throw Exception('فشل في حذف الرسالة: $e');
    }
  }

  // ===================================================================
  // إدارة الملفات المرفقة
  // ===================================================================

  /// رفع ملف مرفق
  Future<MessageAttachment> uploadAttachment(
    File file,
    String conversationId,
    AttachmentType type,
  ) async {
    try {
      final fileName = '${DateTime.now().millisecondsSinceEpoch}_${file.path.split('/').last}';
      final ref = _storage.ref().child('conversations/$conversationId/attachments/$fileName');
      
      final uploadTask = await ref.putFile(file);
      final downloadUrl = await uploadTask.ref.getDownloadURL();
      
      return MessageAttachment(
        id: fileName,
        name: file.path.split('/').last,
        url: downloadUrl,
        type: type,
        size: await file.length(),
      );
    } catch (e) {
      throw Exception('فشل في رفع الملف: $e');
    }
  }

  // ===================================================================
  // دوال مساعدة خاصة
  // ===================================================================

  /// تحديث آخر رسالة في المحادثة
  Future<void> _updateConversationLastMessage(
    String conversationId,
    String messageId,
    String messageText,
    DateTime messageTime,
    String senderId,
  ) async {
    await _conversationsRef.doc(conversationId).update({
      'lastMessageId': messageId,
      'lastMessageText': messageText,
      'lastMessageTime': Timestamp.fromDate(messageTime),
      'lastMessageSenderId': senderId,
      'updatedAt': Timestamp.fromDate(messageTime),
      'messageCount': FieldValue.increment(1),
    });
  }

  /// الحصول على عدد الرسائل غير المقروءة
  Future<int> getUnreadMessageCount(String userId) async {
    try {
      final conversations = await _conversationsRef
          .where('participantIds', arrayContains: userId)
          .where('isArchived', isEqualTo: false)
          .get();

      int totalUnread = 0;
      for (final doc in conversations.docs) {
        final conv = ConversationModel.fromFirestore(doc);
        totalUnread += conv.unreadCounts[userId] ?? 0;
      }

      return totalUnread;
    } catch (e) {
      return 0;
    }
  }

  /// الحصول على إحصائيات التواصل
  Future<Map<String, dynamic>> getCommunicationStats(String userId) async {
    try {
      final conversations = await _conversationsRef
          .where('participantIds', arrayContains: userId)
          .get();

      int totalConversations = conversations.docs.length;
      int activeConversations = 0;
      int archivedConversations = 0;
      int totalMessages = 0;

      for (final doc in conversations.docs) {
        final conv = ConversationModel.fromFirestore(doc);
        if (conv.isArchived) {
          archivedConversations++;
        } else {
          activeConversations++;
        }
        totalMessages += conv.messageCount;
      }

      return {
        'totalConversations': totalConversations,
        'activeConversations': activeConversations,
        'archivedConversations': archivedConversations,
        'totalMessages': totalMessages,
        'unreadMessages': await getUnreadMessageCount(userId),
      };
    } catch (e) {
      return {
        'totalConversations': 0,
        'activeConversations': 0,
        'archivedConversations': 0,
        'totalMessages': 0,
        'unreadMessages': 0,
      };
    }
  }
}
