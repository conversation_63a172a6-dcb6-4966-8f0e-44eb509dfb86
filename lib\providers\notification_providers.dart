import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/services_provider.dart';

// 1. State Class
class NotificationFormState {
  final String title;
  final String message;
  final String? targetType; // 'all', 'class', 'student'
  final String? selectedClassId;
  final String? selectedStudentId;
  final bool isLoading;

  NotificationFormState({
    this.title = '',
    this.message = '',
    this.targetType,
    this.selectedClassId,
    this.selectedStudentId,
    this.isLoading = false,
  });

  NotificationFormState copyWith({
    String? title,
    String? message,
    String? targetType,
    String? selectedClassId,
    String? selectedStudentId,
    bool? isLoading,
    bool clearClassId = false,
    bool clearStudentId = false,
  }) {
    return NotificationFormState(
      title: title ?? this.title,
      message: message ?? this.message,
      targetType: targetType ?? this.targetType,
      selectedClassId: clearClassId ? null : selectedClassId ?? this.selectedClassId,
      selectedStudentId: clearStudentId ? null : selectedStudentId ?? this.selectedStudentId,
      isLoading: isLoading ?? this.isLoading,
    );
  }
}

// 2. Controller (StateNotifier)
class NotificationController extends StateNotifier<NotificationFormState> {
  final Ref _ref;

  NotificationController(this._ref) : super(NotificationFormState());

  void updateTitle(String title) {
    state = state.copyWith(title: title);
  }

  void updateMessage(String message) {
    state = state.copyWith(message: message);
  }

  void selectTargetType(String? type) {
    state = state.copyWith(
      targetType: type,
      clearClassId: true,
      clearStudentId: true,
    );
  }

  void selectClass(String? classId) {
    state = state.copyWith(selectedClassId: classId);
  }

  void selectStudent(String? studentId) {
    state = state.copyWith(selectedStudentId: studentId);
  }

  Future<bool> sendNotification() async {
    if (state.title.isEmpty || state.message.isEmpty) {
      return false;
    }
    state = state.copyWith(isLoading: true);
    try {
      final firebaseService = _ref.read(firebaseServiceProvider);
      String target = state.targetType ?? 'all';
      String? targetId;

      if (target == 'class') {
        targetId = state.selectedClassId;
      } else if (target == 'student') {
        targetId = state.selectedStudentId;
      }

      await firebaseService.sendNotification(
        title: state.title,
        message: state.message,
        target: target,
        targetId: targetId,
      );
      
      // Reset form on success
      state = NotificationFormState();
      return true;
    } catch (e) {
      // In a real app, you'd handle the error state
      state = state.copyWith(isLoading: false);
      return false;
    }
  }
}

// 3. Provider
final notificationControllerProvider = StateNotifierProvider<NotificationController, NotificationFormState>((ref) {
  return NotificationController(ref);
});
