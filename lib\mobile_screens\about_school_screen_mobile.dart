import 'dart:convert'; // استيراد لتحويل البيانات من وإلى JSON

import 'package:cloud_firestore/cloud_firestore.dart'; // استيراد لإدارة قاعدة بيانات Firestore
import 'package:flutter/material.dart'; // استيراد لعناصر واجهة المستخدم من Material Design
import 'package:flutter_quill/flutter_quill.dart' as quill; // استيراد لعرض محتوى محرر النصوص مع تحديد اسم مستعار لتجنب التعارض
import 'package:school_management_system/widgets/error_message.dart'; // استيراد ويدجت لعرض رسائل الخطأ
import 'package:school_management_system/widgets/loading_indicator.dart'; // استيراد لمؤشر التحميل

// شاشة عرض معلومات "عن المدرسة" في تطبيق الجوال للزوار والطلاب وأولياء الأمور
class AboutSchoolScreenMobile extends StatelessWidget {
  const AboutSchoolScreenMobile({super.key});

  @override
  Widget build(BuildContext context) {
    // استخدام StreamBuilder للاستماع إلى التغييرات في بيانات المدرسة بشكل فوري
    return StreamBuilder<DocumentSnapshot>(
      // تحديد الـ stream الذي سيتم الاستماع إليه
      stream: FirebaseFirestore.instance
          .collection('school_info')
          .doc('about_us')
          .snapshots(),
      builder: (context, snapshot) {
        // --- حالات مختلفة للـ StreamBuilder ---

        // 1. في حالة انتظار وصول البيانات
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        // 2. في حالة حدوث خطأ أثناء جلب البيانات
        if (snapshot.hasError) {
          return ErrorMessage(message: 'حدث خطأ: ${snapshot.error}');
        }

        // 3. في حالة عدم وجود بيانات أو أن المستند غير موجود
        if (!snapshot.hasData || !snapshot.data!.exists) {
          return const ErrorMessage(message: 'لم يتم العثور على محتوى لعرضه.');
        }

        // --- استخراج البيانات وعرضها ---

        // تحويل بيانات المستند إلى Map للوصول إليها بسهولة
        final data = snapshot.data!.data() as Map<String, dynamic>;

        // استخراج البيانات من الـ Map مع توفير قيم افتراضية
        final String logoUrl = data['logoUrl'] ?? '';
        final String vision = data['vision'] ?? 'لا توجد رؤية محددة.';
        final String mission = data['mission'] ?? 'لا توجد رسالة محددة.';
        final String history = data['history'] ?? 'لا يوجد تاريخ محدد.';
        final String values = data['values'] ?? 'لا توجد قيم محددة.';
        final String principalMessage = data['principalMessage'] ?? 'لا توجد كلمة للمدير.';
        final String contactInfo = data['contactInfo'] ?? 'لا توجد معلومات تواصل.';
        final List<String> galleryImageUrls = data['galleryImageUrls'] != null
            ? List<String>.from(data['galleryImageUrls'])
            : [];

        // بناء واجهة المستخدم الرئيسية
        return Scaffold(
          appBar: AppBar(
            title: const Text('عن المدرسة'),
          ),
          body: SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // --- قسم الشعار ومعلومات التواصل ---
                Center(
                  child: Column(
                    children: [
                      if (logoUrl.isNotEmpty)
                        ClipRRect(
                          borderRadius: BorderRadius.circular(12),
                          child: Image.network(
                            logoUrl,
                            height: 120,
                            width: 120,
                            fit: BoxFit.contain,
                            // معالجة الأخطاء في حالة عدم تحميل الصورة
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.school, size: 80, color: Colors.grey),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        'معلومات التواصل',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        contactInfo,
                        textAlign: TextAlign.center,
                        style: Theme.of(context).textTheme.bodyLarge,
                      ),
                    ],
                  ),
                ),
                const Divider(height: 40, thickness: 1),

                // --- قسم الرؤية والرسالة ---
                _buildInfoCard(context, 'رؤيتنا', vision, Icons.visibility),
                const SizedBox(height: 16),
                _buildInfoCard(context, 'رسالتنا', mission, Icons.flag),
                const SizedBox(height: 16),

                // --- قسم كلمة المدير ---
                _buildInfoCard(context, 'كلمة المدير', principalMessage, Icons.record_voice_over),
                const SizedBox(height: 16),

                // --- قسم تاريخ المدرسة ---
                _buildInfoCard(context, 'تاريخ المدرسة', history, Icons.history_edu),
                const SizedBox(height: 16),

                // --- قسم قيمنا ---
                _buildInfoCard(context, 'قيمنا', values, Icons.verified_user),
                const SizedBox(height: 16),

                // --- قسم الوصف العام (محتوى محرر النصوص) ---
                _buildQuillViewer(context, data['description']),
                const Divider(height: 40, thickness: 1),

                // --- قسم معرض الصور ---
                if (galleryImageUrls.isNotEmpty) ...[
                  Text(
                    'معرض الصور',
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                  ),
                  const SizedBox(height: 16),
                  GridView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 2, // عرض صورتين في كل صف
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: galleryImageUrls.length,
                    itemBuilder: (context, index) {
                      return ClipRRect(
                        borderRadius: BorderRadius.circular(12.0),
                        child: Image.network(
                          galleryImageUrls[index],
                          fit: BoxFit.cover,
                          loadingBuilder: (context, child, loadingProgress) {
                            if (loadingProgress == null) return child;
                            return const Center(child: LoadingIndicator());
                          },
                          errorBuilder: (context, error, stackTrace) =>
                              Container(
                                color: Colors.grey[200],
                                child: const Icon(Icons.broken_image, color: Colors.grey),
                              ),
                        ),
                      );
                    },
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  // --- ويدجت مساعد لبناء بطاقات المعلومات ---
  Widget _buildInfoCard(BuildContext context, String title, String content, IconData icon) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).colorScheme.primary),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            const Divider(height: 20),
            Text(
              content,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(height: 1.5),
            ),
          ],
        ),
      ),
    );
  }

  // --- ويدجت مساعد لعرض محتوى محرر النصوص ---
  Widget _buildQuillViewer(BuildContext context, dynamic descriptionJson) {
    quill.QuillController controller;
    try {
      // التأكد من أن البيانات ليست فارغة وأنها من نوع String
      if (descriptionJson != null && descriptionJson is String && descriptionJson.isNotEmpty) {
        final decoded = jsonDecode(descriptionJson);
        controller = quill.QuillController(
          document: quill.Document.fromJson(decoded),
          selection: const TextSelection.collapsed(offset: 0),
        );
      } else {
        // إذا كانت البيانات فارغة أو غير صالحة، نعرض مستند فارغ
        controller = quill.QuillController.basic();
      }
    } catch (e) {
      // في حالة حدوث خطأ في التحويل (قد يكون النص عادياً)، نعرضه كما هو
      controller = quill.QuillController(
        document: quill.Document()..insert(0, descriptionJson.toString()),
        selection: const TextSelection.collapsed(offset: 0),
      );
    }

    // إذا لم يكن هناك محتوى، لا نعرض شيئاً
    if (controller.document.isEmpty()) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Divider(height: 40, thickness: 1),
        Text(
          'وصف عام',
          style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: quill.QuillEditor.basic(
            controller: controller,
          ),
        ),
      ],
    );
  }
}
