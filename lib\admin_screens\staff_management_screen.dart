import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/staff_form_dialog.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/staff_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

// شاشة إدارة الكادر التعليمي والموظفين في لوحة التحكم
class StaffManagementScreen extends ConsumerWidget {
  const StaffManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final staffAsyncValue = ref.watch(staffStreamProvider);
    final filteredStaff = ref.watch(filteredStaffProvider);

    return Scaffold(
      body: Column(
        children: [
          // --- شريط البحث ---
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'بحث بالاسم أو البريد الإلكتروني...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                ref.read(staffSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          // --- عرض قائمة الموظفين ---
          Expanded(
            child: staffAsyncValue.when(
              loading: () => const LoadingIndicator(),
              error: (err, stack) =>
                  ErrorMessage(message: 'خطأ في جلب البيانات: $err'),
              data: (staffList) {
                if (staffList.isEmpty) {
                  return const Center(child: Text('لا يوجد موظفون حالياً.'));
                }
                if (filteredStaff.isEmpty) {
                  return const Center(
                      child: Text('لم يتم العثور على نتائج للبحث.'));
                }

                // --- عرض النتائج في شبكة ---
                return GridView.builder(
                  padding: const EdgeInsets.all(16),
                  gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
                    maxCrossAxisExtent: 400, // أقصى عرض للبطاقة
                    childAspectRatio: 2.5, // نسبة العرض إلى الارتفاع
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                  ),
                  itemCount: filteredStaff.length,
                  itemBuilder: (context, index) {
                    final user = filteredStaff[index];
                    return _buildStaffCard(context, user, ref);
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => showDialog(
          context: context,
          builder: (context) => const StaffFormDialog(),
        ),
        tooltip: 'إضافة موظف',
        child: const Icon(Icons.add),
      ),
    );
  }

  // --- ويدجت بناء بطاقة الموظف ---
  Widget _buildStaffCard(BuildContext context, UserModel user, WidgetRef ref) {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // --- الصورة الشخصية ---
            CircleAvatar(
              radius: 40,
              backgroundImage: (user.profileImageUrl != null &&
                      user.profileImageUrl!.isNotEmpty)
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: (user.profileImageUrl == null ||
                      user.profileImageUrl!.isEmpty)
                  ? const Icon(Icons.person, size: 40)
                  : null,
            ),
            const SizedBox(width: 16),
            // --- معلومات الموظف ---
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(user.name,
                      style: Theme.of(context).textTheme.titleLarge),
                  Text(user.jobTitle ?? 'غير محدد',
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(color: Colors.grey.shade600)),
                  const SizedBox(height: 4),
                  Text(user.email,
                      style: Theme.of(context).textTheme.bodyMedium),
                  const SizedBox(height: 4),
                  Chip(
                    label: Text(user.role == 'teacher' ? 'معلم' : 'إداري'),
                    backgroundColor: user.role == 'teacher'
                        ? Colors.blue.shade100
                        : Colors.green.shade100,
                  ),
                ],
              ),
            ),
            // --- أزرار الإجراءات ---
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                IconButton(
                  icon: const Icon(Icons.edit, color: Colors.blue),
                  tooltip: 'تعديل',
                  onPressed: () => showDialog(
                    context: context,
                    builder: (context) => StaffFormDialog(staff: user),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.lock_reset, color: Colors.orange),
                  tooltip: 'إعادة تعيين كلمة المرور',
                  // TODO: Implement password reset logic via a provider/controller
                  onPressed: () {
                    print("Reset password for ${user.email}");
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.delete, color: Colors.red),
                  tooltip: 'حذف',
                  // TODO: Implement delete logic via a provider/controller
                  onPressed: () {
                     print("Delete user ${user.name}");
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
