import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/services/firebase_service.dart';

//======================================================================
// Providers for Admin Assignments Management Screen
//======================================================================

final assignmentsStreamProvider =
    StreamProvider.autoDispose<List<AssignmentModel>>((ref) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getAssignmentsStream();
    });

/// مزودات البحث والتصفية للإدارة
final adminAssignmentSearchQueryProvider = StateProvider<String>((ref) => '');
final adminPriorityFilterProvider = StateProvider<AssignmentPriority?>((ref) => null);
final adminStatusFilterProvider = StateProvider<AssignmentStatus?>((ref) => null);

/// مزود الواجبات المفلترة للإدارة - محسن
final adminFilteredAssignmentsProvider =
    Provider.autoDispose<List<AssignmentModel>>((ref) {
      final assignments = ref.watch(assignmentsStreamProvider).asData?.value ?? [];
      final searchQuery = ref.watch(adminAssignmentSearchQueryProvider).toLowerCase();
      final priorityFilter = ref.watch(adminPriorityFilterProvider);
      final statusFilter = ref.watch(adminStatusFilterProvider);

      var filteredAssignments = assignments;

      // تطبيق البحث النصي
      if (searchQuery.isNotEmpty) {
        filteredAssignments = filteredAssignments.where((assignment) {
          return assignment.title.toLowerCase().contains(searchQuery) ||
              assignment.subjectName.toLowerCase().contains(searchQuery) ||
              (assignment.teacherName?.toLowerCase().contains(searchQuery) ?? false) ||
              assignment.description.toLowerCase().contains(searchQuery);
        }).toList();
      }

      // تطبيق مرشح الأولوية
      if (priorityFilter != null) {
        filteredAssignments = filteredAssignments.where((assignment) {
          return assignment.priority == priorityFilter;
        }).toList();
      }

      // تطبيق مرشح الحالة
      if (statusFilter != null) {
        filteredAssignments = filteredAssignments.where((assignment) {
          return assignment.currentStatus == statusFilter;
        }).toList();
      }

      // ترتيب حسب الأولوية والتاريخ
      filteredAssignments.sort((a, b) {
        // ترتيب حسب الأولوية أولاً
        final priorityComparison = b.priority.index.compareTo(a.priority.index);
        if (priorityComparison != 0) return priorityComparison;
        
        // ثم حسب تاريخ التسليم
        return a.dueDate.compareTo(b.dueDate);
      });

      return filteredAssignments;
    });

//======================================================================
// Providers for Student Assignments Screen - محسنة ومطورة
//======================================================================

/// مزود Stream محسن لجلب واجبات الطالب
/// يجلب جميع الواجبات المخصصة للطالب مع التحديث التلقائي
final studentAssignmentsStreamProvider = StreamProvider.autoDispose
    .family<List<AssignmentModel>, String>((ref, studentId) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return firebaseService.getStudentAssignmentsStream(studentId);
    });

/// مزود لإحصائيات الواجبات الشاملة
/// يحسب جميع الإحصائيات المفيدة من قائمة الواجبات
final assignmentStatsProvider = Provider.autoDispose
    .family<AssignmentStats?, String>((ref, studentId) {
      final assignments =
          ref.watch(studentAssignmentsStreamProvider(studentId)).asData?.value;
      return assignments != null
          ? AssignmentStats(assignments: assignments)
          : null;
    });

/// مزود لتصفية الواجبات حسب الحالة
final assignmentsByStatusProvider = Provider.autoDispose.family<
  Map<AssignmentStatus, List<AssignmentModel>>,
  String
>((ref, studentId) {
  final assignments =
      ref.watch(studentAssignmentsStreamProvider(studentId)).asData?.value ??
      [];

  final Map<AssignmentStatus, List<AssignmentModel>> result = {};
  for (final status in AssignmentStatus.values) {
    result[status] = [];
  }

  for (final assignment in assignments) {
    result[assignment.currentStatus]!.add(assignment);
  }

  return result;
});

/// مزود لتصفية الواجبات حسب الأولوية
final assignmentsByPriorityProvider = Provider.autoDispose.family<
  Map<AssignmentPriority, List<AssignmentModel>>,
  String
>((ref, studentId) {
  final assignments =
      ref.watch(studentAssignmentsStreamProvider(studentId)).asData?.value ??
      [];

  final Map<AssignmentPriority, List<AssignmentModel>> result = {};
  for (final priority in AssignmentPriority.values) {
    result[priority] = [];
  }

  for (final assignment in assignments) {
    result[assignment.priority]!.add(assignment);
  }

  return result;
});

/// مزود لتصفية الواجبات حسب المادة
final assignmentsBySubjectProvider = Provider.autoDispose
    .family<Map<String, List<AssignmentModel>>, String>((ref, studentId) {
      final assignments =
          ref
              .watch(studentAssignmentsStreamProvider(studentId))
              .asData
              ?.value ??
          [];

      final Map<String, List<AssignmentModel>> result = {};

      for (final assignment in assignments) {
        result[assignment.subjectName] ??= [];
        result[assignment.subjectName]!.add(assignment);
      }

      return result;
    });

/// مزود للواجبات العاجلة (أقل من 24 ساعة)
final urgentAssignmentsProvider = Provider.autoDispose
    .family<List<AssignmentModel>, String>((ref, studentId) {
      final assignments =
          ref
              .watch(studentAssignmentsStreamProvider(studentId))
              .asData
              ?.value ??
          [];

      return assignments.where((assignment) {
          return !assignment.isOverdue &&
              assignment.timeRemaining.inHours <= 24 &&
              assignment.timeRemaining.inMinutes > 0;
        }).toList()
        ..sort((a, b) => a.timeRemaining.compareTo(b.timeRemaining));
    });

/// مزود للواجبات المتأخرة
final overdueAssignmentsProvider = Provider.autoDispose
    .family<List<AssignmentModel>, String>((ref, studentId) {
      final assignments =
          ref
              .watch(studentAssignmentsStreamProvider(studentId))
              .asData
              ?.value ??
          [];

      return assignments.where((assignment) => assignment.isOverdue).toList()
        ..sort((a, b) => b.dueDate.compareTo(a.dueDate)); // الأحدث أولاً
    });

/// مزود للواجبات القادمة (خلال الأسبوع القادم)
final upcomingAssignmentsProvider = Provider.autoDispose
    .family<List<AssignmentModel>, String>((ref, studentId) {
      final assignments =
          ref
              .watch(studentAssignmentsStreamProvider(studentId))
              .asData
              ?.value ??
          [];
      final now = DateTime.now();
      final nextWeek = now.add(const Duration(days: 7));

      return assignments.where((assignment) {
          return assignment.dueDate.isAfter(now) &&
              assignment.dueDate.isBefore(nextWeek);
        }).toList()
        ..sort((a, b) => a.dueDate.compareTo(b.dueDate));
    });

/// مزود لحالة البحث في الواجبات للطلاب
final studentAssignmentSearchQueryProvider = StateProvider<String>((ref) => '');

/// مزود لتصفية الواجبات بناءً على البحث للطلاب
final studentFilteredAssignmentsProvider = Provider.autoDispose.family<
  List<AssignmentModel>,
  String
>((ref, studentId) {
  final assignments =
      ref.watch(studentAssignmentsStreamProvider(studentId)).asData?.value ??
      [];
  final searchQuery =
      ref.watch(studentAssignmentSearchQueryProvider).toLowerCase().trim();

  if (searchQuery.isEmpty) {
    return assignments;
  }

  return assignments.where((assignment) {
    return assignment.title.toLowerCase().contains(searchQuery) ||
        assignment.subjectName.toLowerCase().contains(searchQuery) ||
        assignment.description.toLowerCase().contains(searchQuery) ||
        (assignment.teacherName?.toLowerCase().contains(searchQuery) ??
            false) ||
        assignment.tags.any((tag) => tag.toLowerCase().contains(searchQuery));
  }).toList();
});

/// مزود لحالة ترتيب الواجبات
final assignmentSortModeProvider = StateProvider<AssignmentSortMode>(
  (ref) => AssignmentSortMode.dueDate,
);

/// مزود للواجبات المرتبة
final sortedAssignmentsProvider = Provider.autoDispose.family<
  List<AssignmentModel>,
  String
>((ref, studentId) {
  final assignments = ref.watch(studentFilteredAssignmentsProvider(studentId));
  final sortMode = ref.watch(assignmentSortModeProvider);

  final sortedList = List<AssignmentModel>.from(assignments);

  switch (sortMode) {
    case AssignmentSortMode.dueDate:
      sortedList.sort((a, b) => a.dueDate.compareTo(b.dueDate));
      break;
    case AssignmentSortMode.priority:
      sortedList.sort((a, b) => b.priority.index.compareTo(a.priority.index));
      break;
    case AssignmentSortMode.subject:
      sortedList.sort((a, b) => a.subjectName.compareTo(b.subjectName));
      break;
    case AssignmentSortMode.urgency:
      sortedList.sort((a, b) => b.urgencyLevel.compareTo(a.urgencyLevel));
      break;
    case AssignmentSortMode.createdDate:
      sortedList.sort((a, b) => b.createdAt.compareTo(a.createdAt));
      break;
  }

  return sortedList;
});

/// مزود لحالة تصفية الواجبات
final assignmentFilterProvider = StateProvider<AssignmentFilter>(
  (ref) => AssignmentFilter.all,
);

/// مزود للواجبات المفلترة حسب الحالة
final filteredByStatusAssignmentsProvider = Provider.autoDispose
    .family<List<AssignmentModel>, String>((ref, studentId) {
      final assignments = ref.watch(sortedAssignmentsProvider(studentId));
      final filter = ref.watch(assignmentFilterProvider);

      switch (filter) {
        case AssignmentFilter.all:
          return assignments;
        case AssignmentFilter.pending:
          return assignments.where((a) => !a.isOverdue).toList();
        case AssignmentFilter.overdue:
          return assignments.where((a) => a.isOverdue).toList();
        case AssignmentFilter.urgent:
          return assignments
              .where((a) => !a.isOverdue && a.timeRemaining.inHours <= 24)
              .toList();
        case AssignmentFilter.thisWeek:
          final now = DateTime.now();
          final endOfWeek = now.add(Duration(days: 7 - now.weekday));
          return assignments
              .where(
                (a) => a.dueDate.isAfter(now) && a.dueDate.isBefore(endOfWeek),
              )
              .toList();
      }
    });

/// مزود لنمط عرض الواجبات
final assignmentViewModeProvider = StateProvider<AssignmentViewMode>(
  (ref) => AssignmentViewMode.list,
);

/// تعداد لأنماط ترتيب الواجبات
enum AssignmentSortMode {
  dueDate('تاريخ التسليم'),
  priority('الأولوية'),
  subject('المادة'),
  urgency('الإلحاح'),
  createdDate('تاريخ الإنشاء');

  const AssignmentSortMode(this.arabicName);
  final String arabicName;
}

/// تعداد لتصفية الواجبات
enum AssignmentFilter {
  all('جميع الواجبات'),
  pending('مطلوبة'),
  overdue('متأخرة'),
  urgent('عاجلة'),
  thisWeek('هذا الأسبوع');

  const AssignmentFilter(this.arabicName);
  final String arabicName;
}

/// تعداد لأنماط عرض الواجبات
enum AssignmentViewMode {
  list('قائمة'),
  grid('شبكة'),
  calendar('تقويم'),
  timeline('خط زمني');

  const AssignmentViewMode(this.arabicName);
  final String arabicName;
}

//======================================================================
// Controller for Assignment Card - محسن ومطور
//======================================================================

/// حالة بطاقة الواجب المحسنة
/// تحتوي على جميع المعلومات اللازمة لعرض حالة الواجب
class AssignmentCardState {
  final AssignmentStatus status; // حالة الواجب المحسنة
  final String statusText; // نص الحالة
  final Color statusColor; // لون الحالة
  final bool isSubmitted; // هل تم التسليم
  final bool isLoading; // حالة التحميل
  final DateTime? submissionDate; // تاريخ التسليم
  final double? grade; // الدرجة المحصلة
  final String? feedback; // تعليقات المعلم
  final bool canSubmit; // إمكانية التسليم
  final String? submissionError; // رسالة خطأ التسليم

  const AssignmentCardState({
    this.status = AssignmentStatus.pending,
    required this.statusText,
    required this.statusColor,
    this.isSubmitted = false,
    this.isLoading = false,
    this.submissionDate,
    this.grade,
    this.feedback,
    this.canSubmit = true,
    this.submissionError,
  });

  /// إنشاء نسخة محدثة من الحالة
  AssignmentCardState copyWith({
    AssignmentStatus? status,
    String? statusText,
    Color? statusColor,
    bool? isSubmitted,
    bool? isLoading,
    DateTime? submissionDate,
    double? grade,
    String? feedback,
    bool? canSubmit,
    String? submissionError,
  }) {
    return AssignmentCardState(
      status: status ?? this.status,
      statusText: statusText ?? this.statusText,
      statusColor: statusColor ?? this.statusColor,
      isSubmitted: isSubmitted ?? this.isSubmitted,
      isLoading: isLoading ?? this.isLoading,
      submissionDate: submissionDate ?? this.submissionDate,
      grade: grade ?? this.grade,
      feedback: feedback ?? this.feedback,
      canSubmit: canSubmit ?? this.canSubmit,
      submissionError: submissionError ?? this.submissionError,
    );
  }

  /// إنشاء حالة من نموذج الواجب
  factory AssignmentCardState.fromAssignment(AssignmentModel assignment) {
    final status = assignment.currentStatus;
    return AssignmentCardState(
      status: status,
      statusText: status.arabicName,
      statusColor: status.color.withAlpha((255 * 0.1).round()),
      isSubmitted:
          status == AssignmentStatus.submitted ||
          status == AssignmentStatus.graded,
      canSubmit: !assignment.isOverdue || assignment.canSubmitLate,
    );
  }
}

/// تحكم محسن في بطاقة الواجب
/// يدير حالة الواجب وعمليات التسليم والتحديث
class AssignmentCardController extends StateNotifier<AssignmentCardState> {
  final FirebaseService _firebaseService;
  final String _studentId;
  final AssignmentModel _assignment;

  AssignmentCardController(
    this._firebaseService,
    this._studentId,
    this._assignment,
  ) : super(AssignmentCardState.fromAssignment(_assignment)) {
    _initializeAssignmentStatus();
  }

  /// تهيئة حالة الواجب من قاعدة البيانات
  Future<void> _initializeAssignmentStatus() async {
    try {
      // TODO: Implement getAssignmentSubmission method in FirebaseService
      // جلب معلومات التسليم من قاعدة البيانات
      // final submissionData = await _firebaseService.getAssignmentSubmission(
      //   _studentId,
      //   _assignment.id,
      // );
      final submissionData = null; // Placeholder

      if (submissionData != null) {
        // تحديث الحالة بناءً على بيانات التسليم
        state = state.copyWith(
          status: _determineStatusFromSubmission(submissionData),
          statusText: _getStatusText(submissionData),
          statusColor: _getStatusColor(submissionData),
          isSubmitted: true,
          submissionDate:
              submissionData['submissionDate'] != null
                  ? (submissionData['submissionDate'] as Timestamp).toDate()
                  : null,
          grade: submissionData['grade'] as double?,
          feedback: submissionData['feedback'] as String?,
        );
      } else {
        // لا يوجد تسليم، تحديث الحالة بناءً على التاريخ
        _updateStatusBasedOnDate();
      }
    } catch (e) {
      // في حالة الخطأ، استخدم الحالة الافتراضية
      _updateStatusBasedOnDate();
    }
  }

  /// تحديث الحالة بناءً على التاريخ الحالي
  void _updateStatusBasedOnDate() {
    final now = DateTime.now();
    AssignmentStatus status;
    String statusText;
    Color statusColor;
    bool canSubmit;

    if (now.isBefore(_assignment.dueDate)) {
      // الواجب لم ينته موعده بعد
      status = AssignmentStatus.pending;
      statusText = 'مطلوب';
      statusColor = AssignmentStatus.pending.color.withAlpha((255 * 0.1).round());
      canSubmit = true;
    } else if (_assignment.canSubmitLate) {
      // الواجب متأخر لكن يمكن التسليم المتأخر
      status = AssignmentStatus.overdue;
      statusText = 'متأخر - يمكن التسليم';
      statusColor = AssignmentStatus.overdue.color.withAlpha((255 * 0.1).round());
      canSubmit = true;
    } else {
      // الواجب متأخر ولا يمكن التسليم
      status = AssignmentStatus.overdue;
      statusText = 'متأخر';
      statusColor = AssignmentStatus.overdue.color.withAlpha((255 * 0.1).round());
      canSubmit = false;
    }

    state = state.copyWith(
      status: status,
      statusText: statusText,
      statusColor: statusColor,
      canSubmit: canSubmit,
    );
  }

  /// تحديد الحالة من بيانات التسليم
  AssignmentStatus _determineStatusFromSubmission(Map<String, dynamic> data) {
    if (data['grade'] != null) {
      return AssignmentStatus.graded;
    } else if (data['isReturned'] == true) {
      return AssignmentStatus.returned;
    } else {
      return AssignmentStatus.submitted;
    }
  }

  /// الحصول على نص الحالة من بيانات التسليم
  String _getStatusText(Map<String, dynamic> data) {
    final status = _determineStatusFromSubmission(data);
    switch (status) {
      case AssignmentStatus.submitted:
        return 'تم التسليم';
      case AssignmentStatus.graded:
        final grade = data['grade'] as double?;
        return grade != null
            ? 'تم التصحيح (${grade.toInt()}/${_assignment.maxGrade})'
            : 'تم التصحيح';
      case AssignmentStatus.returned:
        return 'مُعاد للتعديل';
      default:
        return status.arabicName;
    }
  }

  /// الحصول على لون الحالة من بيانات التسليم
  Color _getStatusColor(Map<String, dynamic> data) {
    final status = _determineStatusFromSubmission(data);
    return status.color.withAlpha((255 * 0.1).round());
  }

  /// تسليم حل الواجب
  Future<void> submitSolution(dynamic file, {String? notes}) async {
    if (!state.canSubmit) {
      state = state.copyWith(
        submissionError: 'لا يمكن تسليم هذا الواجب في الوقت الحالي',
      );
      return;
    }

    state = state.copyWith(isLoading: true, submissionError: null);

    try {
      // TODO: Implement submitAssignmentSolution method in FirebaseService
      // رفع الحل إلى قاعدة البيانات
      // await _firebaseService.submitAssignmentSolution(
      //   _studentId,
      //   _assignment.id,
      //   file,
      //   notes: notes,
      // );

      // Placeholder - simulate successful submission
      await Future.delayed(const Duration(seconds: 1));

      // تحديث الحالة بعد التسليم الناجح
      state = state.copyWith(
        status: AssignmentStatus.submitted,
        statusText: 'تم التسليم',
        statusColor: AssignmentStatus.submitted.color.withAlpha((255 * 0.1).round()),
        isSubmitted: true,
        isLoading: false,
        submissionDate: DateTime.now(),
        canSubmit: false, // لا يمكن التسليم مرة أخرى
      );
    } catch (e) {
      // في حالة فشل التسليم
      state = state.copyWith(
        isLoading: false,
        submissionError: 'فشل في تسليم الواجب: ${e.toString()}',
      );
    }
  }

  /// إعادة تسليم الواجب (في حالة الإعادة)
  Future<void> resubmitSolution(dynamic file, {String? notes}) async {
    if (state.status != AssignmentStatus.returned) {
      state = state.copyWith(submissionError: 'لا يمكن إعادة تسليم هذا الواجب');
      return;
    }

    await submitSolution(file, notes: notes);
  }

  /// تحديث الحالة يدوياً (للتحديث الفوري)
  void refreshStatus() {
    _initializeAssignmentStatus();
  }

  /// مسح رسالة الخطأ
  void clearError() {
    state = state.copyWith(submissionError: null);
  }
}

/// مزود تحكم بطاقة الواجب المحسن
final assignmentCardControllerProvider = StateNotifierProvider.autoDispose
    .family<
      AssignmentCardController,
      AssignmentCardState,
      ({String studentId, AssignmentModel assignment})
    >((ref, params) {
      final firebaseService = ref.watch(firebaseServiceProvider);
      return AssignmentCardController(
        firebaseService,
        params.studentId,
        params.assignment,
      );
    });
