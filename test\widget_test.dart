// اختبار أساسي للتحقق من أن بيئة الاختبار تعمل

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  testWidgets('Basic smoke test to ensure test environment is working', (WidgetTester tester) async {
    // بناء ويدجت بسيط جداً للاختبار.
    await tester.pumpWidget(
      const MaterialApp(
        home: Scaffold(
          body: Text('Hello World'),
        ),
      ),
    );

    // التحقق من وجود النص 'Hello World'.
    // نجاح هذا الاختبار يعني أن أداة الاختبار تعمل بشكل صحيح.
    expect(find.text('Hello World'), findsOneWidget);
  });
}