import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/timetable_model.dart';
import 'package:school_management_system/providers/timetable_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة الجدول الزمني المحسنة للطلاب
/// تعرض الجدول الدراسي بتصميم عصري وتفاعلي مع إمكانيات متقدمة
class StudentTimetableScreen extends ConsumerStatefulWidget {
  final String studentId;

  const StudentTimetableScreen({super.key, required this.studentId});

  @override
  ConsumerState<StudentTimetableScreen> createState() =>
      _StudentTimetableScreenState();
}

class _StudentTimetableScreenState extends ConsumerState<StudentTimetableScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    // إعداد التبويبات (اليوم الحالي، الأسبوع الكامل، الإحصائيات)
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // مراقبة الجدول الزمني المحسن مع أسماء المعلمين
    final timetableAsyncValue = ref.watch(
      enrichedStudentTimetableProvider(widget.studentId),
    );
    // مراقبة نمط العرض الحالي
    final viewMode = ref.watch(timetableViewModeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الجدول الدراسي'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        // إضافة أزرار التحكم في شريط التطبيق
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
            tooltip: 'البحث في الجدول',
          ),
          // زر تغيير نمط العرض
          PopupMenuButton<TimetableViewMode>(
            icon: const Icon(Icons.view_module),
            tooltip: 'تغيير نمط العرض',
            onSelected: (mode) {
              ref.read(timetableViewModeProvider.notifier).state = mode;
            },
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: TimetableViewMode.today,
                    child: Row(
                      children: [
                        Icon(Icons.today, color: Colors.blue),
                        SizedBox(width: 8),
                        Text('اليوم الحالي'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: TimetableViewMode.weekly,
                    child: Row(
                      children: [
                        Icon(Icons.view_week, color: Colors.green),
                        SizedBox(width: 8),
                        Text('الأسبوع الكامل'),
                      ],
                    ),
                  ),
                  const PopupMenuItem(
                    value: TimetableViewMode.daily,
                    child: Row(
                      children: [
                        Icon(Icons.view_day, color: Colors.orange),
                        SizedBox(width: 8),
                        Text('عرض يومي'),
                      ],
                    ),
                  ),
                ],
          ),
        ],
        // إضافة التبويبات
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.today), text: 'اليوم'),
            Tab(icon: Icon(Icons.calendar_view_week), text: 'الأسبوع'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
          ],
        ),
      ),
      body: timetableAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error:
            (error, stackTrace) => ErrorMessage(
              message: 'حدث خطأ في تحميل الجدول الدراسي: ${error.toString()}',
            ),
        data: (timetable) {
          if (timetable == null) {
            return _buildEmptyState(context);
          }

          return TabBarView(
            controller: _tabController,
            children: [
              // تبويب اليوم الحالي
              _buildTodayView(context, timetable),
              // تبويب الأسبوع الكامل
              _buildWeeklyView(context, timetable, viewMode),
              // تبويب الإحصائيات
              _buildStatsView(context, timetable),
            ],
          );
        },
      ),
    );
  }

  /// بناء حالة الشاشة الفارغة عند عدم وجود جدول زمني
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.schedule_outlined, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لم يتم تحديد جدول دراسي بعد',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'سيظهر الجدول الدراسي هنا عند إضافته',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              // إعادة تحميل البيانات
              ref.invalidate(
                enrichedStudentTimetableProvider(widget.studentId),
              );
            },
            icon: const Icon(Icons.refresh),
            label: const Text('إعادة المحاولة'),
          ),
        ],
      ),
    );
  }

  /// بناء عرض اليوم الحالي
  Widget _buildTodayView(BuildContext context, TimetableModel timetable) {
    final todaySchedule = ref.watch(todayScheduleProvider(widget.studentId));
    final currentDay = ref.watch(currentDayProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة معلومات اليوم
          _buildDayInfoCard(context, currentDay, timetable.className),

          const SizedBox(height: 16),

          // قائمة حصص اليوم
          if (currentDay != 'عطلة نهاية الأسبوع')
            _buildTodayScheduleList(context, todaySchedule)
          else
            _buildWeekendCard(context),
        ],
      ),
    );
  }

  /// بناء العرض الأسبوعي
  Widget _buildWeeklyView(
    BuildContext context,
    TimetableModel timetable,
    TimetableViewMode viewMode,
  ) {
    if (viewMode == TimetableViewMode.daily) {
      return _buildDailyCardsView(context, timetable);
    } else {
      return _buildTraditionalTableView(context, timetable);
    }
  }

  /// بناء عرض الإحصائيات
  Widget _buildStatsView(BuildContext context, TimetableModel timetable) {
    final stats = TimetableStats(timetable);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // بطاقة الإحصائيات العامة
          _buildGeneralStatsCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة إحصائيات المواد
          _buildSubjectStatsCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة إحصائيات المعلمين
          _buildTeacherStatsCard(context, stats),

          const SizedBox(height: 16),

          // بطاقة التوزيع اليومي
          _buildDailyDistributionCard(context, stats),
        ],
      ),
    );
  }

  /// بناء بطاقة معلومات اليوم
  Widget _buildDayInfoCard(
    BuildContext context,
    String currentDay,
    String className,
  ) {
    final now = DateTime.now();
    final isWeekend = currentDay == 'عطلة نهاية الأسبوع';

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors:
                isWeekend
                    ? [Colors.orange.shade100, Colors.orange.shade50]
                    : [Colors.blue.shade100, Colors.blue.shade50],
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isWeekend ? Icons.weekend : Icons.today,
                  color: isWeekend ? Colors.orange : Colors.blue,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        currentDay,
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color:
                              isWeekend
                                  ? Colors.orange.shade800
                                  : Colors.blue.shade800,
                        ),
                      ),
                      Text(
                        className,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: isWeekend ? Colors.orange : Colors.blue,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    '${now.day}/${now.month}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قائمة حصص اليوم الحالي
  Widget _buildTodayScheduleList(
    BuildContext context,
    List<TimetableSession?> todaySchedule,
  ) {
    if (todaySchedule.isEmpty || todaySchedule.every((s) => s == null)) {
      return Card(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Center(
            child: Column(
              children: [
                Icon(
                  Icons.free_breakfast,
                  size: 48,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 8),
                Text(
                  'لا توجد حصص اليوم',
                  style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'حصص اليوم (${todaySchedule.where((s) => s != null).length} حصة)',
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: todaySchedule.length,
          separatorBuilder: (context, index) => const SizedBox(height: 8),
          itemBuilder: (context, index) {
            final session = todaySchedule[index];
            final periodTime =
                index < TimetableModel.periodTimes.length
                    ? TimetableModel.periodTimes[index]
                    : 'غير محدد';

            return _buildSessionCard(
              context,
              session,
              'الحصة ${index + 1}',
              periodTime,
              index + 1,
            );
          },
        ),
      ],
    );
  }

  /// بناء بطاقة عطلة نهاية الأسبوع
  Widget _buildWeekendCard(BuildContext context) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.green.shade100, Colors.green.shade50],
          ),
        ),
        child: Column(
          children: [
            Icon(Icons.weekend, size: 64, color: Colors.green.shade600),
            const SizedBox(height: 16),
            Text(
              'عطلة نهاية الأسبوع',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Colors.green.shade800,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'استمتع بوقتك واستعد للأسبوع القادم!',
              style: TextStyle(fontSize: 14, color: Colors.green.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة حصة واحدة
  Widget _buildSessionCard(
    BuildContext context,
    TimetableSession? session,
    String periodLabel,
    String timeSlot,
    int periodNumber,
  ) {
    if (session == null) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey.shade50,
          ),
          child: Row(
            children: [
              Container(
                width: 4,
                height: 40,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      periodLabel,
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      timeSlot,
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Text(
                'فترة راحة',
                style: TextStyle(
                  color: Colors.grey.shade500,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 3,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [
              session.defaultSubjectColor.withOpacity(0.1),
              session.defaultSubjectColor.withOpacity(0.05),
            ],
          ),
          border: Border.all(
            color: session.defaultSubjectColor.withOpacity(0.3),
            width: 2,
          ),
        ),
        child: Row(
          children: [
            // شريط اللون الجانبي
            Container(
              width: 4,
              height: 50,
              decoration: BoxDecoration(
                color: session.defaultSubjectColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(width: 12),

            // معلومات الحصة
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // اسم المادة
                  Text(
                    session.subject,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 4),

                  // معلومات إضافية
                  Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 14,
                        color: Colors.grey.shade600,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        timeSlot,
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: 12,
                        ),
                      ),
                      if (session.classroom != null) ...[
                        const SizedBox(width: 12),
                        Icon(Icons.room, size: 14, color: Colors.grey.shade600),
                        const SizedBox(width: 4),
                        Text(
                          session.classroom!,
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ],
                  ),

                  // اسم المعلم
                  if (session.teacherName != null) ...[
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(
                          Icons.person,
                          size: 14,
                          color: Colors.grey.shade600,
                        ),
                        const SizedBox(width: 4),
                        Expanded(
                          child: Text(
                            session.teacherName!,
                            style: TextStyle(
                              color: Colors.grey.shade700,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            // رقم الحصة
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color: session.defaultSubjectColor,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Center(
                child: Text(
                  '$periodNumber',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء العرض اليومي بالبطاقات
  Widget _buildDailyCardsView(BuildContext context, TimetableModel timetable) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children:
            TimetableModel.weekDays.map((day) {
              final daySchedule = timetable.getDaySchedule(day);
              final sessionsCount = daySchedule.where((s) => s != null).length;

              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                elevation: 4,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: ExpansionTile(
                  title: Text(
                    day,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Text('$sessionsCount حصة'),
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      day[0],
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  children: [
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        children: List.generate(daySchedule.length, (index) {
                          final session = daySchedule[index];
                          final periodTime =
                              index < TimetableModel.periodTimes.length
                                  ? TimetableModel.periodTimes[index]
                                  : 'غير محدد';

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8),
                            child: _buildSessionCard(
                              context,
                              session,
                              'الحصة ${index + 1}',
                              periodTime,
                              index + 1,
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
      ),
    );
  }

  /// بناء العرض التقليدي للجدول
  Widget _buildTraditionalTableView(
    BuildContext context,
    TimetableModel timetable,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: DataTable(
            headingRowColor: MaterialStateProperty.all(
              Theme.of(context).primaryColor.withOpacity(0.1),
            ),
            border: TableBorder.all(
              color: Colors.grey.shade300,
              borderRadius: BorderRadius.circular(12),
            ),
            columns: [
              const DataColumn(
                label: Text(
                  'اليوم',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
              ...List.generate(
                7,
                (index) => DataColumn(
                  label: Text(
                    'الحصة ${index + 1}',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ],
            rows:
                TimetableModel.weekDays.map((day) {
                  return DataRow(
                    cells: [
                      DataCell(
                        Text(
                          day,
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      ...List.generate(7, (period) {
                        final session = timetable.getSession(day, period + 1);
                        return DataCell(
                          _buildTableSessionCell(context, session),
                        );
                      }),
                    ],
                  );
                }).toList(),
          ),
        ),
      ),
    );
  }

  /// بناء خلية الحصة في الجدول التقليدي
  Widget _buildTableSessionCell(
    BuildContext context,
    TimetableSession? session,
  ) {
    if (session == null) {
      return const Center(
        child: Text('-', style: TextStyle(color: Colors.grey)),
      );
    }

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: session.defaultSubjectColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: session.defaultSubjectColor.withOpacity(0.3)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            session.subject,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 12),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          if (session.teacherName != null) ...[
            const SizedBox(height: 2),
            Text(
              session.teacherName!,
              style: TextStyle(fontSize: 10, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة الإحصائيات العامة
  Widget _buildGeneralStatsCard(BuildContext context, TimetableStats stats) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'الإحصائيات العامة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'إجمالي الحصص',
                    '${stats.timetable.totalSessions}',
                    Icons.schedule,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'عدد المواد',
                    '${stats.timetable.allSubjects.length}',
                    Icons.subject,
                    Colors.green,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'عدد المعلمين',
                    '${stats.timetable.allTeachers.length}',
                    Icons.person,
                    Colors.orange,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'متوسط الحصص اليومية',
                    '${stats.averageDailySessions.toStringAsFixed(1)}',
                    Icons.today,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائية واحد
  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      margin: const EdgeInsets.symmetric(horizontal: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائيات المواد
  Widget _buildSubjectStatsCard(BuildContext context, TimetableStats stats) {
    final subjectCounts = stats.subjectCounts;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.subject, color: Colors.green),
                SizedBox(width: 8),
                Text(
                  'توزيع المواد',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ...subjectCounts.entries.map((entry) {
              final percentage =
                  (entry.value / stats.timetable.totalSessions * 100);
              return Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          entry.key,
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        Text(
                          '${entry.value} حصة (${percentage.toStringAsFixed(1)}%)',
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    LinearProgressIndicator(
                      value: percentage / 100,
                      backgroundColor: Colors.grey.shade200,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Colors.green.shade400,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائيات المعلمين
  Widget _buildTeacherStatsCard(BuildContext context, TimetableStats stats) {
    final teacherCounts = stats.teacherCounts;

    if (teacherCounts.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.person, color: Colors.orange),
                SizedBox(width: 8),
                Text(
                  'توزيع المعلمين',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            ...teacherCounts.entries.take(5).map((entry) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  children: [
                    CircleAvatar(
                      radius: 16,
                      backgroundColor: Colors.orange.shade100,
                      child: Text(
                        entry.key[0],
                        style: TextStyle(
                          color: Colors.orange.shade800,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        entry.key,
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${entry.value} حصة',
                        style: TextStyle(
                          color: Colors.orange.shade800,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة التوزيع اليومي
  Widget _buildDailyDistributionCard(
    BuildContext context,
    TimetableStats stats,
  ) {
    final dailyCounts = stats.dailyCounts;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.calendar_view_week, color: Colors.purple),
                SizedBox(width: 8),
                Text(
                  'التوزيع اليومي',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children:
                  TimetableModel.weekDays.map((day) {
                    final count = dailyCounts[day] ?? 0;
                    final maxCount = dailyCounts.values.reduce(
                      (a, b) => a > b ? a : b,
                    );
                    final height =
                        maxCount > 0 ? (count / maxCount * 60) + 20 : 20;

                    return Column(
                      children: [
                        Container(
                          width: 30,
                          height: height.toDouble(),
                          decoration: BoxDecoration(
                            color: Colors.purple.shade400,
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          day[0],
                          style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '$count',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      ],
                    );
                  }).toList(),
            ),
          ],
        ),
      ),
    );
  }

  /// عرض حوار البحث
  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Row(
              children: [
                Icon(Icons.search, color: Colors.blue),
                SizedBox(width: 8),
                Text('البحث في الجدول'),
              ],
            ),
            content: TextField(
              decoration: const InputDecoration(
                hintText: 'ابحث عن مادة أو معلم...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                ref.read(timetableSearchProvider.notifier).state = value;
              },
            ),
            actions: [
              TextButton(
                onPressed: () {
                  ref.read(timetableSearchProvider.notifier).state = '';
                  Navigator.of(context).pop();
                },
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }
}
