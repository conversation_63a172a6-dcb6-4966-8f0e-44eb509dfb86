import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/attendance_providers.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/reports_providers.dart';
import 'package:school_management_system/services/pdf_export_service.dart';
import 'package:school_management_system/admin_screens/student_detail_report_screen.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// A screen for viewing student reports, refactored with Riverpod.
class StudentReportsScreen extends ConsumerWidget {
  const StudentReportsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Column(
      children: [
        ReportFilters(),
        GenerateReportButton(),
        Expanded(
          child: FilteredStudentsList(),
        ),
      ],
    );
  }
}

/// A widget containing the search and class filter controls.
class ReportFilters extends ConsumerWidget {
  const ReportFilters({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classesAsyncValue = ref.watch(classesStreamProvider);
    final selectedClassId = ref.watch(selectedClassIdProvider);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              onChanged: (value) {
                ref.read(reportSearchQueryProvider.notifier).state = value;
              },
              decoration: const InputDecoration(
                labelText: 'ابحث عن طالب...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.all(Radius.circular(12.0)),
                ),
              ),
            ),
          ),
          const SizedBox(width: 10),
          Expanded(
            child: classesAsyncValue.when(
              data: (classes) => DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'تصفية حسب الصف',
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(12.0)),
                  ),
                ),
                value: selectedClassId,
                hint: const Text('اختر صفاً'),
                onChanged: (value) {
                  ref.read(selectedClassIdProvider.notifier).state = value;
                },
                items: [
                  const DropdownMenuItem<String>(
                    value: null,
                    child: Text('جميع الصفوف'),
                  ),
                  ...classes.map((ClassModel classModel) {
                    return DropdownMenuItem<String>(
                      value: classModel.id,
                      child: Text(classModel.name),
                    );
                  }).toList(),
                ],
              ),
              loading: () => const SizedBox.shrink(),
              error: (e, s) => const SizedBox.shrink(),
            ),
          ),
        ],
      ),
    );
  }
}

/// A button to generate the comprehensive PDF report for the selected class.
class GenerateReportButton extends ConsumerStatefulWidget {
  const GenerateReportButton({super.key});

  @override
  ConsumerState<GenerateReportButton> createState() => _GenerateReportButtonState();
}

class _GenerateReportButtonState extends ConsumerState<GenerateReportButton> {
  bool _isGenerating = false;

  Future<void> _generateReport() async {
    final selectedClassId = ref.read(selectedClassIdProvider);
    if (selectedClassId == null) return;

    setState(() => _isGenerating = true);

    try {
      final reportData = await ref.read(classReportProvider(selectedClassId).future);
      final className = ref.read(selectedClassNameProvider) ?? 'صف غير محدد';
      final pdfService = PdfExportService();
      if (mounted) {
        await pdfService.generateClassComprehensivePdf(context, className, reportData);
      }
    } catch (e, s) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل إنشاء التقرير: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isGenerating = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final selectedClassId = ref.watch(selectedClassIdProvider);

    if (selectedClassId == null) {
      return const SizedBox.shrink();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: _isGenerating
          ? const Center(child: LoadingIndicator())
          : ElevatedButton.icon(
              onPressed: _generateReport,
              icon: const Icon(Icons.picture_as_pdf),
              label: const Text('تنزيل تقرير مجمع للصف'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                minimumSize: const Size(double.infinity, 45),
              ),
            ),
    );
  }
}

/// A list widget to display filtered students.
class FilteredStudentsList extends ConsumerWidget {
  const FilteredStudentsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentsAsyncValue = ref.watch(reportStudentsStreamProvider);
    final filteredStudents = ref.watch(filteredReportStudentsProvider);

    return studentsAsyncValue.when(
      data: (students) {
        if (students.isEmpty) {
          return const Center(child: Text('لا يوجد طلاب.'));
        }
        if (filteredStudents.isEmpty) {
          return const Center(child: Text('لم يتم العثور على طلاب.'));
        }
        return ListView.builder(
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final student = filteredStudents[index];
            return CustomCard(
              child: ListTile(
                leading: CircleAvatar(child: Text(student.name.isNotEmpty ? student.name[0] : '')),
                title: Text(student.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                subtitle: Text(student.email),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => StudentDetailReportScreen(student: student),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => ErrorMessage(message: 'خطأ: $err'),
    );
  }
}
