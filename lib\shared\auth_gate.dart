import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:school_management_system/mobile_screens/guardian_home_page.dart';
import 'package:school_management_system/mobile_screens/login_screen.dart';
import 'package:school_management_system/mobile_screens/student_main_navigation.dart';
import 'package:school_management_system/teacher_screens/teacher_main_layout.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// بوابة المصادقة الذكية لتطبيق الجوال
///
/// هذا الويدجت يحدد نوع المستخدم (طالب أم ولي أمر) بعد تسجيل الدخول
/// ويوجهه إلى الواجهة المناسبة.
class AuthGate extends StatelessWidget {
  const AuthGate({super.key});

  /// دالة للتحقق من دور المستخدم من خلال البحث في مجموعات مختلفة في Firestore
  Future<String> _getUserRole(String uid) async {
    final firestore = FirebaseFirestore.instance;

    // 1. التحقق مما إذا كان المستخدم طالباً
    final studentDoc = await firestore.collection('students').doc(uid).get();
    if (studentDoc.exists) {
      return 'student';
    }

    // 2. التحقق مما إذا كان المستخدم ولي أمر
    final guardianDoc = await firestore.collection('guardians').doc(uid).get();
    if (guardianDoc.exists) {
      return 'guardian';
    }

    // 3. إذا لم يتم العثور عليه في أي من المجموعتين
    return 'unknown';
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        // في حالة الانتظار، عرض مؤشر تحميل
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        // إذا كان المستخدم قد سجل دخوله
        if (snapshot.hasData) {
          final user = snapshot.data!;

          // استخدام FutureBuilder للتحقق من دور المستخدم بشكل غير متزامن
          return FutureBuilder<String>(
            future: _getUserRole(user.uid),
            builder: (context, roleSnapshot) {
              // في حالة انتظار نتيجة التحقق من الدور
              if (roleSnapshot.connectionState == ConnectionState.waiting) {
                return const LoadingIndicator();
              }

              // إذا حدث خطأ أثناء التحقق
              if (roleSnapshot.hasError) {
                return const Scaffold(
                  body: Center(
                    child: Text('حدث خطأ أثناء التحقق من صلاحياتك.'),
                  ),
                );
              }

              final role = roleSnapshot.data;

              // توجيه المستخدم بناءً على دوره
              switch (role) {
                case 'student':
                  return StudentMainNavigation();
                case 'guardian':
                  return const GuardianHomePage();
                case 'teacher':
                  return const TeacherMainLayout();
                default:
                  // إذا كان الدور غير معروف، يتم تسجيل الخروج وإعادته لشاشة الدخول
                  return const LoginScreen();
              }
            },
          );
        }

        // إذا لم يكن المستخدم مسجلاً دخوله، عرض شاشة الدخول
        return const LoginScreen();
      },
    );
  }
}
