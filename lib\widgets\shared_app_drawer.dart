import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:school_management_system/mobile_screens/public_home_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// ويدجت القائمة الجانبية المشتركة والمحسّنة للتطبيق
///
/// تعرض معلومات المستخدم المسجل دخوله وتوفر وصولاً سهلاً
/// إلى الشاشة العامة وزر تسجيل الخروج.
class SharedAppDrawer extends StatefulWidget {
  const SharedAppDrawer({super.key});

  @override
  State<SharedAppDrawer> createState() => _SharedAppDrawerState();
}

class _SharedAppDrawerState extends State<SharedAppDrawer> {
  final User? _user = FirebaseAuth.instance.currentUser;
  String _userName = 'جار التحميل...';
  String _userEmail = '';

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  /// دالة لجلب بيانات المستخدم (الاسم) من Firestore
  Future<void> _loadUserData() async {
    if (_user == null) {
      if (mounted) setState(() => _userName = 'زائر');
      return;
    }

    _userEmail = _user!.email ?? 'بريد إلكتروني غير متوفر';

    // في تطبيق حقيقي، قد يتم تخزين دور المستخدم في 'claims' أو في مستند 'users' مركزي
    // هنا، سنبحث في المجموعات المحتملة (users, guardians, students)
    final collectionsToSearch = ['users', 'guardians', 'students'];
    for (String collection in collectionsToSearch) {
      try {
        final doc = await FirebaseFirestore.instance.collection(collection).doc(_user!.uid).get();
        if (doc.exists && doc.data()!.containsKey('name')) {
          if (mounted) {
            setState(() {
              _userName = (doc.data()! as Map<String, dynamic>)['name'];
            });
          }
          return; // تم العثور على الاسم، أوقف البحث
        }
      } catch (e) {
        // تجاهل الخطأ إذا كانت المجموعة غير موجودة واستمر
        print('Could not search in collection $collection: $e');
      }
    }

    // إذا لم يتم العثور على الاسم في أي مكان
    if (mounted) setState(() => _userName = 'مستخدم');
  }

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          UserAccountsDrawerHeader(
            accountName: Text(
              _userName,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                fontFamily: GoogleFonts.cairo().fontFamily,
              ),
            ),
            accountEmail: Text(
              _userEmail,
              style: TextStyle(
                fontFamily: GoogleFonts.cairo().fontFamily,
              ),
            ),
            currentAccountPicture: CircleAvatar(
              backgroundColor: Colors.white,
              child: Text(
                _userName.isNotEmpty ? _userName[0].toUpperCase() : 'U',
                style: TextStyle(fontSize: 40.0, color: Theme.of(context).primaryColor),
              ),
            ),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor,
            ),
          ),
          ListTile(
            leading: const Icon(Icons.home_work_outlined),
            title: const Text('الواجهة العامة'),
            onTap: () {
              Navigator.of(context).pop();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const PublicHomeScreen()),
                (Route<dynamic> route) => false,
              );
            },
          ),
          const Divider(),
          ListTile(
            leading: const Icon(Icons.logout, color: Colors.redAccent),
            title: const Text('تسجيل الخروج', style: TextStyle(color: Colors.redAccent)),
            onTap: () async {
              Navigator.of(context).pop();
              await FirebaseService().signOut();
              Navigator.of(context).pushAndRemoveUntil(
                MaterialPageRoute(builder: (context) => const PublicHomeScreen()),
                (Route<dynamic> route) => false,
              );
            },
          ),
        ],
      ),
    );
  }
}
