
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/competition_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// ويدجت متخصص لعرض وإدارة المسابقات في لوحة التحكم.
class CompetitionsManagementTab extends ConsumerWidget {
  final Function(CompetitionModel) onEdit;
  final Function(CompetitionModel) onDelete;

  const CompetitionsManagementTab({
    super.key,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final competitionsAsyncValue = ref.watch(competitionsStreamProvider);

    return competitionsAsyncValue.when(
      loading: () => const LoadingIndicator(),
      error: (err, stack) =>
          ErrorMessage(message: 'حدث خطأ أثناء جلب البيانات: $err'),
      data: (competitions) {
        if (competitions.isEmpty) {
          return const Center(
              child: Text('لا توجد مسابقات حالياً. قم بإضافة مسابقة جديدة.'));
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: competitions.length,
          itemBuilder: (context, index) {
            final competition = competitions[index];
            return CustomCard(
              child: ListTile(
                leading: const Icon(Icons.emoji_events,
                    color: Colors.amber, size: 40),
                title: Text(competition.title,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                subtitle: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(competition.description),
                    const SizedBox(height: 4),
                    if (competition.startDate != null)
                      Text(
                        'يبدأ في: ${DateFormat.yMMMd('ar_SA').format(competition.startDate!)}',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                  ],
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // زر التعديل
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      tooltip: 'تعديل',
                      onPressed: () => onEdit(competition),
                    ),
                    // زر الحذف
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف',
                      onPressed: () => onDelete(competition),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
