import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/behavior_evaluation_model.dart';
import 'package:school_management_system/providers/behavior_evaluation_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/custom_card.dart';

/// شاشة إدارة تقييمات السلوك المتقدمة
///
/// تقدم هذه الشاشة واجهة شاملة ومتطورة لإدارة جميع تقييمات السلوك في النظام المدرسي
/// مع أدوات متقدمة للفلترة والبحث والتحليل والإدارة
///
/// الميزات الرئيسية:
/// - عرض شامل لجميع تقييمات السلوك مع فلترة متقدمة
/// - إحصائيات وتحليلات في الوقت الفعلي
/// - أدوات إدارية للموافقة والتعديل والحذف
/// - تقارير مفصلة وقابلة للتصدير
/// - واجهة حديثة تشبه أنظمة إدارة المشاريع المتقدمة
/// - دعم البحث النصي والفلترة المتعددة المعايير
/// - لوحة تحكم تفاعلية للإحصائيات
/// - إدارة خطط تحسين السلوك
/// - نظام إشعارات للمتابعة والتذكيرات
class BehaviorEvaluationManagementScreen extends ConsumerStatefulWidget {
  /// معرف المدير الحالي
  final String adminId;

  /// اسم المدير الحالي
  final String adminName;

  /// دور المدير في النظام
  final String adminRole;

  const BehaviorEvaluationManagementScreen({
    super.key,
    required this.adminId,
    required this.adminName,
    required this.adminRole,
  });

  @override
  ConsumerState<BehaviorEvaluationManagementScreen> createState() =>
      _BehaviorEvaluationManagementScreenState();
}

class _BehaviorEvaluationManagementScreenState
    extends ConsumerState<BehaviorEvaluationManagementScreen>
    with TickerProviderStateMixin {
  /// متحكم التبويبات الرئيسية
  late TabController _tabController;

  /// متحكم البحث النصي
  final TextEditingController _searchController = TextEditingController();

  /// فهرس التبويب المحدد حالياً
  int _selectedTabIndex = 0;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم التبويبات مع 4 تبويبات رئيسية
    _tabController = TabController(length: 4, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedTabIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],

      // شريط التطبيق المتقدم
      appBar: _buildAdvancedAppBar(),

      // محتوى الشاشة الرئيسي
      body: Column(
        children: [
          // شريط الفلاتر والبحث
          _buildFiltersAndSearchBar(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب جميع التقييمات
                _buildAllEvaluationsTab(),

                // تبويب التقييمات المعلقة
                _buildPendingEvaluationsTab(),

                // تبويب الإحصائيات والتحليلات
                _buildStatisticsTab(),

                // تبويب الإعدادات والأدوات
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),

      // زر الإجراء العائم لإنشاء تقييم جديد
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء شريط التطبيق المتقدم
  PreferredSizeWidget _buildAdvancedAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: Colors.indigo[800],
      foregroundColor: Colors.white,

      // العنوان مع الأيقونة
      title: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.psychology, color: Colors.white, size: 24),
          ),
          const SizedBox(width: 12),
          const Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'إدارة تقييمات السلوك',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Text(
                'نظام التقييم السلوكي المتقدم',
                style: TextStyle(fontSize: 12, color: Colors.white70),
              ),
            ],
          ),
        ],
      ),

      // أزرار الإجراءات
      actions: [
        // زر التصدير
        IconButton(
          icon: const Icon(Icons.download, color: Colors.white),
          onPressed: () => _exportEvaluations(),
          tooltip: 'تصدير التقييمات',
        ),

        // زر الإعدادات
        IconButton(
          icon: const Icon(Icons.settings, color: Colors.white),
          onPressed: () => _showSettingsDialog(),
          tooltip: 'إعدادات النظام',
        ),

        // زر المساعدة
        IconButton(
          icon: const Icon(Icons.help_outline, color: Colors.white),
          onPressed: () => _showHelpDialog(),
          tooltip: 'المساعدة',
        ),
      ],

      // تبويبات فرعية
      bottom: TabBar(
        controller: _tabController,
        indicatorColor: Colors.white,
        indicatorWeight: 3,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white70,
        labelStyle: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        tabs: const [
          Tab(icon: Icon(Icons.list_alt), text: 'جميع التقييمات'),
          Tab(icon: Icon(Icons.pending_actions), text: 'المعلقة'),
          Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
          Tab(icon: Icon(Icons.tune), text: 'الإعدادات'),
        ],
      ),
    );
  }

  /// بناء شريط الفلاتر والبحث
  Widget _buildFiltersAndSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // شريط البحث الرئيسي
          Row(
            children: [
              // حقل البحث
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText:
                        'البحث في التقييمات (الطالب، المُقيِّم، الوصف...)',
                    prefixIcon: const Icon(Icons.search, color: Colors.grey),
                    suffixIcon:
                        _searchController.text.isNotEmpty
                            ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                ref
                                    .read(
                                      behaviorEvaluationSearchQueryProvider
                                          .notifier,
                                    )
                                    .state = '';
                              },
                            )
                            : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.indigo[600]!),
                    ),
                    filled: true,
                    fillColor: Colors.grey[50],
                  ),
                  onChanged: (value) {
                    ref
                        .read(behaviorEvaluationSearchQueryProvider.notifier)
                        .state = value;
                  },
                ),
              ),

              const SizedBox(width: 12),

              // زر الفلاتر المتقدمة
              Container(
                decoration: BoxDecoration(
                  color: Colors.indigo[600],
                  borderRadius: BorderRadius.circular(12),
                ),
                child: IconButton(
                  icon: const Icon(Icons.filter_list, color: Colors.white),
                  onPressed: () => _showAdvancedFiltersDialog(),
                  tooltip: 'فلاتر متقدمة',
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // شريط الفلاتر السريعة
          _buildQuickFiltersRow(),
        ],
      ),
    );
  }

  /// بناء رقاقة فلتر سريع
  Widget _buildQuickFilterChip({
    required String label,
    required bool isSelected,
    required VoidCallback onTap,
    required Color color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected ? color : Colors.transparent,
          border: Border.all(color: color),
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : color,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }

  /// تبويب جميع التقييمات
  Widget _buildAllEvaluationsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم مع إحصائيات سريعة
          _buildSectionHeader(
            title: 'جميع تقييمات السلوك',
            subtitle: 'عرض وإدارة جميع التقييمات في النظام',
            icon: Icons.list_alt,
          ),

          const SizedBox(height: 16),

          // قائمة التقييمات
          Expanded(child: _buildEvaluationsList()),
        ],
      ),
    );
  }

  /// تبويب التقييمات المعلقة
  Widget _buildPendingEvaluationsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            title: 'التقييمات المعلقة',
            subtitle: 'التقييمات التي تحتاج موافقة أو مراجعة',
            icon: Icons.pending_actions,
          ),

          const SizedBox(height: 16),

          Expanded(child: _buildPendingEvaluationsList()),
        ],
      ),
    );
  }

  /// تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            title: 'الإحصائيات والتحليلات',
            subtitle: 'تحليلات شاملة لتقييمات السلوك',
            icon: Icons.analytics,
          ),

          const SizedBox(height: 16),

          Expanded(child: _buildStatisticsView()),
        ],
      ),
    );
  }

  /// تبويب الإعدادات
  Widget _buildSettingsTab() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionHeader(
            title: 'الإعدادات والأدوات',
            subtitle: 'إعدادات النظام والأدوات المساعدة',
            icon: Icons.tune,
          ),

          const SizedBox(height: 16),

          Expanded(child: _buildSettingsView()),
        ],
      ),
    );
  }

  /// بناء رأس القسم
  Widget _buildSectionHeader({
    required String title,
    required String subtitle,
    required IconData icon,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.indigo[100],
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(icon, color: Colors.indigo[700], size: 24),
        ),

        const SizedBox(width: 16),

        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(fontSize: 14, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// بناء قائمة التقييمات
  Widget _buildEvaluationsList() {
    // TODO: تنفيذ قائمة التقييمات مع البيانات الفعلية
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.psychology_outlined, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'قائمة التقييمات قيد التطوير',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم عرض جميع تقييمات السلوك هنا',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء قائمة التقييمات المعلقة
  Widget _buildPendingEvaluationsList() {
    // TODO: تنفيذ قائمة التقييمات المعلقة
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.pending_actions_outlined, size: 64, color: Colors.orange),
          SizedBox(height: 16),
          Text(
            'التقييمات المعلقة قيد التطوير',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'سيتم عرض التقييمات التي تحتاج موافقة هنا',
            style: TextStyle(fontSize: 14, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// بناء عرض الإحصائيات
  Widget _buildStatisticsView() {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      children: [
        _buildStatCard(
          title: 'إجمالي التقييمات',
          value: '1,234',
          icon: Icons.assessment,
          color: Colors.blue,
          trend: '+12%',
        ),
        _buildStatCard(
          title: 'التقييمات الإيجابية',
          value: '856',
          icon: Icons.thumb_up,
          color: Colors.green,
          trend: '+8%',
        ),
        _buildStatCard(
          title: 'التقييمات السلبية',
          value: '234',
          icon: Icons.thumb_down,
          color: Colors.red,
          trend: '-5%',
        ),
        _buildStatCard(
          title: 'متوسط التقييم',
          value: '4.2',
          icon: Icons.star,
          color: Colors.orange,
          trend: '+0.3',
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String trend,
  }) {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Icon(icon, color: color, size: 32),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: color.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    trend,
                    style: TextStyle(
                      color: color,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            Text(
              value,
              style: const TextStyle(
                fontSize: 28,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),

            const SizedBox(height: 4),

            Text(
              title,
              style: TextStyle(fontSize: 14, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء صف الفلاتر السريعة
  Widget _buildQuickFiltersRow() {
    final filters = ref.watch(behaviorEvaluationFiltersProvider);

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          // فلتر نوع السلوك
          _buildQuickFilterChip(
            label: 'إيجابي',
            isSelected: filters.behaviorType == BehaviorType.positive,
            onTap: () => _toggleBehaviorTypeFilter(BehaviorType.positive),
            color: Colors.green,
          ),

          const SizedBox(width: 8),

          _buildQuickFilterChip(
            label: 'سلبي',
            isSelected: filters.behaviorType == BehaviorType.negative,
            onTap: () => _toggleBehaviorTypeFilter(BehaviorType.negative),
            color: Colors.red,
          ),

          const SizedBox(width: 8),

          _buildQuickFilterChip(
            label: 'محايد',
            isSelected: filters.behaviorType == BehaviorType.neutral,
            onTap: () => _toggleBehaviorTypeFilter(BehaviorType.neutral),
            color: Colors.grey,
          ),

          const SizedBox(width: 16),

          // فلتر مستوى الشدة
          _buildQuickFilterChip(
            label: 'عالي الشدة',
            isSelected: filters.severityLevel == SeverityLevel.high,
            onTap: () => _toggleSeverityLevelFilter(SeverityLevel.high),
            color: Colors.orange,
          ),

          const SizedBox(width: 8),

          _buildQuickFilterChip(
            label: 'شديد',
            isSelected: filters.severityLevel == SeverityLevel.severe,
            onTap: () => _toggleSeverityLevelFilter(SeverityLevel.severe),
            color: Colors.red[800]!,
          ),

          const SizedBox(width: 16),

          // زر إزالة جميع الفلاتر
          if (filters.hasActiveFilters)
            TextButton.icon(
              onPressed: () {
                ref
                    .read(behaviorEvaluationFiltersProvider.notifier)
                    .clearFilters();
              },
              icon: const Icon(Icons.clear_all, size: 16),
              label: const Text('إزالة الفلاتر'),
              style: TextButton.styleFrom(
                foregroundColor: Colors.grey[600],
                textStyle: const TextStyle(fontSize: 12),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء عرض الإعدادات
  Widget _buildSettingsView() {
    return ListView(
      children: [
        // إعدادات النظام
        _buildSettingsSection(
          title: 'إعدادات النظام',
          children: [
            _buildSettingsTile(
              title: 'إعدادات التقييم',
              subtitle: 'تخصيص معايير ونقاط التقييم',
              icon: Icons.tune,
              onTap: () => _showEvaluationSettings(),
            ),
            _buildSettingsTile(
              title: 'إعدادات الإشعارات',
              subtitle: 'إدارة إشعارات أولياء الأمور',
              icon: Icons.notifications,
              onTap: () => _showNotificationSettings(),
            ),
            _buildSettingsTile(
              title: 'إعدادات التقارير',
              subtitle: 'تخصيص التقارير والتصدير',
              icon: Icons.report,
              onTap: () => _showReportSettings(),
            ),
          ],
        ),

        const SizedBox(height: 24),

        // أدوات النظام
        _buildSettingsSection(
          title: 'أدوات النظام',
          children: [
            _buildSettingsTile(
              title: 'نسخ احتياطي',
              subtitle: 'إنشاء نسخة احتياطية من البيانات',
              icon: Icons.backup,
              onTap: () => _createBackup(),
            ),
            _buildSettingsTile(
              title: 'استيراد البيانات',
              subtitle: 'استيراد تقييمات من ملف خارجي',
              icon: Icons.upload_file,
              onTap: () => _importData(),
            ),
            _buildSettingsTile(
              title: 'تنظيف البيانات',
              subtitle: 'حذف التقييمات القديمة',
              icon: Icons.cleaning_services,
              onTap: () => _cleanupData(),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء قسم إعدادات
  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  /// بناء عنصر إعدادات
  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return CustomCard(
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: Colors.indigo[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.indigo[700]),
        ),
        title: Text(title, style: const TextStyle(fontWeight: FontWeight.w600)),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => _showCreateEvaluationDialog(),
      backgroundColor: Colors.indigo[600],
      foregroundColor: Colors.white,
      icon: const Icon(Icons.add),
      label: const Text('تقييم جديد'),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// تبديل فلتر نوع السلوك
  void _toggleBehaviorTypeFilter(BehaviorType behaviorType) {
    final currentFilter = ref.read(behaviorEvaluationFiltersProvider);
    final newBehaviorType =
        currentFilter.behaviorType == behaviorType ? null : behaviorType;

    ref
        .read(behaviorEvaluationFiltersProvider.notifier)
        .updateBehaviorType(newBehaviorType);
  }

  /// تبديل فلتر مستوى الشدة
  void _toggleSeverityLevelFilter(SeverityLevel severityLevel) {
    final currentFilter = ref.read(behaviorEvaluationFiltersProvider);
    final newSeverityLevel =
        currentFilter.severityLevel == severityLevel ? null : severityLevel;

    ref
        .read(behaviorEvaluationFiltersProvider.notifier)
        .updateSeverityLevel(newSeverityLevel);
  }

  /// عرض حوار الفلاتر المتقدمة
  void _showAdvancedFiltersDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلاتر متقدمة'),
            content: const Text('حوار الفلاتر المتقدمة قيد التطوير'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار إنشاء تقييم جديد
  void _showCreateEvaluationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إنشاء تقييم جديد'),
            content: const Text('نموذج إنشاء التقييم قيد التطوير'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إنشاء'),
              ),
            ],
          ),
    );
  }

  /// تصدير التقييمات
  void _exportEvaluations() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('ميزة التصدير قيد التطوير'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض حوار الإعدادات
  void _showSettingsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعدادات النظام'),
            content: const Text('إعدادات النظام قيد التطوير'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار المساعدة
  void _showHelpDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('المساعدة'),
            content: const Text('دليل الاستخدام قيد التطوير'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// عرض إعدادات التقييم
  void _showEvaluationSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات التقييم قيد التطوير')),
    );
  }

  /// عرض إعدادات الإشعارات
  void _showNotificationSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات الإشعارات قيد التطوير')),
    );
  }

  /// عرض إعدادات التقارير
  void _showReportSettings() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('إعدادات التقارير قيد التطوير')),
    );
  }

  /// إنشاء نسخة احتياطية
  void _createBackup() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('النسخ الاحتياطي قيد التطوير')),
    );
  }

  /// استيراد البيانات
  void _importData() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('استيراد البيانات قيد التطوير')),
    );
  }

  /// تنظيف البيانات
  void _cleanupData() {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('تنظيف البيانات قيد التطوير')));
  }
}
