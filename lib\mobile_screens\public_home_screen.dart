import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/mobile_screens/about_school_screen_mobile.dart';
import 'package:school_management_system/mobile_screens/competitions_screen.dart';
import 'package:school_management_system/mobile_screens/login_screen.dart';
import 'package:school_management_system/mobile_screens/public_notifications_screen.dart';
import 'package:school_management_system/mobile_screens/school_staff_screen.dart';
import 'package:school_management_system/mobile_screens/student_activities_screen.dart';
import 'package:school_management_system/mobile_screens/student_lessons_screen.dart';
import 'package:school_management_system/providers/dashboard_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة البداية العامة للتطبيق (واجهة الزائر)
class PublicHomeScreen extends StatefulWidget {
  const PublicHomeScreen({super.key});

  @override
  State<PublicHomeScreen> createState() => _PublicHomeScreenState();
}

class _PublicHomeScreenState extends State<PublicHomeScreen> {
  int _selectedIndex = 0;

  // دالة لتغيير التبويب من داخل الويدجت
  void _navigateToPage(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = <Widget>[
      PublicDashboard(navigateToPage: _navigateToPage), // لوحة المعلومات الرئيسية
      const StudentLessonsScreen(),
      const StudentActivitiesScreen(),
      const SchoolStaffScreen(),
      const AboutSchoolScreenMobile(),
    ];
  }

  static const List<String> _pageTitles = [
    'الرئيسية',
    'الدروس النموذجية',
    'الفعاليات والأنشطة',
    'الكادر التعليمي',
    'عن المدرسة',
  ];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_pageTitles[_selectedIndex]),
        actions: [
          IconButton(
            tooltip: 'الإشعارات العامة',
            icon: const Icon(Icons.notifications_outlined),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => const PublicNotificationsScreen()),
              );
            },
          ),
          Padding(
            padding:
                const EdgeInsets.symmetric(horizontal: 8.0, vertical: 6.0),
            child: ElevatedButton.icon(
              icon: const Icon(Icons.login),
              label: const Text('تسجيل الدخول'),
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const LoginScreen()),
                );
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      ),
      body: IndexedStack(
        index: _selectedIndex,
        children: _pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
              icon: Icon(Icons.home_outlined),
              activeIcon: Icon(Icons.home),
              label: 'الرئيسية'),
          BottomNavigationBarItem(
              icon: Icon(Icons.book_outlined),
              activeIcon: Icon(Icons.book),
              label: 'الدروس'),
          BottomNavigationBarItem(
              icon: Icon(Icons.local_activity_outlined),
              activeIcon: Icon(Icons.local_activity),
              label: 'الأنشطة'),
          BottomNavigationBarItem(
              icon: Icon(Icons.people_outline),
              activeIcon: Icon(Icons.people),
              label: 'الكادر'),
          BottomNavigationBarItem(
              icon: Icon(Icons.school_outlined),
              activeIcon: Icon(Icons.school),
              label: 'عن المدرسة'),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey.shade600,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}

/// لوحة المعلومات الرئيسية للزوار
class PublicDashboard extends ConsumerWidget {
  final Function(int) navigateToPage;
  const PublicDashboard({super.key, required this.navigateToPage});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // --- قسم الترحيب ---
          Text(
            'أهلاً بكم في مدرستنا',
            style: Theme.of(context)
                .textTheme
                .headlineMedium
                ?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            'البوابة الرقمية للزوار والطلاب وأولياء الأمور. استكشفوا أحدث الأنشطة والدروس النموذجية وتعرفوا على كادرنا المتميز.',
            style: Theme.of(context).textTheme.titleSmall,
          ),
          const Divider(height: 32),

          // --- قسم الإحصائيات ---
          ref.watch(publicDashboardStatsProvider).when(
                loading: () => const LoadingIndicator(),
                error: (err, stack) => Text('خطأ: $err'),
                data: (stats) => GridView.count(
                  crossAxisCount: 3,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  children: [
                    _buildStatCard(context, 'الدروس',
                        stats['lessons'] ?? 0, Icons.book, Colors.orange),
                    _buildStatCard(
                        context,
                        'الأنشطة',
                        stats['activities'] ?? 0,
                        Icons.local_activity,
                        Colors.blue),
                    _buildStatCard(context, 'الكادر', stats['staff'] ?? 0,
                        Icons.people, Colors.green),
                  ],
                ),
              ),
          const Divider(height: 32),

          // --- قسم آخر الأنشطة القادمة ---
          Text(
            'الفعاليات القادمة',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          _buildUpcomingActivities(context, ref),

          const Divider(height: 32),

          // --- قسم روابط سريعة ---
          Text(
            'استكشف المزيد',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 16),
          GridView.count(
            crossAxisCount: 2,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 2.5,
            children: [
              _buildQuickLinkCard(context, 'الدروس النموذجية', Icons.menu_book,
                  () => navigateToPage(1)),
              _buildQuickLinkCard(context, 'الكادر التعليمي', Icons.group,
                  () => navigateToPage(3)),
              _buildQuickLinkCard(
                  context, 'المسابقات', Icons.emoji_events_outlined, () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => const CompetitionsScreen()),
                );
              }),
              _buildQuickLinkCard(context, 'كل الفعاليات', Icons.event,
                  () => navigateToPage(2)),
            ],
          ),
        ],
      ),
    );
  }

  // ويدجت مساعد لبطاقة الإحصائيات
  Widget _buildStatCard(BuildContext context, String title, int count,
      IconData icon, Color color) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      color: color.withOpacity(0.1),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              count.toString(),
              style: TextStyle(
                  fontSize: 22, fontWeight: FontWeight.bold, color: color),
            ),
            FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(title,
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center),
            ),
          ],
        ),
      ),
    );
  }

  // ويدجت مساعد لعرض الأنشطة القادمة
  Widget _buildUpcomingActivities(BuildContext context, WidgetRef ref) {
    return ref.watch(upcomingActivitiesProvider).when(
          loading: () => const LoadingIndicator(),
          error: (err, stack) => Text('خطأ: $err'),
          data: (activities) {
            if (activities.isEmpty) {
              return const Center(child: Text('لا توجد فعاليات قادمة حالياً.'));
            }
            return SizedBox(
              height: 200,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: activities.length,
                itemBuilder: (context, index) {
                  final activity = activities[index];
                  return Card(
                    elevation: 2,
                    margin: const EdgeInsets.only(left: 16),
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12)),
                    child: SizedBox(
                      width: 250,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Expanded(
                            child: ClipRRect(
                              borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(12)),
                              child: activity.imageUrls.isNotEmpty
                                  ? Image.network(
                                      activity.imageUrls.first,
                                      width: double.infinity,
                                      fit: BoxFit.cover,
                                    )
                                  : Container(
                                      color: Colors.grey[200],
                                      child: const Center(
                                          child: Icon(Icons.image, size: 50))),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  activity.title,
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  DateFormat.yMMMd('ar').format(activity.date),
                                  style: Theme.of(context).textTheme.bodySmall,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            );
          },
        );
  }

  // ويدجت مساعد لبطاقة الروابط السريعة
  Widget _buildQuickLinkCard(
      BuildContext context, String title, IconData icon, VoidCallback onTap) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(icon, color: Theme.of(context).primaryColor),
              const SizedBox(width: 8),
              Text(title, style: const TextStyle(fontWeight: FontWeight.bold)),
            ],
          ),
        ),
      ),
    );
  }
}
