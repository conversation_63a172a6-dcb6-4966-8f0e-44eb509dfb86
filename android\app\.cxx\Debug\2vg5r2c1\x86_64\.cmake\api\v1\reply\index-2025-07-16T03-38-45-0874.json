{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-e5757807e6aed7d49d64.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-2d9c9e684349a4b02d09.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-8fa890d11140bfdeceb3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-2d9c9e684349a4b02d09.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-8fa890d11140bfdeceb3.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-e5757807e6aed7d49d64.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}