import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import '../providers/fees_providers.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';

/// شاشة لعرض التفاصيل المالية للطالب (الرسوم والمدفوعات)
class StudentFeesScreen extends ConsumerWidget {
  final String studentId;

  const StudentFeesScreen({Key? key, required this.studentId})
      : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final financialDetailsAsyncValue =
        ref.watch(studentFinancialDetailsProvider(studentId));

    return Scaffold(
      appBar: AppBar(title: const Text('الوضع المالي'), centerTitle: true),
      body: financialDetailsAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error: (err, stack) => ErrorMessage(
          message: 'حدث خطأ في جلب البيانات المالية: $err',
        ),
        data: (financialData) {
          if (financialData.isEmpty) {
            return const Center(
              child: Text(
                'لا توجد بيانات مالية متاحة.',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            );
          }

          final double totalAssigned = financialData['totalAssigned'] ?? 0.0;
          final double totalPaid = financialData['totalPaid'] ?? 0.0;
          final double totalRemaining = financialData['totalRemaining'] ?? 0.0;
          final List assignedFees = financialData['assignedFees'] ?? [];
          final List payments = financialData['payments'] ?? [];

          return DefaultTabController(
            length: 2, // عدد التبويبات: الرسوم والمدفوعات
            child: Column(
              children: [
                // بطاقات الإحصائيات في الأعلى
                _buildSummaryCards(totalAssigned, totalPaid, totalRemaining),
                // شريط التبويبات
                const TabBar(
                  tabs: [
                    Tab(text: 'الرسوم المفروضة'),
                    Tab(text: 'سجل المدفوعات'),
                  ],
                ),
                // محتوى التبويبات
                Expanded(
                  child: TabBarView(
                    children: [
                      _buildAssignedFeesList(assignedFees),
                      _buildPaymentsList(payments),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // ويدجت لعرض بطاقات الملخص المالي
  Widget _buildSummaryCards(
    double totalAssigned,
    double totalPaid,
    double totalRemaining,
  ) {
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_YM',
      symbol: 'ر.ي',
    );
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        children: [
          _buildStatCard(
            'المبلغ الإجمالي',
            currencyFormat.format(totalAssigned),
            Colors.blue,
          ),
          _buildStatCard(
            'المبلغ المدفوع',
            currencyFormat.format(totalPaid),
            Colors.green,
          ),
          _buildStatCard(
            'المبلغ المتبقي',
            currencyFormat.format(totalRemaining),
            Colors.red,
          ),
        ],
      ),
    );
  }

  // ويدجت لعرض قائمة الرسوم المفروضة
  Widget _buildAssignedFeesList(List fees) {
    if (fees.isEmpty) {
      return const Center(child: Text('لا توجد رسوم مفروضة حالياً.'));
    }
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_YM',
      symbol: 'ر.ي',
    );

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: fees.length,
      itemBuilder: (context, index) {
        final fee = fees[index];
        final total = fee['amount_total'] ?? 0.0;
        final paid = fee['amount_paid'] ?? 0.0;
        final remaining = total - paid;
        final isPaid = remaining <= 0;

        return Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 6.0),
          child: ListTile(
            title: Text(
              fee['fee_type_name'] ?? 'رسوم غير محددة',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'الإجمالي: ${currencyFormat.format(total)}\nالمدفوع: ${currencyFormat.format(paid)}',
            ),
            trailing: Text(
              isPaid
                  ? 'مدفوع بالكامل'
                  : 'المتبقي: ${currencyFormat.format(remaining)}',
              style: TextStyle(
                color: isPaid ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  // ويدجت لعرض قائمة المدفوعات
  Widget _buildPaymentsList(List payments) {
    if (payments.isEmpty) {
      return const Center(child: Text('لا يوجد سجل للمدفوعات.'));
    }
    final currencyFormat = NumberFormat.currency(
      locale: 'ar_YM',
      symbol: 'ر.ي',
    );

    return ListView.builder(
      padding: const EdgeInsets.all(8.0),
      itemCount: payments.length,
      itemBuilder: (context, index) {
        final payment = payments[index];
        final DateTime date = (payment['payment_date'] as Timestamp).toDate();
        final formattedDate = DateFormat.yMMMMd('ar_YM').format(date);

        return Card(
          elevation: 2,
          margin: const EdgeInsets.symmetric(vertical: 6.0),
          child: ListTile(
            leading: const Icon(Icons.payment, color: Colors.green),
            title: Text(
              'مبلغ: ${currencyFormat.format(payment['amount'] ?? 0.0)}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              'تاريخ الدفع: $formattedDate\nطريقة الدفع: ${payment['payment_method'] ?? 'غير محدد'}',
            ),
          ),
        );
      },
    );
  }

  // ويدجت مساعد لبطاقات الإحصائيات
  Widget _buildStatCard(String title, String value, Color color) {
    return Expanded(
      child: Card(
        color: color.withOpacity(0.1),
        elevation: 0,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            children: [
              Text(
                title,
                style: const TextStyle(fontSize: 14, color: Colors.black87),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
