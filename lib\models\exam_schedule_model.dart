import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/exam_model.dart';

/// نموذج جلسة امتحان واحدة
/// يمثل امتحان مادة واحدة في يوم وقت محدد
class ExamSession {
  final String id;
  final String examId;            // معرف الامتحان الرئيسي
  final String subjectId;         // معرف المادة
  final String subjectName;       // اسم المادة
  final String classId;           // معرف الصف
  final String className;         // اسم الصف
  final DateTime examDate;        // تاريخ الامتحان
  final TimeOfDay startTime;      // وقت البداية
  final TimeOfDay endTime;        // وقت النهاية
  final Duration duration;        // مدة الامتحان
  final String roomId;            // معرف القاعة
  final String roomName;          // اسم القاعة
  final List<String> supervisorIds; // معرفات المراقبين
  final List<String> supervisorNames; // أسماء المراقبين
  final int totalMarks;           // الدرجة الكاملة
  final String? instructions;     // تعليمات خاصة بالامتحان
  final List<String> studentIds;  // معرفات الطلاب المشاركين
  final ExamSessionStatus status; // حالة جلسة الامتحان
  final DateTime createdAt;       // تاريخ الإنشاء
  final String createdBy;         // منشئ الجلسة

  const ExamSession({
    required this.id,
    required this.examId,
    required this.subjectId,
    required this.subjectName,
    required this.classId,
    required this.className,
    required this.examDate,
    required this.startTime,
    required this.endTime,
    required this.duration,
    required this.roomId,
    required this.roomName,
    required this.supervisorIds,
    required this.supervisorNames,
    required this.totalMarks,
    this.instructions,
    required this.studentIds,
    required this.status,
    required this.createdAt,
    required this.createdBy,
  });

  /// إنشاء جلسة امتحان من مستند Firestore
  factory ExamSession.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ExamSession(
      id: doc.id,
      examId: data['examId'] as String? ?? '',
      subjectId: data['subjectId'] as String? ?? '',
      subjectName: data['subjectName'] as String? ?? '',
      classId: data['classId'] as String? ?? '',
      className: data['className'] as String? ?? '',
      examDate: (data['examDate'] as Timestamp).toDate(),
      startTime: TimeOfDay(
        hour: data['startHour'] as int? ?? 8,
        minute: data['startMinute'] as int? ?? 0,
      ),
      endTime: TimeOfDay(
        hour: data['endHour'] as int? ?? 10,
        minute: data['endMinute'] as int? ?? 0,
      ),
      duration: Duration(minutes: data['durationMinutes'] as int? ?? 120),
      roomId: data['roomId'] as String? ?? '',
      roomName: data['roomName'] as String? ?? '',
      supervisorIds: List<String>.from(data['supervisorIds'] ?? []),
      supervisorNames: List<String>.from(data['supervisorNames'] ?? []),
      totalMarks: data['totalMarks'] as int? ?? 100,
      instructions: data['instructions'] as String?,
      studentIds: List<String>.from(data['studentIds'] ?? []),
      status: ExamSessionStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => ExamSessionStatus.scheduled,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
    );
  }

  /// تحويل جلسة الامتحان إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'examId': examId,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'classId': classId,
      'className': className,
      'examDate': Timestamp.fromDate(examDate),
      'startHour': startTime.hour,
      'startMinute': startTime.minute,
      'endHour': endTime.hour,
      'endMinute': endTime.minute,
      'durationMinutes': duration.inMinutes,
      'roomId': roomId,
      'roomName': roomName,
      'supervisorIds': supervisorIds,
      'supervisorNames': supervisorNames,
      'totalMarks': totalMarks,
      'instructions': instructions,
      'studentIds': studentIds,
      'status': status.toString(),
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
    };
  }

  /// التحقق من صحة بيانات الجلسة
  bool get isValid {
    return examId.isNotEmpty &&
           subjectId.isNotEmpty &&
           classId.isNotEmpty &&
           roomId.isNotEmpty &&
           supervisorIds.isNotEmpty &&
           studentIds.isNotEmpty &&
           totalMarks > 0 &&
           duration.inMinutes > 0;
  }

  /// التحقق من كون الجلسة جارية حالياً
  bool get isOngoing {
    final now = DateTime.now();
    final sessionStart = DateTime(
      examDate.year,
      examDate.month,
      examDate.day,
      startTime.hour,
      startTime.minute,
    );
    final sessionEnd = DateTime(
      examDate.year,
      examDate.month,
      examDate.day,
      endTime.hour,
      endTime.minute,
    );
    
    return now.isAfter(sessionStart) && now.isBefore(sessionEnd);
  }

  /// التحقق من كون الجلسة قادمة
  bool get isUpcoming {
    final now = DateTime.now();
    final sessionStart = DateTime(
      examDate.year,
      examDate.month,
      examDate.day,
      startTime.hour,
      startTime.minute,
    );
    
    return now.isBefore(sessionStart);
  }

  /// التحقق من كون الجلسة منتهية
  bool get isCompleted {
    final now = DateTime.now();
    final sessionEnd = DateTime(
      examDate.year,
      examDate.month,
      examDate.day,
      endTime.hour,
      endTime.minute,
    );
    
    return now.isAfter(sessionEnd) || status == ExamSessionStatus.completed;
  }

  /// حساب الدقائق المتبقية للجلسة
  int get minutesRemaining {
    if (isCompleted) return 0;
    
    final now = DateTime.now();
    final sessionStart = DateTime(
      examDate.year,
      examDate.month,
      examDate.day,
      startTime.hour,
      startTime.minute,
    );
    
    if (isUpcoming) {
      return sessionStart.difference(now).inMinutes;
    } else if (isOngoing) {
      final sessionEnd = DateTime(
        examDate.year,
        examDate.month,
        examDate.day,
        endTime.hour,
        endTime.minute,
      );
      return sessionEnd.difference(now).inMinutes;
    }
    
    return 0;
  }

  @override
  String toString() {
    return 'ExamSession(id: $id, subject: $subjectName, class: $className, date: $examDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExamSession && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// تعداد حالات جلسة الامتحان
enum ExamSessionStatus {
  scheduled,  // مجدولة
  ongoing,    // جارية
  completed,  // مكتملة
  cancelled   // ملغية
}

/// امتدادات مفيدة لحالات جلسة الامتحان
extension ExamSessionStatusExtension on ExamSessionStatus {
  /// الاسم العربي لحالة الجلسة
  String get arabicName {
    switch (this) {
      case ExamSessionStatus.scheduled:
        return 'مجدولة';
      case ExamSessionStatus.ongoing:
        return 'جارية';
      case ExamSessionStatus.completed:
        return 'مكتملة';
      case ExamSessionStatus.cancelled:
        return 'ملغية';
    }
  }
}

/// فئة مساعدة لوقت اليوم (بديل عن TimeOfDay من Flutter)
class TimeOfDay {
  final int hour;
  final int minute;

  const TimeOfDay({required this.hour, required this.minute});

  /// تحويل إلى نص بصيغة 24 ساعة
  String get format24Hour {
    return '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  }

  /// تحويل إلى نص بصيغة 12 ساعة
  String get format12Hour {
    final period = hour >= 12 ? 'م' : 'ص';
    final displayHour = hour == 0 ? 12 : (hour > 12 ? hour - 12 : hour);
    return '${displayHour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')} $period';
  }

  @override
  String toString() => format24Hour;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimeOfDay && other.hour == hour && other.minute == minute;
  }

  @override
  int get hashCode => hour.hashCode ^ minute.hashCode;
}
