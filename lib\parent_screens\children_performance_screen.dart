import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/models/student_grade_model.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة متابعة أداء الأبناء للأولياء
///
/// هذه الشاشة تعتبر من أهم الشاشات لأولياء الأمور حيث تمكنهم من:
/// - متابعة الأداء الأكاديمي التفصيلي لكل ابن/ابنة
/// - مقارنة الأداء عبر الفترات الزمنية المختلفة
/// - عرض الدرجات والتقديرات في جميع المواد
/// - تتبع التحسن أو التراجع في الأداء
/// - عرض تقارير مفصلة لكل مادة دراسية
/// - مقارنة أداء الطالب مع متوسط الصف
/// - عرض ملاحظات المعلمين وتوصياتهم
/// - إحصائيات شاملة عن الحضور والغياب
/// - تحليل نقاط القوة والضعف لكل طالب
///
/// تدفق العمل في الشاشة:
/// 1. عرض قائمة بجميع الأبناء المسجلين
/// 2. اختيار الطالب المراد متابعة أدائه
/// 3. عرض ملخص شامل لأداء الطالب
/// 4. تفصيل الدرجات حسب المواد والفترات
/// 5. عرض المقارنات والتحليلات
/// 6. إمكانية تصدير التقارير ومشاركتها
///
/// الألوان والتصميم:
/// - اللون الأساسي: أزرق داكن (يرمز للثقة والاستقرار)
/// - ألوان فرعية: أزرق فاتح، أخضر للنجاح، أحمر للتحذير
/// - تصميم احترافي يناسب الأولياء
/// - مخططات بيانية واضحة ومفهومة
/// - بطاقات منظمة لعرض المعلومات
class ChildrenPerformanceScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر الذي يستخدم النظام
  /// يستخدم لتحميل بيانات الأبناء المرتبطين به
  final String parentId;

  const ChildrenPerformanceScreen({super.key, required this.parentId});

  @override
  ConsumerState<ChildrenPerformanceScreen> createState() =>
      _ChildrenPerformanceScreenState();
}

class _ChildrenPerformanceScreenState
    extends ConsumerState<ChildrenPerformanceScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: الملخص، المواد، المقارنات، التقارير
  late TabController _tabController;

  /// متحكم الرسوم المتحركة للمخططات البيانية
  /// يستخدم لإظهار المخططات بشكل تدريجي وجذاب
  late AnimationController _chartAnimationController;

  /// الرسم المتحرك لظهور المخططات
  late Animation<double> _chartAnimation;

  /// متحكم البحث في الطلاب
  final TextEditingController _searchController = TextEditingController();

  /// متحكم اختيار الفترة الزمنية
  final TextEditingController _periodController = TextEditingController();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة الأبناء المرتبطين بولي الأمر
  List<StudentModel> _children = [];

  /// الطالب المحدد حالياً للمتابعة
  /// null يعني عرض ملخص لجميع الأبناء
  StudentModel? _selectedStudent;

  /// قائمة درجات الطالب المحدد
  List<StudentGradeModel> _studentGrades = [];

  /// خريطة الدرجات مجمعة حسب المادة
  /// المفتاح: اسم المادة، القيمة: قائمة الدرجات
  Map<String, List<StudentGradeModel>> _gradesBySubject = {};

  /// خريطة الدرجات مجمعة حسب الفترة الزمنية
  /// المفتاح: الفترة (شهر/فصل)، القيمة: قائمة الدرجات
  Map<String, List<StudentGradeModel>> _gradesByPeriod = {};

  /// بيانات الحضور والغياب للطالب المحدد
  Map<String, dynamic> _attendanceData = {};

  /// ملاحظات المعلمين وتوصياتهم
  List<Map<String, dynamic>> _teacherComments = [];

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  bool _isLoading = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  /// حالة تصدير التقرير
  bool _isExporting = false;

  /// نص البحث الحالي
  String _searchQuery = '';

  /// الفترة الزمنية المحددة للعرض
  PerformancePeriod _selectedPeriod = PerformancePeriod.currentSemester;

  /// نوع المقارنة المحددة
  ComparisonType _comparisonType = ComparisonType.classAverage;

  /// فلتر المواد (الكل أو مادة محددة)
  String? _subjectFilter;

  // ===================================================================
  // إحصائيات الأداء المحسوبة
  // ===================================================================

  /// المعدل العام للطالب المحدد
  double _overallAverage = 0.0;

  /// أعلى درجة حصل عليها الطالب
  double _highestGrade = 0.0;

  /// أقل درجة حصل عليها الطالب
  double _lowestGrade = 0.0;

  /// عدد المواد التي يتفوق فيها الطالب
  int _excellentSubjects = 0;

  /// عدد المواد التي تحتاج تحسين
  int _needsImprovementSubjects = 0;

  /// نسبة الحضور للطالب
  double _attendanceRate = 0.0;

  /// عدد أيام الغياب
  int _absenceDays = 0;

  /// اتجاه الأداء (تحسن، ثبات، تراجع)
  PerformanceTrend _performanceTrend = PerformanceTrend.stable;

  /// نسبة التحسن أو التراجع
  double _trendPercentage = 0.0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة للمخططات
    _chartAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // إنشاء الرسم المتحرك للمخططات
    _chartAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _chartAnimationController,
        curve: Curves.easeInOutCubic,
      ),
    );

    // تحميل البيانات الأولية
    _loadChildrenData();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _chartAnimationController.dispose();
    _searchController.dispose();
    _periodController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: Text(
          _selectedStudent != null
              ? 'أداء ${_selectedStudent!.name}'
              : 'متابعة أداء الأبناء',
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800], // لون أزرق داكن للأولياء
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.dashboard, size: 18), text: 'الملخص'),
            Tab(icon: Icon(Icons.subject, size: 18), text: 'المواد'),
            Tab(icon: Icon(Icons.compare_arrows, size: 18), text: 'المقارنات'),
            Tab(icon: Icon(Icons.assessment, size: 18), text: 'التقارير'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر اختيار الطالب
          if (_children.length > 1)
            PopupMenuButton<StudentModel>(
              icon: const Icon(Icons.person, color: Colors.white),
              tooltip: 'اختيار الطالب',
              onSelected: (student) => _selectStudent(student),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem<StudentModel>(
                      value: null,
                      child: Text('جميع الأبناء'),
                    ),
                    ..._children.map(
                      (student) => PopupMenuItem<StudentModel>(
                        value: student,
                        child: Text(student.name),
                      ),
                    ),
                  ],
            ),

          // زر البحث والفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'الفلاتر والبحث',
          ),

          // زر تصدير التقرير
          if (_isExporting)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.file_download, color: Colors.white),
              onPressed: () => _exportReport(),
              tooltip: 'تصدير التقرير',
            ),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: Column(
        children: [
          // قسم اختيار الطالب (إذا لم يتم اختيار طالب محدد)
          if (_selectedStudent == null) _buildStudentSelector(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب الملخص العام
                _buildSummaryTab(),

                // تبويب المواد التفصيلية
                _buildSubjectsTab(),

                // تبويب المقارنات والتحليلات
                _buildComparisonsTab(),

                // تبويب التقارير والإحصائيات
                _buildReportsTab(),
              ],
            ),
          ),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم للإجراءات السريعة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickActionsDialog(),
        backgroundColor: Colors.blue[600],
        icon: const Icon(Icons.analytics, color: Colors.white),
        label: const Text(
          'تحليل سريع',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل بيانات الأبناء وأدائهم من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة:
  /// - قائمة الأبناء المرتبطين بولي الأمر
  /// - درجات كل طالب في جميع المواد
  /// - بيانات الحضور والغياب
  /// - ملاحظات المعلمين وتوصياتهم
  /// - حساب الإحصائيات والتحليلات
  Future<void> _loadChildrenData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل البيانات الفعلية من قاعدة البيانات
      // هنا سيتم استدعاء الخدمات لجلب:
      // 1. قائمة الأبناء من جدول الطلاب
      // 2. درجات كل طالب من جدول الدرجات
      // 3. بيانات الحضور والغياب
      // 4. ملاحظات المعلمين

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 2));

      // محاكاة بيانات الأبناء
      _simulateChildrenData();

      // إذا كان هناك طالب واحد فقط، اختره تلقائياً
      if (_children.length == 1) {
        _selectStudent(_children.first);
      }
    } catch (e) {
      // معالجة الأخطاء وعرض رسالة مناسبة
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadChildrenData(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// محاكاة بيانات الأبناء للاختبار
  ///
  /// هذه الدالة تنشئ بيانات وهمية لاختبار الواجهة
  /// في التطبيق الفعلي ستحل محلها البيانات من قاعدة البيانات
  void _simulateChildrenData() {
    final now = DateTime.now();

    // محاكاة قائمة الأبناء
    _children = [
      StudentModel(
        id: 'student1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        studentNumber: 'STU001',
        studentClass: 'الصف السادس أ',
        classId: 'class1',
        createdAt: Timestamp.fromDate(now.subtract(const Duration(days: 365))),
        dateOfBirth: DateTime(2010, 5, 15),
        address: 'صنعاء، اليمن',
        phoneNumber: '777123456',
        isActive: true,
        profileImageUrl: null,
        gender: 'ذكر',
      ),
      StudentModel(
        id: 'student2',
        name: 'فاطمة أحمد محمد',
        email: '<EMAIL>',
        studentNumber: 'STU002',
        studentClass: 'الصف الرابع ب',
        classId: 'class2',
        createdAt: Timestamp.fromDate(now.subtract(const Duration(days: 200))),
        dateOfBirth: DateTime(2012, 8, 22),
        address: 'صنعاء، اليمن',
        phoneNumber: '777123457',
        isActive: true,
        profileImageUrl: null,
        gender: 'أنثى',
      ),
    ];
  }

  /// اختيار طالب محدد لمتابعة أدائه
  ///
  /// [student] الطالب المراد اختياره، null لعرض جميع الأبناء
  void _selectStudent(StudentModel? student) async {
    setState(() {
      _selectedStudent = student;
      _isLoading = true;
    });

    if (student != null) {
      // تحميل بيانات الطالب المحدد
      await _loadStudentPerformanceData(student.id);
    }

    setState(() {
      _isLoading = false;
    });
  }

  /// تحميل بيانات أداء طالب محدد
  ///
  /// [studentId] معرف الطالب المراد تحميل بياناته
  Future<void> _loadStudentPerformanceData(String studentId) async {
    try {
      // TODO: تحميل البيانات الفعلية من قاعدة البيانات
      // هنا سيتم استدعاء الخدمات لجلب:
      // 1. درجات الطالب في جميع المواد
      // 2. بيانات الحضور والغياب
      // 3. ملاحظات المعلمين

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 1));

      // محاكاة درجات الطالب
      _simulateStudentGrades(studentId);

      // محاكاة بيانات الحضور
      _simulateAttendanceData(studentId);

      // محاكاة ملاحظات المعلمين
      _simulateTeacherComments(studentId);

      // تجميع البيانات وحساب الإحصائيات
      _groupGradesBySubject();
      _groupGradesByPeriod();
      _calculatePerformanceStatistics();

      // بدء الرسوم المتحركة للمخططات
      _chartAnimationController.reset();
      _chartAnimationController.forward();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الطالب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// محاكاة درجات الطالب
  void _simulateStudentGrades(String studentId) {
    final now = DateTime.now();

    // محاكاة درجات متنوعة للطالب
    _studentGrades = [
      StudentGradeModel(
        id: 'grade1',
        studentId: studentId,
        examId: 'exam1',
        examName: 'امتحان الرياضيات النصفي',
        subjectId: 'math',
        subjectName: 'الرياضيات',
        teacherId: 'teacher1',
        teacherName: 'أ. أحمد محمد',
        grade: 87.5,
        maxGrade: 100.0,
        examDate: now.subtract(const Duration(days: 30)),
        examType: ExamType.midterm,
        examDuration: 90,
        isPublished: true,
        publishedAt: now.subtract(const Duration(days: 28)),
        teacherNotes: 'أداء جيد، يحتاج تحسين في الهندسة',
        performanceRating: 'جيد جداً',
        classAverage: 75.0,
        highestInClass: 95.0,
        lowestInClass: 45.0,
        rankInClass: 5,
        totalStudents: 30,
        semester: 'الفصل الأول',
        academicYear: '2024-2025',
        createdAt: now.subtract(const Duration(days: 30)),
        updatedAt: now.subtract(const Duration(days: 28)),
      ),
      // يمكن إضافة المزيد من الدرجات هنا
    ];
  }

  /// محاكاة بيانات الحضور والغياب
  void _simulateAttendanceData(String studentId) {
    _attendanceData = {
      'totalDays': 120,
      'presentDays': 115,
      'absentDays': 5,
      'attendanceRate': 95.8,
      'lateArrivals': 3,
      'earlyDepartures': 1,
    };
  }

  /// محاكاة ملاحظات المعلمين
  void _simulateTeacherComments(String studentId) {
    final now = DateTime.now();

    _teacherComments = [
      {
        'teacherName': 'أ. أحمد محمد',
        'subject': 'الرياضيات',
        'comment': 'طالب مجتهد ومتفوق، يحتاج مزيد من التركيز في الهندسة',
        'rating': 4,
        'date': now.subtract(const Duration(days: 7)),
      },
      {
        'teacherName': 'أ. فاطمة علي',
        'subject': 'العلوم',
        'comment': 'أداء ممتاز، استمر على هذا المستوى',
        'rating': 5,
        'date': now.subtract(const Duration(days: 14)),
      },
    ];
  }

  /// تجميع الدرجات حسب المادة
  ///
  /// ينظم الدرجات في مجموعات حسب المادة لسهولة العرض والتحليل
  void _groupGradesBySubject() {
    _gradesBySubject.clear();

    for (final grade in _studentGrades) {
      final subjectName = grade.subjectName;
      if (_gradesBySubject.containsKey(subjectName)) {
        _gradesBySubject[subjectName]!.add(grade);
      } else {
        _gradesBySubject[subjectName] = [grade];
      }
    }
  }

  /// تجميع الدرجات حسب الفترة الزمنية
  ///
  /// ينظم الدرجات حسب الفترة (شهر/فصل) لتتبع التطور
  void _groupGradesByPeriod() {
    _gradesByPeriod.clear();

    for (final grade in _studentGrades) {
      final period = '${grade.examDate.year}-${grade.examDate.month}';
      if (_gradesByPeriod.containsKey(period)) {
        _gradesByPeriod[period]!.add(grade);
      } else {
        _gradesByPeriod[period] = [grade];
      }
    }
  }

  /// حساب إحصائيات الأداء
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الأداء
  void _calculatePerformanceStatistics() {
    if (_studentGrades.isEmpty) return;

    // حساب المعدل العام
    final totalGrades = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a + b);
    _overallAverage = totalGrades / _studentGrades.length;

    // حساب أعلى وأقل درجة
    _highestGrade = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a > b ? a : b);
    _lowestGrade = _studentGrades
        .map((g) => g.grade)
        .reduce((a, b) => a < b ? a : b);

    // حساب المواد المتفوقة والتي تحتاج تحسين
    _excellentSubjects = 0;
    _needsImprovementSubjects = 0;

    _gradesBySubject.forEach((subject, grades) {
      final subjectAverage =
          grades.map((g) => g.grade).reduce((a, b) => a + b) / grades.length;
      if (subjectAverage >= 85.0) {
        _excellentSubjects++;
      } else if (subjectAverage < 70.0) {
        _needsImprovementSubjects++;
      }
    });

    // حساب بيانات الحضور
    _attendanceRate = _attendanceData['attendanceRate'] ?? 0.0;
    _absenceDays = _attendanceData['absentDays'] ?? 0;

    // تحديد اتجاه الأداء (محاكاة)
    _performanceTrend = PerformanceTrend.improving;
    _trendPercentage = 8.5;
  }

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الملخص
        break;
      case 1: // تبويب المواد
        break;
      case 2: // تبويب المقارنات
        _chartAnimationController.reset();
        _chartAnimationController.forward();
        break;
      case 3: // تبويب التقارير
        break;
    }
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الفلاتر والبحث'),
            content: const Text('سيتم تطبيق خيارات الفلترة والبحث قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// تصدير التقرير
  Future<void> _exportReport() async {
    setState(() {
      _isExporting = true;
    });

    try {
      // TODO: تطبيق تصدير التقرير
      await Future.delayed(const Duration(seconds: 2));

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تصدير التقرير بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تصدير التقرير: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isExporting = false;
      });
    }
  }

  /// عرض حوار الإجراءات السريعة
  void _showQuickActionsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تحليل سريع'),
            content: const Text('سيتم تطبيق التحليل السريع قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// بناء منتقي الطالب
  Widget _buildStudentSelector() {
    return const Center(
      child: Text(
        'منتقي الطالب\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء التبويبات
  Widget _buildSummaryTab() {
    return const Center(
      child: Text(
        'ملخص الأداء\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildSubjectsTab() {
    return const Center(
      child: Text(
        'المواد التفصيلية\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildComparisonsTab() {
    return const Center(
      child: Text(
        'المقارنات والتحليلات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  Widget _buildReportsTab() {
    return const Center(
      child: Text(
        'التقارير والإحصائيات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    if (_selectedStudent == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        border: Border(top: BorderSide(color: Colors.blue[200]!)),
      ),
      child: Row(
        children: [
          Icon(Icons.trending_up, size: 16, color: Colors.blue[600]),
          const SizedBox(width: 4),
          Text(
            'المعدل: ${_overallAverage.toStringAsFixed(1)}',
            style: TextStyle(fontSize: 12, color: Colors.blue[600]),
          ),
          const Spacer(),
          Icon(Icons.event_available, size: 16, color: Colors.blue[600]),
          const SizedBox(width: 4),
          Text(
            'الحضور: ${_attendanceRate.toStringAsFixed(1)}%',
            style: TextStyle(fontSize: 12, color: Colors.blue[600]),
          ),
        ],
      ),
    );
  }
}

/// تعداد فترات الأداء
enum PerformancePeriod {
  currentSemester, // الفصل الحالي
  lastSemester, // الفصل الماضي
  currentYear, // السنة الحالية
  lastYear, // السنة الماضية
  all, // جميع الفترات
}

/// تعداد أنواع المقارنة
enum ComparisonType {
  classAverage, // مع متوسط الصف
  schoolAverage, // مع متوسط المدرسة
  previousPeriod, // مع الفترة السابقة
  siblings, // مع الأشقاء
}

/// تعداد اتجاه الأداء
enum PerformanceTrend {
  improving, // تحسن
  stable, // ثبات
  declining, // تراجع
}
