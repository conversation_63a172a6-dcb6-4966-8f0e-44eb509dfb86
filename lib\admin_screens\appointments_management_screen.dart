import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/appointment_model.dart';
import 'package:school_management_system/providers/appointment_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';

/// شاشة إدارة المواعيد والاجتماعات المتقدمة
///
/// تقدم هذه الشاشة واجهة شاملة لإدارة جميع المواعيد في النظام المدرسي
/// مع إمكانيات متقدمة للبحث والفلترة والتحليل والإدارة
///
/// الميزات الرئيسية:
/// - عرض جميع المواعيد في النظام
/// - فلترة متقدمة حسب التاريخ والحالة والنوع
/// - إنشاء مواعيد جديدة
/// - تعديل وإلغاء المواعيد الموجودة
/// - عرض إحصائيات شاملة
/// - إدارة الموافقات والصلاحيات
/// - تصدير التقارير
/// - إرسال التذكيرات
///
/// التصميم:
/// - واجهة حديثة ومتجاوبة
/// - ألوان متدرجة حسب أولوية المواعيد
/// - أيقونات واضحة ومعبرة
/// - تنظيم هرمي للمعلومات
/// - دعم الوضع المظلم والفاتح
class AppointmentsManagementScreen extends ConsumerStatefulWidget {
  /// معرف المدير الحالي
  final String adminId;

  /// اسم المدير الحالي
  final String adminName;

  /// دور المدير الحالي
  final String adminRole;

  const AppointmentsManagementScreen({
    super.key,
    required this.adminId,
    required this.adminName,
    required this.adminRole,
  });

  @override
  ConsumerState<AppointmentsManagementScreen> createState() =>
      _AppointmentsManagementScreenState();
}

class _AppointmentsManagementScreenState
    extends ConsumerState<AppointmentsManagementScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  late TabController _tabController;

  /// متحكم البحث
  final TextEditingController _searchController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة البيانات
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // ===================================================================
  // متغيرات الحالة
  // ===================================================================

  /// الفهرس المحدد حالياً في التبويبات
  int _selectedTabIndex = 0;

  /// الموعد المحدد حالياً للعرض أو التعديل
  AppointmentModel? _selectedAppointment;

  /// هل نافذة التفاصيل مفتوحة؟
  bool _isDetailsOpen = false;

  @override
  void initState() {
    super.initState();

    // تهيئة متحكم التبويبات مع 4 تبويبات رئيسية
    _tabController = TabController(length: 4, vsync: this);

    // إضافة مستمع لتغيير التبويبات
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        setState(() {
          _selectedTabIndex = _tabController.index;
          _selectedAppointment = null;
          _isDetailsOpen = false;
        });
      }
    });
  }

  @override
  void dispose() {
    // تنظيف الموارد
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // ===================================================================
      // شريط التطبيق العلوي
      // ===================================================================
      appBar: AppBar(
        title: const Text(
          'إدارة المواعيد والاجتماعات',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Colors.indigo[700],
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر إنشاء موعد جديد
          IconButton(
            icon: const Icon(Icons.add_circle_outline),
            tooltip: 'إنشاء موعد جديد',
            onPressed: _showCreateAppointmentDialog,
          ),
          // زر تحديث البيانات
          IconButton(
            icon: const Icon(Icons.refresh),
            tooltip: 'تحديث البيانات',
            onPressed: _refreshData,
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            tooltip: 'إعدادات المواعيد',
            onPressed: _showAppointmentSettings,
          ),
          const SizedBox(width: 8),
        ],
        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          indicatorWeight: 3,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.calendar_today), text: 'جميع المواعيد'),
            Tab(icon: Icon(Icons.pending_actions), text: 'المعلقة'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.settings_applications), text: 'الإعدادات'),
          ],
        ),
      ),

      // ===================================================================
      // المحتوى الرئيسي
      // ===================================================================
      body: Row(
        children: [
          // القائمة الجانبية للفلاتر (في الشاشات الكبيرة)
          if (MediaQuery.of(context).size.width > 1000)
            Container(
              width: 300,
              decoration: BoxDecoration(
                color: Colors.grey[50],
                border: Border(right: BorderSide(color: Colors.grey[300]!)),
              ),
              child: _buildFiltersPanel(),
            ),

          // المحتوى الرئيسي
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildAllAppointmentsTab(),
                _buildPendingAppointmentsTab(),
                _buildStatisticsTab(),
                _buildSettingsTab(),
              ],
            ),
          ),

          // لوحة التفاصيل الجانبية (عند تحديد موعد)
          if (_isDetailsOpen && _selectedAppointment != null)
            Container(
              width: 400,
              decoration: BoxDecoration(
                color: Colors.white,
                border: Border(left: BorderSide(color: Colors.grey[300]!)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(-2, 0),
                  ),
                ],
              ),
              child: _buildAppointmentDetailsPanel(),
            ),
        ],
      ),

      // ===================================================================
      // الزر العائم لإنشاء موعد جديد (في الشاشات الصغيرة)
      // ===================================================================
      floatingActionButton:
          MediaQuery.of(context).size.width <= 1000
              ? FloatingActionButton.extended(
                onPressed: _showCreateAppointmentDialog,
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
                icon: const Icon(Icons.add),
                label: const Text('موعد جديد'),
              )
              : null,
    );
  }

  // ===================================================================
  // بناء لوحة الفلاتر
  // ===================================================================

  /// بناء لوحة الفلاتر الجانبية
  Widget _buildFiltersPanel() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Text(
            'فلاتر البحث',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.indigo[700],
            ),
          ),
          const SizedBox(height: 16),

          // شريط البحث
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'البحث في المواعيد...',
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
            ),
            onChanged: _onSearchChanged,
          ),
          const SizedBox(height: 16),

          // فلتر التاريخ
          _buildDateFilter(),
          const SizedBox(height: 16),

          // فلتر الحالة
          _buildStatusFilter(),
          const SizedBox(height: 16),

          // فلتر النوع
          _buildTypeFilter(),
          const SizedBox(height: 16),

          // فلتر الأولوية
          _buildPriorityFilter(),
          const SizedBox(height: 24),

          // أزرار الإجراءات
          Row(
            children: [
              Expanded(
                child: ElevatedButton(
                  onPressed: _applyFilters,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.indigo[600],
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('تطبيق'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetFilters,
                  child: const Text('إعادة تعيين'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // ===================================================================
  // الدوال المساعدة للنصوص
  // ===================================================================

  /// الحصول على نص الحالة
  String _getStatusText(AppointmentStatus status) {
    switch (status) {
      case AppointmentStatus.pending:
        return 'معلق';
      case AppointmentStatus.confirmed:
        return 'مؤكد';
      case AppointmentStatus.cancelled:
        return 'ملغي';
      case AppointmentStatus.completed:
        return 'مكتمل';
      case AppointmentStatus.postponed:
        return 'مؤجل';
      case AppointmentStatus.inProgress:
        return 'جاري الآن';
    }
  }

  /// الحصول على نص النوع
  String _getTypeText(AppointmentType type) {
    switch (type) {
      case AppointmentType.parentMeeting:
        return 'اجتماع أولياء أمور';
      case AppointmentType.personalInterview:
        return 'مقابلة شخصية';
      case AppointmentType.consultation:
        return 'استشارة';
      case AppointmentType.disciplinaryMeeting:
        return 'اجتماع تأديبي';
      case AppointmentType.academicReview:
        return 'مراجعة أكاديمية';
      case AppointmentType.counselingSession:
        return 'جلسة إرشاد';
      case AppointmentType.medicalCheckup:
        return 'فحص طبي';
      case AppointmentType.specialEvent:
        return 'فعالية خاصة';
      case AppointmentType.groupMeeting:
        return 'اجتماع جماعي';
      case AppointmentType.onlineSession:
        return 'جلسة إلكترونية';
    }
  }

  /// الحصول على نص الأولوية
  String _getPriorityText(AppointmentPriority priority) {
    switch (priority) {
      case AppointmentPriority.low:
        return 'منخفضة';
      case AppointmentPriority.normal:
        return 'عادية';
      case AppointmentPriority.high:
        return 'مهمة';
      case AppointmentPriority.urgent:
        return 'عاجلة';
      case AppointmentPriority.critical:
        return 'حرجة';
    }
  }

  // ===================================================================
  // دوال معالجة الأحداث
  // ===================================================================

  /// معالجة تغيير نص البحث
  void _onSearchChanged(String query) {
    // تطبيق البحث مع تأخير لتجنب الاستعلامات المتكررة
    Future.delayed(const Duration(milliseconds: 500), () {
      if (_searchController.text == query) {
        ref.read(appointmentFiltersProvider.notifier).updateSearchQuery(query);
      }
    });
  }

  /// اختيار تاريخ البداية
  void _selectStartDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      ref.read(appointmentFiltersProvider.notifier).updateStartDate(date);
    }
  }

  /// اختيار تاريخ النهاية
  void _selectEndDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      ref.read(appointmentFiltersProvider.notifier).updateEndDate(date);
    }
  }

  /// تطبيق الفلاتر
  void _applyFilters() {
    // الفلاتر تطبق تلقائياً عند التغيير
    // هذه الدالة للتأكيد أو إجراءات إضافية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تطبيق الفلاتر'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// إعادة تعيين الفلاتر
  void _resetFilters() {
    ref.read(appointmentFiltersProvider.notifier).resetFilters();
    _searchController.clear();

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إعادة تعيين الفلاتر'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// عرض نافذة إنشاء موعد جديد
  void _showCreateAppointmentDialog() {
    // TODO: تطبيق نافذة إنشاء موعد جديد
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إنشاء موعد جديد'),
            content: const Text('سيتم تطبيق نافذة إنشاء الموعد قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// تحديث البيانات
  void _refreshData() {
    // إعادة تحميل جميع البيانات
    ref.invalidate(userAppointmentsProvider);
    ref.invalidate(pendingApprovalsProvider);
    ref.invalidate(appointmentStatsProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث البيانات'),
        backgroundColor: Colors.green,
      ),
    );
  }

  /// عرض إعدادات المواعيد
  void _showAppointmentSettings() {
    // TODO: تطبيق نافذة إعدادات المواعيد
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعدادات المواعيد'),
            content: const Text('سيتم تطبيق إعدادات المواعيد قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء المحتوى (ستكمل في الجزء التالي)
  // ===================================================================

  /// بناء قائمة المواعيد
  Widget _buildAppointmentsList(List<AppointmentModel> appointments) {
    // TODO: تطبيق قائمة المواعيد
    return const Center(child: Text('قائمة المواعيد - سيتم التطبيق قريباً'));
  }

  /// بناء قائمة المواعيد المعلقة
  Widget _buildPendingAppointmentsList(List<AppointmentModel> appointments) {
    // TODO: تطبيق قائمة المواعيد المعلقة
    return const Center(child: Text('المواعيد المعلقة - سيتم التطبيق قريباً'));
  }

  /// بناء عرض الإحصائيات
  Widget _buildStatisticsView(AppointmentStats stats) {
    // TODO: تطبيق عرض الإحصائيات
    return const Center(child: Text('الإحصائيات - سيتم التطبيق قريباً'));
  }

  /// بناء لوحة تفاصيل الموعد
  Widget _buildAppointmentDetailsPanel() {
    // TODO: تطبيق لوحة التفاصيل
    return const Center(child: Text('تفاصيل الموعد - سيتم التطبيق قريباً'));
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection({
    required String title,
    required List<Widget> children,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),
        ...children,
      ],
    );
  }

  /// بناء عنصر إعدادات
  Widget _buildSettingsTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: Colors.indigo[600]),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
    );
  }

  // ===================================================================
  // بناء فلاتر محددة
  // ===================================================================

  /// بناء فلتر التاريخ
  Widget _buildDateFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'التاريخ',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectStartDate,
                icon: const Icon(Icons.date_range, size: 16),
                label: const Text('من'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _selectEndDate,
                icon: const Icon(Icons.date_range, size: 16),
                label: const Text('إلى'),
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء فلتر الحالة
  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الحالة',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Consumer(
          builder: (context, ref, child) {
            final filters = ref.watch(appointmentFiltersProvider);
            return DropdownButtonFormField<AppointmentStatus?>(
              value: filters.status,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              hint: const Text('اختر الحالة'),
              items: [
                const DropdownMenuItem<AppointmentStatus?>(
                  value: null,
                  child: Text('جميع الحالات'),
                ),
                ...AppointmentStatus.values.map((status) {
                  return DropdownMenuItem<AppointmentStatus?>(
                    value: status,
                    child: Text(_getStatusText(status)),
                  );
                }),
              ],
              onChanged: (value) {
                ref
                    .read(appointmentFiltersProvider.notifier)
                    .updateStatus(value);
              },
            );
          },
        ),
      ],
    );
  }

  /// بناء فلتر النوع
  Widget _buildTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النوع',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Consumer(
          builder: (context, ref, child) {
            final filters = ref.watch(appointmentFiltersProvider);
            return DropdownButtonFormField<AppointmentType?>(
              value: filters.type,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              hint: const Text('اختر النوع'),
              items: [
                const DropdownMenuItem<AppointmentType?>(
                  value: null,
                  child: Text('جميع الأنواع'),
                ),
                ...AppointmentType.values.map((type) {
                  return DropdownMenuItem<AppointmentType?>(
                    value: type,
                    child: Text(_getTypeText(type)),
                  );
                }),
              ],
              onChanged: (value) {
                ref.read(appointmentFiltersProvider.notifier).updateType(value);
              },
            );
          },
        ),
      ],
    );
  }

  /// بناء فلتر الأولوية
  Widget _buildPriorityFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الأولوية',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            color: Colors.grey[700],
          ),
        ),
        const SizedBox(height: 8),
        Consumer(
          builder: (context, ref, child) {
            final filters = ref.watch(appointmentFiltersProvider);
            return DropdownButtonFormField<AppointmentPriority?>(
              value: filters.priority,
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 8,
                ),
              ),
              hint: const Text('اختر الأولوية'),
              items: [
                const DropdownMenuItem<AppointmentPriority?>(
                  value: null,
                  child: Text('جميع الأولويات'),
                ),
                ...AppointmentPriority.values.map((priority) {
                  return DropdownMenuItem<AppointmentPriority?>(
                    value: priority,
                    child: Text(_getPriorityText(priority)),
                  );
                }),
              ],
              onChanged: (value) {
                ref
                    .read(appointmentFiltersProvider.notifier)
                    .updatePriority(value);
              },
            );
          },
        ),
      ],
    );
  }

  // ===================================================================
  // بناء التبويبات الرئيسية
  // ===================================================================

  /// بناء تبويب جميع المواعيد
  Widget _buildAllAppointmentsTab() {
    return Consumer(
      builder: (context, ref, child) {
        // جلب المواعيد باستخدام الفلاتر الحالية
        final filters = ref.watch(appointmentFiltersProvider);
        final appointmentsParams = UserAppointmentsParams(
          userId: widget.adminId,
          startDate: filters.startDate,
          endDate: filters.endDate,
          status: filters.status,
          type: filters.type,
          limit: 100,
        );

        final appointmentsAsync = ref.watch(
          userAppointmentsProvider(appointmentsParams),
        );

        return appointmentsAsync.when(
          data: (appointments) => _buildAppointmentsList(appointments),
          loading: () => const Center(child: LoadingIndicator()),
          error:
              (error, stack) => Center(
                child: ErrorMessage(message: 'فشل في جلب المواعيد: $error'),
              ),
        );
      },
    );
  }

  /// بناء تبويب المواعيد المعلقة
  Widget _buildPendingAppointmentsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final pendingAppointmentsAsync = ref.watch(
          pendingApprovalsProvider(widget.adminId),
        );

        return pendingAppointmentsAsync.when(
          data: (appointments) => _buildPendingAppointmentsList(appointments),
          loading: () => const Center(child: LoadingIndicator()),
          error:
              (error, stack) => Center(
                child: ErrorMessage(
                  message: 'فشل في جلب المواعيد المعلقة: $error',
                ),
              ),
        );
      },
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final statsAsync = ref.watch(appointmentStatsProvider(widget.adminId));

        return statsAsync.when(
          data: (stats) => _buildStatisticsView(stats),
          loading: () => const Center(child: LoadingIndicator()),
          error:
              (error, stack) => Center(
                child: ErrorMessage(message: 'فشل في جلب الإحصائيات: $error'),
              ),
        );
      },
    );
  }

  /// بناء تبويب الإعدادات
  Widget _buildSettingsTab() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إعدادات نظام المواعيد',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.indigo[700],
            ),
          ),
          const SizedBox(height: 24),

          // إعدادات عامة
          _buildSettingsSection(
            title: 'الإعدادات العامة',
            children: [
              _buildSettingsTile(
                title: 'مدة الموعد الافتراضية',
                subtitle: '30 دقيقة',
                icon: Icons.timer,
                onTap: _showDefaultDurationSettings,
              ),
              _buildSettingsTile(
                title: 'أوقات العمل',
                subtitle: '8:00 ص - 4:00 م',
                icon: Icons.schedule,
                onTap: _showWorkingHoursSettings,
              ),
              _buildSettingsTile(
                title: 'التذكيرات التلقائية',
                subtitle: 'مفعلة',
                icon: Icons.notifications,
                onTap: _showReminderSettings,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // إعدادات الموافقات
          _buildSettingsSection(
            title: 'إعدادات الموافقات',
            children: [
              _buildSettingsTile(
                title: 'الموافقة المطلوبة للمواعيد',
                subtitle: 'المواعيد المهمة فقط',
                icon: Icons.approval,
                onTap: _showApprovalSettings,
              ),
              _buildSettingsTile(
                title: 'مهلة الموافقة',
                subtitle: '24 ساعة',
                icon: Icons.hourglass_empty,
                onTap: _showApprovalTimeoutSettings,
              ),
            ],
          ),
        ],
      ),
    );
  }

  // دوال الإعدادات (TODO)
  void _showDefaultDurationSettings() {}
  void _showWorkingHoursSettings() {}
  void _showReminderSettings() {}
  void _showApprovalSettings() {}
  void _showApprovalTimeoutSettings() {}
}
