import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج تقييم السلوك المتقدم
///
/// يمثل تقييم سلوك طالب في النظام المدرسي مع جميع التفاصيل والمعايير
/// يدعم تقييمات متعددة الأبعاد ومتابعة التطور السلوكي للطالب
///
/// الميزات المتقدمة:
/// - تقييم متعدد المعايير (الانضباط، التعاون، الاحترام، إلخ)
/// - تتبع التطور السلوكي عبر الزمن
/// - ربط التقييمات بالأحداث والمواقف المحددة
/// - دعم التقييمات الإيجابية والسلبية
/// - نظام نقاط السلوك والمكافآت
/// - تقارير شاملة للأولياء والإدارة
/// - خطط تحسين السلوك المخصصة
/// - تتبع فعالية التدخلات السلوكية
class BehaviorEvaluationModel {
  /// معرف التقييم الفريد
  final String id;

  /// معرف الطالب المُقيَّم
  final String studentId;

  /// اسم الطالب المُقيَّم
  final String studentName;

  /// معرف الصف الدراسي
  final String classId;

  /// اسم الصف الدراسي
  final String className;

  /// معرف المُقيِّم (معلم، مرشد، إدارة)
  final String evaluatorId;

  /// اسم المُقيِّم
  final String evaluatorName;

  /// دور المُقيِّم (معلم المادة، معلم الصف، مرشد، مدير، إلخ)
  final String evaluatorRole;

  /// نوع التقييم (يومي، أسبوعي، شهري، حدث محدد)
  final EvaluationType evaluationType;

  /// فئة السلوك المُقيَّم (انضباط، تعاون، احترام، إلخ)
  final BehaviorCategory category;

  /// نوع السلوك (إيجابي، سلبي، محايد)
  final BehaviorType behaviorType;

  /// مستوى شدة السلوك (منخفض، متوسط، عالي، شديد)
  final SeverityLevel severityLevel;

  /// التقييم العام للسلوك (ممتاز، جيد جداً، جيد، مقبول، ضعيف)
  final OverallRating overallRating;

  /// النقاط المكتسبة أو المفقودة من هذا التقييم
  final int pointsAwarded;

  /// إجمالي نقاط السلوك للطالب حتى تاريخ هذا التقييم
  final int totalBehaviorPoints;

  /// عنوان التقييم أو الحدث
  final String title;

  /// وصف تفصيلي للسلوك المُلاحظ
  final String description;

  /// الموقف أو السياق الذي حدث فيه السلوك
  final String context;

  /// المادة الدراسية المرتبطة (إن وجدت)
  final String? subjectId;

  /// اسم المادة الدراسية
  final String? subjectName;

  /// الموقع الذي حدث فيه السلوك (الفصل، الملعب، المكتبة، إلخ)
  final String location;

  /// تاريخ ووقت حدوث السلوك
  final DateTime incidentDateTime;

  /// تاريخ إنشاء التقييم
  final DateTime createdAt;

  /// تاريخ آخر تحديث للتقييم
  final DateTime updatedAt;

  /// معرف من قام بآخر تحديث
  final String? updatedBy;

  /// التقييمات التفصيلية لمعايير السلوك المختلفة
  final Map<BehaviorCriteria, CriteriaRating> criteriaRatings;

  /// الإجراءات المتخذة نتيجة لهذا السلوك
  final List<BehaviorAction> actionsTaken;

  /// التوصيات لتحسين السلوك
  final List<String> recommendations;

  /// خطة التحسين المقترحة (إن وجدت)
  final BehaviorImprovementPlan? improvementPlan;

  /// هل تم إشعار ولي الأمر؟
  final bool parentNotified;

  /// تاريخ إشعار ولي الأمر
  final DateTime? parentNotificationDate;

  /// طريقة إشعار ولي الأمر (رسالة، مكالمة، اجتماع)
  final NotificationMethod? notificationMethod;

  /// رد فعل ولي الأمر على التقييم
  final String? parentResponse;

  /// تاريخ رد ولي الأمر
  final DateTime? parentResponseDate;

  /// المرفقات المرتبطة بالتقييم (صور، فيديوهات، مستندات)
  final List<BehaviorAttachment> attachments;

  /// الشهود على السلوك (طلاب آخرون، معلمون)
  final List<Witness> witnesses;

  /// السلوكيات المرتبطة أو المشابهة
  final List<String> relatedEvaluationIds;

  /// حالة التقييم (مسودة، نهائي، مراجعة، مؤرشف)
  final EvaluationStatus status;

  /// مستوى الخصوصية (عام، محدود، سري)
  final PrivacyLevel privacyLevel;

  /// العلامات والتصنيفات الإضافية
  final List<String> tags;

  /// ملاحظات إضافية من المُقيِّم
  final String? evaluatorNotes;

  /// ملاحظات من الإدارة
  final String? adminNotes;

  /// تقييم المتابعة (إن وجد)
  final String? followUpEvaluationId;

  /// هل يحتاج هذا التقييم لمتابعة؟
  final bool requiresFollowUp;

  /// تاريخ المتابعة المطلوبة
  final DateTime? followUpDate;

  /// معلومات إضافية (JSON)
  final Map<String, dynamic> metadata;

  const BehaviorEvaluationModel({
    required this.id,
    required this.studentId,
    required this.studentName,
    required this.classId,
    required this.className,
    required this.evaluatorId,
    required this.evaluatorName,
    required this.evaluatorRole,
    required this.evaluationType,
    required this.category,
    required this.behaviorType,
    required this.severityLevel,
    required this.overallRating,
    required this.pointsAwarded,
    required this.totalBehaviorPoints,
    required this.title,
    required this.description,
    required this.context,
    this.subjectId,
    this.subjectName,
    required this.location,
    required this.incidentDateTime,
    required this.createdAt,
    required this.updatedAt,
    this.updatedBy,
    required this.criteriaRatings,
    required this.actionsTaken,
    required this.recommendations,
    this.improvementPlan,
    this.parentNotified = false,
    this.parentNotificationDate,
    this.notificationMethod,
    this.parentResponse,
    this.parentResponseDate,
    this.attachments = const [],
    this.witnesses = const [],
    this.relatedEvaluationIds = const [],
    required this.status,
    required this.privacyLevel,
    this.tags = const [],
    this.evaluatorNotes,
    this.adminNotes,
    this.followUpEvaluationId,
    this.requiresFollowUp = false,
    this.followUpDate,
    this.metadata = const {},
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory BehaviorEvaluationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return BehaviorEvaluationModel(
      id: doc.id,
      studentId: data['studentId'] ?? '',
      studentName: data['studentName'] ?? '',
      classId: data['classId'] ?? '',
      className: data['className'] ?? '',
      evaluatorId: data['evaluatorId'] ?? '',
      evaluatorName: data['evaluatorName'] ?? '',
      evaluatorRole: data['evaluatorRole'] ?? '',
      evaluationType: EvaluationType.values.firstWhere(
        (e) => e.toString() == data['evaluationType'],
        orElse: () => EvaluationType.daily,
      ),
      category: BehaviorCategory.values.firstWhere(
        (e) => e.toString() == data['category'],
        orElse: () => BehaviorCategory.discipline,
      ),
      behaviorType: BehaviorType.values.firstWhere(
        (e) => e.toString() == data['behaviorType'],
        orElse: () => BehaviorType.neutral,
      ),
      severityLevel: SeverityLevel.values.firstWhere(
        (e) => e.toString() == data['severityLevel'],
        orElse: () => SeverityLevel.low,
      ),
      overallRating: OverallRating.values.firstWhere(
        (e) => e.toString() == data['overallRating'],
        orElse: () => OverallRating.acceptable,
      ),
      pointsAwarded: data['pointsAwarded'] ?? 0,
      totalBehaviorPoints: data['totalBehaviorPoints'] ?? 0,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      context: data['context'] ?? '',
      subjectId: data['subjectId'],
      subjectName: data['subjectName'],
      location: data['location'] ?? '',
      incidentDateTime:
          (data['incidentDateTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedBy: data['updatedBy'],
      criteriaRatings: (data['criteriaRatings'] as Map<String, dynamic>? ?? {})
          .map(
            (key, value) => MapEntry(
              BehaviorCriteria.values.firstWhere(
                (e) => e.toString() == key,
                orElse: () => BehaviorCriteria.respect,
              ),
              CriteriaRating.fromMap(value as Map<String, dynamic>),
            ),
          ),
      actionsTaken:
          (data['actionsTaken'] as List<dynamic>? ?? [])
              .map((e) => BehaviorAction.fromMap(e as Map<String, dynamic>))
              .toList(),
      recommendations: List<String>.from(data['recommendations'] ?? []),
      improvementPlan:
          data['improvementPlan'] != null
              ? BehaviorImprovementPlan.fromMap(
                data['improvementPlan'] as Map<String, dynamic>,
              )
              : null,
      parentNotified: data['parentNotified'] ?? false,
      parentNotificationDate:
          (data['parentNotificationDate'] as Timestamp?)?.toDate(),
      notificationMethod:
          data['notificationMethod'] != null
              ? NotificationMethod.values.firstWhere(
                (e) => e.toString() == data['notificationMethod'],
                orElse: () => NotificationMethod.message,
              )
              : null,
      parentResponse: data['parentResponse'],
      parentResponseDate: (data['parentResponseDate'] as Timestamp?)?.toDate(),
      attachments:
          (data['attachments'] as List<dynamic>? ?? [])
              .map((e) => BehaviorAttachment.fromMap(e as Map<String, dynamic>))
              .toList(),
      witnesses:
          (data['witnesses'] as List<dynamic>? ?? [])
              .map((e) => Witness.fromMap(e as Map<String, dynamic>))
              .toList(),
      relatedEvaluationIds: List<String>.from(
        data['relatedEvaluationIds'] ?? [],
      ),
      status: EvaluationStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => EvaluationStatus.draft,
      ),
      privacyLevel: PrivacyLevel.values.firstWhere(
        (e) => e.toString() == data['privacyLevel'],
        orElse: () => PrivacyLevel.general,
      ),
      tags: List<String>.from(data['tags'] ?? []),
      evaluatorNotes: data['evaluatorNotes'],
      adminNotes: data['adminNotes'],
      followUpEvaluationId: data['followUpEvaluationId'],
      requiresFollowUp: data['requiresFollowUp'] ?? false,
      followUpDate: (data['followUpDate'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// تحويل النموذج إلى خريطة للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'classId': classId,
      'className': className,
      'evaluatorId': evaluatorId,
      'evaluatorName': evaluatorName,
      'evaluatorRole': evaluatorRole,
      'evaluationType': evaluationType.toString(),
      'category': category.toString(),
      'behaviorType': behaviorType.toString(),
      'severityLevel': severityLevel.toString(),
      'overallRating': overallRating.toString(),
      'pointsAwarded': pointsAwarded,
      'totalBehaviorPoints': totalBehaviorPoints,
      'title': title,
      'description': description,
      'context': context,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'location': location,
      'incidentDateTime': Timestamp.fromDate(incidentDateTime),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'updatedBy': updatedBy,
      'criteriaRatings': criteriaRatings.map(
        (key, value) => MapEntry(key.toString(), value.toMap()),
      ),
      'actionsTaken': actionsTaken.map((e) => e.toMap()).toList(),
      'recommendations': recommendations,
      'improvementPlan': improvementPlan?.toMap(),
      'parentNotified': parentNotified,
      'parentNotificationDate':
          parentNotificationDate != null
              ? Timestamp.fromDate(parentNotificationDate!)
              : null,
      'notificationMethod': notificationMethod?.toString(),
      'parentResponse': parentResponse,
      'parentResponseDate':
          parentResponseDate != null
              ? Timestamp.fromDate(parentResponseDate!)
              : null,
      'attachments': attachments.map((e) => e.toMap()).toList(),
      'witnesses': witnesses.map((e) => e.toMap()).toList(),
      'relatedEvaluationIds': relatedEvaluationIds,
      'status': status.toString(),
      'privacyLevel': privacyLevel.toString(),
      'tags': tags,
      'evaluatorNotes': evaluatorNotes,
      'adminNotes': adminNotes,
      'followUpEvaluationId': followUpEvaluationId,
      'requiresFollowUp': requiresFollowUp,
      'followUpDate':
          followUpDate != null ? Timestamp.fromDate(followUpDate!) : null,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من التقييم
  BehaviorEvaluationModel copyWith({
    String? title,
    String? description,
    String? context,
    BehaviorType? behaviorType,
    SeverityLevel? severityLevel,
    OverallRating? overallRating,
    int? pointsAwarded,
    int? totalBehaviorPoints,
    String? location,
    DateTime? incidentDateTime,
    DateTime? updatedAt,
    String? updatedBy,
    Map<BehaviorCriteria, CriteriaRating>? criteriaRatings,
    List<BehaviorAction>? actionsTaken,
    List<String>? recommendations,
    BehaviorImprovementPlan? improvementPlan,
    bool? parentNotified,
    DateTime? parentNotificationDate,
    NotificationMethod? notificationMethod,
    String? parentResponse,
    DateTime? parentResponseDate,
    List<BehaviorAttachment>? attachments,
    List<Witness>? witnesses,
    List<String>? relatedEvaluationIds,
    EvaluationStatus? status,
    List<String>? tags,
    String? evaluatorNotes,
    String? adminNotes,
    String? followUpEvaluationId,
    bool? requiresFollowUp,
    DateTime? followUpDate,
    Map<String, dynamic>? metadata,
  }) {
    return BehaviorEvaluationModel(
      id: id,
      studentId: studentId,
      studentName: studentName,
      classId: classId,
      className: className,
      evaluatorId: evaluatorId,
      evaluatorName: evaluatorName,
      evaluatorRole: evaluatorRole,
      evaluationType: evaluationType,
      category: category,
      behaviorType: behaviorType ?? this.behaviorType,
      severityLevel: severityLevel ?? this.severityLevel,
      overallRating: overallRating ?? this.overallRating,
      pointsAwarded: pointsAwarded ?? this.pointsAwarded,
      totalBehaviorPoints: totalBehaviorPoints ?? this.totalBehaviorPoints,
      title: title ?? this.title,
      description: description ?? this.description,
      context: context ?? this.context,
      subjectId: subjectId,
      subjectName: subjectName,
      location: location ?? this.location,
      incidentDateTime: incidentDateTime ?? this.incidentDateTime,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      updatedBy: updatedBy ?? this.updatedBy,
      criteriaRatings: criteriaRatings ?? this.criteriaRatings,
      actionsTaken: actionsTaken ?? this.actionsTaken,
      recommendations: recommendations ?? this.recommendations,
      improvementPlan: improvementPlan ?? this.improvementPlan,
      parentNotified: parentNotified ?? this.parentNotified,
      parentNotificationDate:
          parentNotificationDate ?? this.parentNotificationDate,
      notificationMethod: notificationMethod ?? this.notificationMethod,
      parentResponse: parentResponse ?? this.parentResponse,
      parentResponseDate: parentResponseDate ?? this.parentResponseDate,
      attachments: attachments ?? this.attachments,
      witnesses: witnesses ?? this.witnesses,
      relatedEvaluationIds: relatedEvaluationIds ?? this.relatedEvaluationIds,
      status: status ?? this.status,
      privacyLevel: privacyLevel,
      tags: tags ?? this.tags,
      evaluatorNotes: evaluatorNotes ?? this.evaluatorNotes,
      adminNotes: adminNotes ?? this.adminNotes,
      followUpEvaluationId: followUpEvaluationId ?? this.followUpEvaluationId,
      requiresFollowUp: requiresFollowUp ?? this.requiresFollowUp,
      followUpDate: followUpDate ?? this.followUpDate,
      metadata: metadata ?? this.metadata,
    );
  }

  /// التحقق من صحة التقييم
  bool get isValid {
    return studentId.isNotEmpty &&
        evaluatorId.isNotEmpty &&
        title.isNotEmpty &&
        description.isNotEmpty &&
        context.isNotEmpty &&
        location.isNotEmpty &&
        criteriaRatings.isNotEmpty;
  }

  /// التحقق من إمكانية تعديل التقييم
  bool get canBeModified {
    return status == EvaluationStatus.draft ||
        status == EvaluationStatus.review;
  }

  /// التحقق من إمكانية حذف التقييم
  bool get canBeDeleted {
    return status == EvaluationStatus.draft;
  }

  /// التحقق من ضرورة إشعار ولي الأمر
  bool get shouldNotifyParent {
    return behaviorType == BehaviorType.negative &&
        severityLevel != SeverityLevel.low &&
        !parentNotified;
  }

  /// الحصول على متوسط تقييم المعايير
  double get averageCriteriaRating {
    if (criteriaRatings.isEmpty) return 0.0;

    final totalScore = criteriaRatings.values
        .map((rating) => rating.score)
        .reduce((a, b) => a + b);

    return totalScore / criteriaRatings.length;
  }

  /// الحصول على نص وصفي لنوع السلوك
  String get behaviorTypeDescription {
    switch (behaviorType) {
      case BehaviorType.positive:
        return 'سلوك إيجابي';
      case BehaviorType.negative:
        return 'سلوك سلبي';
      case BehaviorType.neutral:
        return 'سلوك محايد';
    }
  }

  /// الحصول على نص وصفي لمستوى الشدة
  String get severityLevelDescription {
    switch (severityLevel) {
      case SeverityLevel.low:
        return 'منخفض';
      case SeverityLevel.medium:
        return 'متوسط';
      case SeverityLevel.high:
        return 'عالي';
      case SeverityLevel.severe:
        return 'شديد';
    }
  }

  /// الحصول على نص وصفي للتقييم العام
  String get overallRatingDescription {
    switch (overallRating) {
      case OverallRating.excellent:
        return 'ممتاز';
      case OverallRating.veryGood:
        return 'جيد جداً';
      case OverallRating.good:
        return 'جيد';
      case OverallRating.acceptable:
        return 'مقبول';
      case OverallRating.poor:
        return 'ضعيف';
    }
  }

  /// الحصول على لون مناسب للتقييم
  String get ratingColor {
    switch (overallRating) {
      case OverallRating.excellent:
        return '#4CAF50'; // أخضر
      case OverallRating.veryGood:
        return '#8BC34A'; // أخضر فاتح
      case OverallRating.good:
        return '#FFC107'; // أصفر
      case OverallRating.acceptable:
        return '#FF9800'; // برتقالي
      case OverallRating.poor:
        return '#F44336'; // أحمر
    }
  }
}

// ===================================================================
// الفئات المساعدة والتعدادات
// ===================================================================

/// تقييم معيار سلوكي محدد
class CriteriaRating {
  /// المعيار المُقيَّم
  final BehaviorCriteria criteria;

  /// النقاط المحصل عليها (من 1 إلى 5)
  final int score;

  /// ملاحظات على هذا المعيار
  final String? notes;

  /// هل هذا المعيار قابل للتطبيق على هذا الطالب؟
  final bool applicable;

  const CriteriaRating({
    required this.criteria,
    required this.score,
    this.notes,
    this.applicable = true,
  });

  factory CriteriaRating.fromMap(Map<String, dynamic> map) {
    return CriteriaRating(
      criteria: BehaviorCriteria.values.firstWhere(
        (e) => e.toString() == map['criteria'],
        orElse: () => BehaviorCriteria.respect,
      ),
      score: map['score'] ?? 1,
      notes: map['notes'],
      applicable: map['applicable'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'criteria': criteria.toString(),
      'score': score,
      'notes': notes,
      'applicable': applicable,
    };
  }

  /// الحصول على وصف نصي للنقاط
  String get scoreDescription {
    switch (score) {
      case 5:
        return 'ممتاز';
      case 4:
        return 'جيد جداً';
      case 3:
        return 'جيد';
      case 2:
        return 'مقبول';
      case 1:
        return 'ضعيف';
      default:
        return 'غير محدد';
    }
  }
}

/// إجراء تم اتخاذه نتيجة للسلوك
class BehaviorAction {
  /// نوع الإجراء
  final ActionType type;

  /// وصف الإجراء
  final String description;

  /// من قام بتنفيذ الإجراء
  final String implementedBy;

  /// تاريخ تنفيذ الإجراء
  final DateTime implementedAt;

  /// مدة الإجراء (بالأيام) - للعقوبات المؤقتة
  final int? durationDays;

  /// هل تم تنفيذ الإجراء بالفعل؟
  final bool completed;

  /// ملاحظات على الإجراء
  final String? notes;

  const BehaviorAction({
    required this.type,
    required this.description,
    required this.implementedBy,
    required this.implementedAt,
    this.durationDays,
    this.completed = false,
    this.notes,
  });

  factory BehaviorAction.fromMap(Map<String, dynamic> map) {
    return BehaviorAction(
      type: ActionType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ActionType.verbalWarning,
      ),
      description: map['description'] ?? '',
      implementedBy: map['implementedBy'] ?? '',
      implementedAt:
          (map['implementedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      durationDays: map['durationDays'],
      completed: map['completed'] ?? false,
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'description': description,
      'implementedBy': implementedBy,
      'implementedAt': Timestamp.fromDate(implementedAt),
      'durationDays': durationDays,
      'completed': completed,
      'notes': notes,
    };
  }
}

/// خطة تحسين السلوك
class BehaviorImprovementPlan {
  /// معرف الخطة
  final String id;

  /// أهداف التحسين
  final List<String> goals;

  /// الاستراتيجيات المقترحة
  final List<String> strategies;

  /// الجدول الزمني للتنفيذ
  final DateTime startDate;
  final DateTime endDate;

  /// المسؤولون عن التنفيذ
  final List<String> responsiblePersons;

  /// مؤشرات النجاح
  final List<String> successIndicators;

  /// تاريخ المراجعة
  final DateTime reviewDate;

  /// حالة الخطة
  final PlanStatus status;

  /// ملاحظات على الخطة
  final String? notes;

  const BehaviorImprovementPlan({
    required this.id,
    required this.goals,
    required this.strategies,
    required this.startDate,
    required this.endDate,
    required this.responsiblePersons,
    required this.successIndicators,
    required this.reviewDate,
    required this.status,
    this.notes,
  });

  factory BehaviorImprovementPlan.fromMap(Map<String, dynamic> map) {
    return BehaviorImprovementPlan(
      id: map['id'] ?? '',
      goals: List<String>.from(map['goals'] ?? []),
      strategies: List<String>.from(map['strategies'] ?? []),
      startDate: (map['startDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      endDate: (map['endDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      responsiblePersons: List<String>.from(map['responsiblePersons'] ?? []),
      successIndicators: List<String>.from(map['successIndicators'] ?? []),
      reviewDate: (map['reviewDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: PlanStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => PlanStatus.active,
      ),
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'goals': goals,
      'strategies': strategies,
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'responsiblePersons': responsiblePersons,
      'successIndicators': successIndicators,
      'reviewDate': Timestamp.fromDate(reviewDate),
      'status': status.toString(),
      'notes': notes,
    };
  }
}

/// مرفق تقييم السلوك
class BehaviorAttachment {
  /// معرف المرفق
  final String id;

  /// اسم الملف
  final String name;

  /// رابط الملف
  final String url;

  /// نوع الملف
  final String type;

  /// حجم الملف بالبايت
  final int size;

  /// تاريخ الرفع
  final DateTime uploadedAt;

  /// معرف من قام بالرفع
  final String uploadedBy;

  const BehaviorAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
    required this.uploadedAt,
    required this.uploadedBy,
  });

  factory BehaviorAttachment.fromMap(Map<String, dynamic> map) {
    return BehaviorAttachment(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      type: map['type'] ?? '',
      size: map['size'] ?? 0,
      uploadedAt: (map['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      uploadedBy: map['uploadedBy'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type,
      'size': size,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'uploadedBy': uploadedBy,
    };
  }
}

/// شاهد على السلوك
class Witness {
  /// معرف الشاهد
  final String id;

  /// اسم الشاهد
  final String name;

  /// دور الشاهد (طالب، معلم، موظف)
  final String role;

  /// شهادة الشاهد
  final String testimony;

  /// تاريخ الشهادة
  final DateTime witnessedAt;

  const Witness({
    required this.id,
    required this.name,
    required this.role,
    required this.testimony,
    required this.witnessedAt,
  });

  factory Witness.fromMap(Map<String, dynamic> map) {
    return Witness(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      role: map['role'] ?? '',
      testimony: map['testimony'] ?? '',
      witnessedAt:
          (map['witnessedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'role': role,
      'testimony': testimony,
      'witnessedAt': Timestamp.fromDate(witnessedAt),
    };
  }
}

// ===================================================================
// التعدادات (Enums)
// ===================================================================

/// أنواع التقييم
enum EvaluationType {
  daily, // يومي
  weekly, // أسبوعي
  monthly, // شهري
  incident, // حدث محدد
  periodic, // دوري
  special, // خاص
}

/// فئات السلوك
enum BehaviorCategory {
  discipline, // الانضباط
  cooperation, // التعاون
  respect, // الاحترام
  responsibility, // المسؤولية
  honesty, // الصدق
  participation, // المشاركة
  leadership, // القيادة
  creativity, // الإبداع
  social, // اجتماعي
  academic, // أكاديمي
}

/// أنواع السلوك
enum BehaviorType {
  positive, // إيجابي
  negative, // سلبي
  neutral, // محايد
}

/// مستويات الشدة
enum SeverityLevel {
  low, // منخفض
  medium, // متوسط
  high, // عالي
  severe, // شديد
}

/// التقييم العام
enum OverallRating {
  excellent, // ممتاز
  veryGood, // جيد جداً
  good, // جيد
  acceptable, // مقبول
  poor, // ضعيف
}

/// معايير السلوك
enum BehaviorCriteria {
  respect, // الاحترام
  cooperation, // التعاون
  responsibility, // المسؤولية
  honesty, // الصدق
  punctuality, // الالتزام بالوقت
  participation, // المشاركة
  leadership, // القيادة
  helpfulness, // المساعدة
  selfControl, // ضبط النفس
  initiative, // المبادرة
}

/// أنواع الإجراءات
enum ActionType {
  verbalWarning, // تحذير شفهي
  writtenWarning, // تحذير كتابي
  detention, // احتجاز
  suspension, // إيقاف
  counseling, // إرشاد
  parentMeeting, // اجتماع مع ولي الأمر
  communityService, // خدمة مجتمعية
  reward, // مكافأة
  recognition, // تقدير
  privilegeRemoval, // إزالة امتياز
}

/// طرق الإشعار
enum NotificationMethod {
  message, // رسالة
  call, // مكالمة
  meeting, // اجتماع
  email, // إيميل
  letter, // خطاب
}

/// حالة التقييم
enum EvaluationStatus {
  draft, // مسودة
  review, // مراجعة
  final_, // نهائي
  archived, // مؤرشف
}

/// مستوى الخصوصية
enum PrivacyLevel {
  general, // عام
  limited, // محدود
  confidential, // سري
}

/// حالة خطة التحسين
enum PlanStatus {
  active, // نشطة
  completed, // مكتملة
  suspended, // معلقة
  cancelled, // ملغية
}
