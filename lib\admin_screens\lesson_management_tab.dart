import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/lesson_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class LessonManagementTab extends ConsumerWidget {
  final Function(LessonModel) onEdit;
  final Function(LessonModel) onDelete;

  const LessonManagementTab({
    super.key,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final lessonsAsyncValue = ref.watch(lessonsStreamProvider);

    return Scaffold(
      body: lessonsAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error: (err, stack) => ErrorMessage(message: 'خطأ: $err'),
        data: (lessons) {
          if (lessons.isEmpty) {
            return const Center(child: Text('لا توجد دروس. اضغط + للإضافة.'));
          }
          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: lessons.length,
            itemBuilder: (context, index) {
              final lesson = lessons[index];
              return CustomCard(
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundImage: (lesson.imageUrls.isNotEmpty)
                        ? NetworkImage(lesson.imageUrls.first)
                        : null,
                    child: (lesson.imageUrls.isEmpty)
                        ? const Icon(Icons.book_online)
                        : null,
                  ),
                  title: Text(
                    lesson.title,
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                  subtitle: Text(lesson.subject),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      IconButton(
                        icon: const Icon(Icons.edit, color: Colors.blue),
                        tooltip: 'تعديل',
                        onPressed: () => onEdit(lesson),
                      ),
                      IconButton(
                        icon: const Icon(Icons.delete, color: Colors.red),
                        tooltip: 'حذف',
                        onPressed: () => onDelete(lesson),
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
