import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/admin_screens/class_attendance_screen.dart';
import 'package:school_management_system/admin_screens/student_attendance_details_screen.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/models/attendance_model.dart';
import 'package:school_management_system/providers/attendance_providers.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// A screen for managing student attendance, refactored with Riverpod.
class AttendanceManagementScreen extends ConsumerWidget {
  const AttendanceManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedClassId = ref.watch(selectedClassIdProvider);
    final selectedDate = ref.watch(selectedAttendanceDateProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('مراقبة الحضور والغياب'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          if (selectedClassId != null)
            IconButton(
              icon: const Icon(Icons.analytics),
              tooltip: 'إحصائيات الحضور',
              onPressed: () => _showAttendanceStatistics(context, ref, selectedClassId),
            ),
          // زر تسجيل الحضور
          if (selectedClassId != null)
            IconButton(
              icon: const Icon(Icons.checklist_rtl),
              tooltip: 'تسجيل حضور الفصل',
              onPressed: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ClassAttendanceScreen(classId: selectedClassId),
                  ),
                );
              },
            ),
          // زر التصدير
          if (selectedClassId != null)
            IconButton(
              icon: const Icon(Icons.download),
              tooltip: 'تصدير البيانات',
              onPressed: () => _exportAttendanceData(context, ref, selectedClassId),
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط التحكم المحسن
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Column(
              children: [
                // اختيار الفصل
                const ClassesDropdown(),
                
                if (selectedClassId != null) ...[
                  const SizedBox(height: 16),
                  
                  // شريط البحث ومرشح التاريخ
                  Row(
                    children: [
                      // شريط البحث
                      Expanded(
                        flex: 2,
                        child: TextField(
                          onChanged: (value) {
                            ref.read(attendanceSearchQueryProvider.notifier).state = value;
                          },
                          decoration: InputDecoration(
                            labelText: 'ابحث عن طالب بالاسم أو الرقم الأكاديمي...',
                            prefixIcon: const Icon(Icons.search),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            filled: true,
                            fillColor: Colors.white,
                          ),
                        ),
                      ),
                      
                      const SizedBox(width: 12),
                      
                      // مرشح التاريخ
                      Expanded(
                        child: InkWell(
                          onTap: () => _selectDate(context, ref),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey.shade300),
                              borderRadius: BorderRadius.circular(8),
                              color: Colors.white,
                            ),
                            child: Row(
                              children: [
                                const Icon(Icons.calendar_today, size: 20),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: Text(
                                    selectedDate != null
                                        ? DateFormat.yMMMd('ar').format(selectedDate)
                                        : 'اختر التاريخ',
                                    style: TextStyle(
                                      color: selectedDate != null 
                                          ? Colors.black87 
                                          : Colors.grey.shade600,
                                    ),
                                  ),
                                ),
                                const Icon(Icons.arrow_drop_down),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // مرشحات الحضور
                  SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: AttendanceStatus.values.map((status) {
                        final isSelected = ref.watch(selectedAttendanceStatusProvider) == status;
                        return Padding(
                          padding: const EdgeInsets.only(right: 8),
                          child: FilterChip(
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  status.icon,
                                  size: 16,
                                  color: isSelected ? Colors.white : status.color,
                                ),
                                const SizedBox(width: 6),
                                Text(status.arabicName),
                              ],
                            ),
                            selected: isSelected,
                            onSelected: (selected) {
                              ref.read(selectedAttendanceStatusProvider.notifier).state = 
                                  selected ? status : null;
                            },
                            selectedColor: status.color,
                            labelStyle: TextStyle(
                              color: isSelected ? Colors.white : Colors.black87,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        );
                      }).toList(),
                    ),
                  ),
                ],
              ],
            ),
          ),
          
          // قائمة الطلاب
          const Expanded(child: StudentsList()),
        ],
      ),
    );
  }

  /// اختيار التاريخ
  Future<void> _selectDate(BuildContext context, WidgetRef ref) async {
    final selectedDate = ref.read(selectedAttendanceDateProvider);
    final picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    
    if (picked != null) {
      ref.read(selectedAttendanceDateProvider.notifier).state = picked;
    }
  }

  /// عرض إحصائيات الحضور
  void _showAttendanceStatistics(BuildContext context, WidgetRef ref, String classId) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          height: MediaQuery.of(context).size.height * 0.6,
          padding: const EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(Icons.analytics, color: Colors.blue),
                  const SizedBox(width: 12),
                  const Text(
                    'إحصائيات الحضور والغياب',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
              
              const Divider(),
              
              const Expanded(
                child: Center(
                  child: Text(
                    'ميزة الإحصائيات قيد التطوير',
                    style: TextStyle(fontSize: 16),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تصدير بيانات الحضور
  void _exportAttendanceData(BuildContext context, WidgetRef ref, String classId) {
    // TODO: تنفيذ تصدير بيانات الحضور
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة التصدير قيد التطوير')),
    );
  }
}

/// A dropdown widget to select a class.
class ClassesDropdown extends ConsumerWidget {
  const ClassesDropdown({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classesAsyncValue = ref.watch(classesStreamProvider);
    final selectedClassId = ref.watch(selectedClassIdProvider);

    return classesAsyncValue.when(
      data: (classes) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: DropdownButtonFormField<String>(
            value: selectedClassId,
            hint: Text('اختر الفصل الدراسي', style: GoogleFonts.cairo()),
            items: classes.map((ClassModel classModel) {
              return DropdownMenuItem<String>(
                value: classModel.id,
                child: Text(
                  classModel.name,
                  style: GoogleFonts.cairo(),
                ),
              );
            }).toList(),
            onChanged: (value) {
              ref.read(selectedClassIdProvider.notifier).state = value;
            },
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            ),
          ),
        );
      },
      loading: () => const Padding(
        padding: EdgeInsets.all(16.0),
        child: LoadingIndicator(),
      ),
      error: (err, stack) => ErrorMessage(message: err.toString()),
    );
  }
}

/// A list widget to display students based on the selected class and search query.
class StudentsList extends ConsumerWidget {
  const StudentsList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedClassId = ref.watch(selectedClassIdProvider);
    final classId = ref.watch(selectedClassIdProvider);
    final studentsAsyncValue = ref.watch(classStudentsStreamProvider(classId));
    final filteredStudents = ref.watch(filteredAttendanceStudentsProvider);

    if (selectedClassId == null) {
      return Center(
        child: Text(
          'الرجاء اختيار فصل دراسي لعرض الطلاب',
          style: GoogleFonts.cairo(fontSize: 16),
        ),
      );
    }

    return studentsAsyncValue.when(
      data: (students) {
        if (students.isEmpty) {
          return const Center(child: Text('لا يوجد طلاب في هذا الفصل.'));
        }
        if (filteredStudents.isEmpty) {
          return const Center(child: Text('لم يتم العثور على نتائج.'));
        }
        return ListView.builder(
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final student = filteredStudents[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ListTile(
                title: Text(student.name, style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
                subtitle: Text('الرقم الأكاديمي: ${student.studentNumber}'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => StudentAttendanceDetailsScreen(student: student),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => ErrorMessage(message: err.toString()),
    );
  }
}
