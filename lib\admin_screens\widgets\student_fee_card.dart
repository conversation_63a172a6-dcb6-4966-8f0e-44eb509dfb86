import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/fee_type_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/fees_providers.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/services/pdf_export_service.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// ويدجت لعرض تفاصيل الرسوم لطالب واحد
class StudentFeeCard extends ConsumerWidget {
  final StudentModel student;

  const StudentFeeCard({super.key, required this.student});

  void _showAssignFeeDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AssignFeeDialog(student: student),
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ExpansionTile(
        title: Text(
          student.name,
          style: GoogleFonts.cairo(fontWeight: FontWeight.bold),
        ),
        subtitle: Text('الرقم الأكاديمي: ${student.studentNumber}'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: ElevatedButton.icon(
                    onPressed: () => _showAssignFeeDialog(context),
                    icon: const Icon(Icons.add),
                    label: const Text('إضافة رسوم جديدة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).primaryColor,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                FeeDetails(studentId: student.id),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// ويدجت لعرض تفاصيل الرسوم المالية للطالب
class FeeDetails extends ConsumerWidget {
  final String studentId;

  const FeeDetails({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final financialDetailsAsync = ref.watch(studentFinancialDetailsProvider(studentId));
    final PdfExportService pdfExportService = PdfExportService();

    return financialDetailsAsync.when(
      loading: () => const LoadingIndicator(),
      error: (err, stack) => Text('خطأ: $err'),
      data: (details) {
        final assignedFees = details['assignedFees'] as List<Map<String, dynamic>>;
        final payments = details['payments'] as List<Map<String, dynamic>>;
        final totalAmount = details['totalAssigned'] as double;
        final paidAmount = details['totalPaid'] as double;
        final remainingAmount = details['totalRemaining'] as double;

        if (assignedFees.isEmpty) {
          return const Center(child: Text('لا توجد رسوم مخصصة لهذا الطالب بعد.'));
        }

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            FinancialSummary(
              totalFees: totalAmount,
              paidAmount: paidAmount,
              remainingAmount: remainingAmount,
            ),
            const SizedBox(height: 16),
            Align(
              alignment: Alignment.centerRight,
              child: ElevatedButton.icon(
                onPressed: () => _showPdfOptionsDialog(context, ref.read(studentsForFeesProvider).value!.firstWhere((s) => s.id == studentId), details),
                icon: const Icon(Icons.picture_as_pdf),
                label: const Text('تقرير PDF للرسوم'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            const SizedBox(height: 16),
            const Text('تفاصيل الرسوم:', style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
            const Divider(),
            ...assignedFees.map((feeData) {
              final remainingForFee = (feeData['amount_total'] as num) - (feeData['amount_paid'] as num);
              return AssignedFeeRow(
                feeTypeName: feeData['fee_type_name'],
                totalAmount: (feeData['amount_total'] as num).toDouble(),
                paidAmount: (feeData['amount_paid'] as num).toDouble(),
                remainingAmount: remainingForFee.toDouble(),
                onPay: () => _showPaymentDialog(context, ref.read(studentsForFeesProvider).value!.firstWhere((s) => s.id == studentId), feeData['id'], feeData['fee_type_name'], remainingForFee.toDouble()),
              );
            }).toList(),
            const Divider(height: 32),
            PaymentHistory(studentId: studentId),
          ],
        );
      },
    );
  }

  void _showPdfOptionsDialog(BuildContext context, StudentModel student, Map<String, dynamic> financialData) {
     final PdfExportService pdfExportService = PdfExportService();
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('تقرير PDF'),
          content: const Text('اختر الإجراء الذي تريده.'),
          actions: <Widget>[
            TextButton(
              child: const Text('طباعة'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                pdfExportService.generateAndPrintPdf(student, financialData: financialData, grades: [], attendance: []);
              },
            ),
            TextButton(
              child: const Text('تحميل'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
                pdfExportService.generateAndDownloadPdf(context, student, financialData: financialData, grades: [], attendance: []);
              },
            ),
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
          ],
        );
      },
    );
  }

  void _showPaymentDialog(BuildContext context, StudentModel student, String assignedFeeId, String feeTypeName, double remainingAmount) {
    showDialog(
      context: context,
      builder: (context) => PaymentDialog(
        student: student,
        assignedFeeId: assignedFeeId,
        feeTypeName: feeTypeName,
        remainingAmount: remainingAmount,
      ),
    );
  }
}

// Other widgets like AssignFeeDialog, AssignedFeeRow, PaymentDialog, FinancialSummary, FinancialInfo, PaymentHistory would be here
// For brevity, they are not included in this refactoring step but should be moved to their own files or kept here.
// Let's assume they are moved to separate files for a cleaner architecture.
// For now, I will include them here to make the code runnable.

class AssignFeeDialog extends ConsumerStatefulWidget {
  final StudentModel student;
  const AssignFeeDialog({super.key, required this.student});
  @override
  ConsumerState<AssignFeeDialog> createState() => _AssignFeeDialogState();
}

class _AssignFeeDialogState extends ConsumerState<AssignFeeDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  FeeTypeModel? _selectedFeeType;
  
  // This should be a provider
  Future<List<FeeTypeModel>> _loadFeeTypes(WidgetRef ref) async {
    final firebaseService = ref.read(firebaseServiceProvider);
    final feeTypesSnapshot = await firebaseService.getFeeTypes().first;
    return feeTypesSnapshot.docs.map((doc) => FeeTypeModel.fromMap(doc.data() as Map<String, dynamic>, doc.id)).toList();
  }

  void _submitAssignedFee(WidgetRef ref) async {
    if (_formKey.currentState!.validate()) {
      if (_selectedFeeType == null) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('الرجاء اختيار نوع الرسوم')));
        return;
      }
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text;
      try {
        await ref.read(firebaseServiceProvider).assignFeeToStudent(widget.student.id, {
          'fee_type_id': _selectedFeeType!.id,
          'fee_type_name': _selectedFeeType!.name,
          'amount_total': amount,
          'amount_paid': 0,
          'notes': notes,
          'created_at': Timestamp.now(),
        });
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تمت إضافة الرسوم بنجاح')));
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('حدث خطأ: $e')));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('إضافة رسوم جديدة لـ ${widget.student.name}'),
      content: FutureBuilder<List<FeeTypeModel>>(
        future: _loadFeeTypes(ref),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const LoadingIndicator();
          }
          if (snapshot.hasError || !snapshot.hasData) {
            return const Text('خطأ في تحميل أنواع الرسوم');
          }
          final feeTypes = snapshot.data!;
          return Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<FeeTypeModel>(
                  value: _selectedFeeType,
                  hint: const Text('اختر نوع الرسوم'),
                  items: feeTypes.map((feeType) {
                    return DropdownMenuItem<FeeTypeModel>(value: feeType, child: Text(feeType.name));
                  }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedFeeType = value;
                      _amountController.text = value?.defaultValue.toString() ?? '';
                    });
                  },
                  validator: (value) => value == null ? 'نوع الرسوم مطلوب' : null,
                  decoration: const InputDecoration(border: OutlineInputBorder()),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _amountController,
                  decoration: const InputDecoration(labelText: 'القيمة', border: OutlineInputBorder()),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) return 'المبلغ مطلوب';
                    if (double.tryParse(value) == null) return 'مبلغ غير صالح';
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _notesController,
                  decoration: const InputDecoration(labelText: 'ملاحظات (اختياري)', border: OutlineInputBorder()),
                ),
              ],
            ),
          );
        },
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(onPressed: () => _submitAssignedFee(ref), child: const Text('تأكيد الإضافة')),
      ],
    );
  }
}

class AssignedFeeRow extends StatelessWidget {
  final String feeTypeName;
  final double totalAmount;
  final double paidAmount;
  final double remainingAmount;
  final VoidCallback onPay;

  const AssignedFeeRow({
    super.key,
    required this.feeTypeName,
    required this.totalAmount,
    required this.paidAmount,
    required this.remainingAmount,
    required this.onPay,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(feeTypeName, style: const TextStyle(fontWeight: FontWeight.bold)),
                Text('الإجمالي: ${totalAmount.toStringAsFixed(2)}'),
                Text('المدفوع: ${paidAmount.toStringAsFixed(2)}', style: const TextStyle(color: Colors.green)),
                Text('المتبقي: ${remainingAmount.toStringAsFixed(2)}', style: const TextStyle(color: Colors.red)),
              ],
            ),
          ),
          if (remainingAmount > 0)
            ElevatedButton(onPressed: onPay, child: const Text('دفع جديد')),
        ],
      ),
    );
  }
}

class PaymentDialog extends ConsumerStatefulWidget {
  final StudentModel student;
  final String assignedFeeId;
  final String feeTypeName;
  final double remainingAmount;

  const PaymentDialog({
    super.key,
    required this.student,
    required this.assignedFeeId,
    required this.feeTypeName,
    required this.remainingAmount,
  });

  @override
  ConsumerState<PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends ConsumerState<PaymentDialog> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _notesController = TextEditingController();
  DateTime _selectedDate = DateTime.now();

  void _submitPayment() async {
    if (_formKey.currentState!.validate()) {
      final amount = double.parse(_amountController.text);
      final notes = _notesController.text;
      final recordedBy = ref.read(firebaseServiceProvider).getCurrentUser()?.displayName ?? 'unknown';

      try {
        await ref.read(firebaseServiceProvider).addPaymentAndUpdateFee(
          studentId: widget.student.id,
          assignedFeeId: widget.assignedFeeId,
          paymentData: {
            'amount': amount,
            'assigned_fee_id': widget.assignedFeeId,
            'fee_type_name': widget.feeTypeName,
            'payment_date': Timestamp.fromDate(_selectedDate),
            'notes': notes,
            'recorded_by': recordedBy,
          },
        );
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تسجيل الدفع بنجاح.')));
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('حدث خطأ: $e')));
      }
    }
  }

  Future<void> _pickDate() async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: _selectedDate,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (pickedDate != null && pickedDate != _selectedDate) {
      setState(() {
        _selectedDate = pickedDate;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('دفع جديد لـ ${widget.feeTypeName}'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _amountController,
              decoration: const InputDecoration(labelText: 'المبلغ المدفوع'),
              keyboardType: TextInputType.number,
              validator: (value) {
                if (value == null || value.isEmpty) return 'المبلغ مطلوب';
                final amount = double.tryParse(value);
                if (amount == null || amount <= 0) return 'مبلغ غير صالح';
                if (amount > widget.remainingAmount) return 'المبلغ المدفوع أكبر من المتبقي';
                return null;
              },
            ),
            TextFormField(
              controller: _notesController,
              decoration: const InputDecoration(labelText: 'ملاحظات (اختياري)'),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: Text('تاريخ الدفع: ${DateFormat('yyyy/MM/dd').format(_selectedDate)}'),
              trailing: const Icon(Icons.calendar_today),
              onTap: _pickDate,
            ),
          ],
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(onPressed: _submitPayment, child: const Text('تأكيد الدفع')),
      ],
    );
  }
}

class FinancialSummary extends StatelessWidget {
  final double totalFees;
  final double paidAmount;
  final double remainingAmount;

  const FinancialSummary({
    super.key,
    required this.totalFees,
    required this.paidAmount,
    required this.remainingAmount,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            FinancialInfo(title: 'إجمالي الرسوم', amount: totalFees, color: Colors.blue.shade800),
            FinancialInfo(title: 'المدفوع', amount: paidAmount, color: Colors.green.shade800),
            FinancialInfo(title: 'المتبقي', amount: remainingAmount, color: Colors.red.shade800),
          ],
        ),
      ),
    );
  }
}

class FinancialInfo extends StatelessWidget {
  final String title;
  final double amount;
  final Color color;

  const FinancialInfo({
    super.key,
    required this.title,
    required this.amount,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(title, style: GoogleFonts.cairo(fontSize: 16)),
        const SizedBox(height: 4),
        Text(
          NumberFormat.currency(symbol: ' ر.ي', decimalDigits: 2).format(amount),
          style: GoogleFonts.lato(fontSize: 18, fontWeight: FontWeight.bold, color: color),
        ),
      ],
    );
  }
}

class PaymentHistory extends ConsumerWidget {
  final String studentId;
  const PaymentHistory({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // This should be a provider
    final paymentsStream = ref.watch(firebaseServiceProvider).getStudentPayments(studentId);
    return ExpansionTile(
      title: const Text('عرض سجل المدفوعات'),
      children: [
        StreamBuilder<QuerySnapshot>(
          stream: paymentsStream,
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) return const LoadingIndicator();
            if (!snapshot.hasData || snapshot.data!.docs.isEmpty) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: Center(child: Text('لا توجد مدفوعات مسجلة.')),
              );
            }
            final payments = snapshot.data!.docs;
            return SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: DataTable(
                columns: const [
                  DataColumn(label: Text('التاريخ')),
                  DataColumn(label: Text('النوع')),
                  DataColumn(label: Text('المبلغ')),
                  DataColumn(label: Text('ملاحظات')),
                  DataColumn(label: Text('المسجل')),
                ],
                rows: payments.map((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  return DataRow(cells: [
                    DataCell(Text(DateFormat('yyyy/MM/dd').format((data['payment_date'] as Timestamp).toDate()))),
                    DataCell(Text(data['fee_type_name'] ?? 'غير محدد')),
                    DataCell(Text(NumberFormat.currency(symbol: ' ر.ي', decimalDigits: 2).format(data['amount']))),
                    DataCell(Text(data['notes'] ?? '')),
                    DataCell(Text(data['recorded_by'] ?? '')),
                  ]);
                }).toList(),
              ),
            );
          },
        ),
      ],
    );
  }
}
