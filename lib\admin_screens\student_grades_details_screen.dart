import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/shared_widgets/subject_dropdown.dart';

class StudentGradesDetailsScreen extends StatefulWidget {
  final StudentModel student;

  const StudentGradesDetailsScreen({Key? key, required this.student}) : super(key: key);

  @override
  _StudentGradesDetailsScreenState createState() => _StudentGradesDetailsScreenState();
}

class _StudentGradesDetailsScreenState extends State<StudentGradesDetailsScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('درجات الطالب: ${widget.student.name}'),
      ),
      body: StreamBuilder<QuerySnapshot>(
        // 1. الاستعلام الأول: جلب المواد المتاحة للطالب.
        // يتم جلب المواد الخاصة بصف الطالب (`widget.student.classId`) بالإضافة إلى المواد العامة (`'all'`).
        stream: FirebaseFirestore.instance
            .collection('subjects')
            .where('classId', whereIn: [widget.student.classId, 'all'])
            .orderBy('name')
            .snapshots(),
        builder: (context, subjectSnapshot) {
          if (subjectSnapshot.connectionState == ConnectionState.waiting) {
            return const LoadingIndicator();
          }
          if (subjectSnapshot.hasError) {
            return Center(child: Text('حدث خطأ: ${subjectSnapshot.error}'));
          }
          if (!subjectSnapshot.hasData || subjectSnapshot.data!.docs.isEmpty) {
            return const Center(child: Text('لا توجد مواد متاحة لهذا الطالب.'));
          }
          final subjects = subjectSnapshot.data!.docs;
          // تمرير قائمة المواد المتاحة إلى الويدجت الذي يبني القائمة.
          return _buildGradesList(widget.student, subjects);
        },
      ),
    );
  }

  /// ويدجت يبني قائمة المواد مع درجات الطالب.
  Widget _buildGradesList(StudentModel student, List<QueryDocumentSnapshot> subjects) {
    // 2. الاستعلام الثاني: جلب الدرجات المسجلة للطالب.
    // هذا استعلام متداخل يعتمد على نتيجة الاستعلام الأول (قائمة المواد).
    return StreamBuilder<QuerySnapshot>(
      stream: FirebaseFirestore.instance.collection('students').doc(student.id).collection('grades').snapshots(),
      builder: (context, gradeSnapshot) {
        if (gradeSnapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }
        // إنشاء خريطة (Map) من الدرجات لتسهيل الوصول إلى درجة كل مادة باستخدام ID المادة.
        final gradesMap = {for (var doc in gradeSnapshot.data!.docs) doc.id: doc.data() as Map<String, dynamic>};

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: subjects.length,
          itemBuilder: (context, index) {
            final subjectDoc = subjects[index];
            final subjectName = subjectDoc['name'];
            // البحث عن درجة المادة في الخريطة باستخدام ID المادة.
            // إذا لم توجد درجة، يتم عرض 'N/A'.
            final grade = gradesMap[subjectDoc.id]?['grade']?.toString() ?? 'N/A';

            return Card(
              margin: const EdgeInsets.only(bottom: 12),
              child: ListTile(
                title: Text(subjectName, style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
                trailing: Text(grade, style: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold)),
                // عند الضغط، تفتح نافذة لتعديل الدرجة.
                onTap: () => _showEditGradeDialog(student, subjectDoc, grade),
              ),
            );
          },
        );
      },
    );
  }

  /// عرض نافذة منبثقة لتعديل درجة الطالب لمادة معينة.
  void _showEditGradeDialog(StudentModel student, DocumentSnapshot subjectDoc, String currentGrade) {
    final formKey = GlobalKey<FormState>();
    final gradeController = TextEditingController(text: currentGrade == 'N/A' ? '' : currentGrade);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('تعديل درجة ${student.name}'),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('المادة: ${subjectDoc['name']}', style: const TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                TextFormField(
                  controller: gradeController,
                  keyboardType: TextInputType.number,
                  decoration: const InputDecoration(labelText: 'الدرجة الجديدة'),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال درجة';
                    }
                    final grade = int.tryParse(value);
                    if (grade == null) {
                      return 'الرجاء إدخال رقم صحيح';
                    }
                    if (grade < 0 || grade > 100) {
                      return 'الدرجة يجب أن تكون بين 0 و 100';
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final newGrade = int.parse(gradeController.text);
                  try {
                    // تحديث أو إنشاء مستند الدرجة في المجموعة الفرعية 'grades' للطالب.
                    // يتم استخدام ID المادة كمعرّف للمستند لضمان وجود درجة واحدة فقط لكل مادة.
                    await FirebaseFirestore.instance
                        .collection('students')
                        .doc(student.id)
                        .collection('grades')
                        .doc(subjectDoc.id)
                        .set({'grade': newGrade, 'subjectName': subjectDoc['name']});
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('تم تحديث الدرجة بنجاح')));
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('حدث خطأ: $e')));
                  }
                }
              },
              child: const Text('حفظ'),
            ),
          ],
        );
      },
    );
  }
}
