import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة جدول امتحانات المعلم
///
/// هذه الشاشة تعرض للمعلم جدوله الشخصي للامتحانات
/// مع جميع التفاصيل المتعلقة بالامتحانات المكلف بها
///
/// الوظائف الرئيسية:
/// - عرض الامتحانات المجدولة للمعلم
/// - تفاصيل كل امتحان (التاريخ، الوقت، القاعة، المادة)
/// - حالة الاستعداد لكل امتحان
/// - التذكيرات والإشعارات المهمة
/// - إمكانية تحديث حالة الاستعداد
/// - عرض الملاحظات الخاصة بكل امتحان
/// - تصدير الجدول وطباعته
///
/// أنواع العرض:
/// - عرض يومي: امتحانات اليوم الحالي
/// - عرض أسبوعي: امتحانات الأسبوع
/// - عرض شهري: جميع امتحانات الشهر
/// - عرض قائمة: جميع الامتحانات مرتبة زمنياً
class TeacherExamScheduleScreen extends ConsumerStatefulWidget {
  const TeacherExamScheduleScreen({super.key});

  @override
  ConsumerState<TeacherExamScheduleScreen> createState() =>
      _TeacherExamScheduleScreenState();
}

class _TeacherExamScheduleScreenState
    extends ConsumerState<TeacherExamScheduleScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والحالة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: اليوم، الأسبوع، الشهر، الكل
  late TabController _tabController;

  /// متحكم البحث
  final _searchController = TextEditingController();

  // ===================================================================
  // متغيرات الحالة الرئيسية
  // ===================================================================

  /// قائمة امتحانات المعلم
  List<ExamModel> _teacherExams = [];

  /// التاريخ المحدد حالياً للعرض
  DateTime _selectedDate = DateTime.now();

  /// نوع العرض المحدد
  ScheduleViewType _viewType = ScheduleViewType.today;

  /// حالة تحميل البيانات
  bool _isLoading = false;

  /// حالة تحديث البيانات
  bool _isRefreshing = false;

  /// فلتر البحث
  String _searchQuery = '';

  /// فلتر حالة الامتحان
  ExamStatus? _statusFilter;

  /// فلتر نوع الامتحان
  ExamType? _typeFilter;

  // ===================================================================
  // إحصائيات سريعة
  // ===================================================================

  /// عدد امتحانات اليوم
  int _todayExamsCount = 0;

  /// عدد امتحانات الأسبوع
  int _weekExamsCount = 0;

  /// عدد الامتحانات المعلقة
  int _pendingExamsCount = 0;

  /// عدد الامتحانات المكتملة
  int _completedExamsCount = 0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // تحميل البيانات الأولية
    _loadTeacherExams();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'جدول امتحاناتي',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.today, size: 20), text: 'اليوم'),
            Tab(icon: Icon(Icons.view_week, size: 20), text: 'الأسبوع'),
            Tab(icon: Icon(Icons.calendar_month, size: 20), text: 'الشهر'),
            Tab(icon: Icon(Icons.list, size: 20), text: 'الكل'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في الامتحانات',
          ),

          // زر الفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFiltersDialog(),
            tooltip: 'فلترة الامتحانات',
          ),

          // زر التحديث
          IconButton(
            icon:
                _isRefreshing
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isRefreshing ? null : () => _refreshData(),
            tooltip: 'تحديث البيانات',
          ),
        ],
      ),

      // محتوى التبويبات
      body: Column(
        children: [
          // قسم الإحصائيات السريعة
          _buildQuickStatsSection(),

          // محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                // تبويب امتحانات اليوم
                _buildTodayExamsTab(),

                // تبويب امتحانات الأسبوع
                _buildWeekExamsTab(),

                // تبويب امتحانات الشهر
                _buildMonthExamsTab(),

                // تبويب جميع الامتحانات
                _buildAllExamsTab(),
              ],
            ),
          ),
        ],
      ),

      // زر عائم لإضافة تذكير
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showAddReminderDialog(),
        backgroundColor: Colors.green[600],
        icon: const Icon(Icons.add_alert, color: Colors.white),
        label: const Text(
          'إضافة تذكير',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  ///
  /// يعرض ملخص سريع لامتحانات المعلم
  Widget _buildQuickStatsSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.green[50],
      child: Row(
        children: [
          // إحصائية امتحانات اليوم
          Expanded(
            child: _buildStatCard(
              title: 'اليوم',
              count: _todayExamsCount,
              icon: Icons.today,
              color: Colors.blue,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية امتحانات الأسبوع
          Expanded(
            child: _buildStatCard(
              title: 'الأسبوع',
              count: _weekExamsCount,
              icon: Icons.view_week,
              color: Colors.orange,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية الامتحانات المعلقة
          Expanded(
            child: _buildStatCard(
              title: 'معلقة',
              count: _pendingExamsCount,
              icon: Icons.pending,
              color: Colors.red,
            ),
          ),
          const SizedBox(width: 12),

          // إحصائية الامتحانات المكتملة
          Expanded(
            child: _buildStatCard(
              title: 'مكتملة',
              count: _completedExamsCount,
              icon: Icons.check_circle,
              color: Colors.green,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية واحدة
  Widget _buildStatCard({
    required String title,
    required int count,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(title, style: const TextStyle(fontSize: 12, color: Colors.grey)),
        ],
      ),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// تحميل امتحانات المعلم
  Future<void> _loadTeacherExams() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل امتحانات المعلم من قاعدة البيانات
      // محاكاة البيانات
      await Future.delayed(const Duration(seconds: 1));

      // تحديث الإحصائيات
      _updateStatistics();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الامتحانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث نوع العرض حسب التبويب المحدد
    setState(() {
      switch (_tabController.index) {
        case 0:
          _viewType = ScheduleViewType.today;
          break;
        case 1:
          _viewType = ScheduleViewType.week;
          break;
        case 2:
          _viewType = ScheduleViewType.month;
          break;
        case 3:
          _viewType = ScheduleViewType.all;
          break;
      }
    });
  }

  /// تحديث الإحصائيات
  void _updateStatistics() {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final weekStart = today.subtract(Duration(days: today.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 6));

    setState(() {
      // حساب امتحانات اليوم
      _todayExamsCount =
          _teacherExams.where((exam) {
            final examDate = DateTime(
              exam.startDate.year,
              exam.startDate.month,
              exam.startDate.day,
            );
            return examDate.isAtSameMomentAs(today);
          }).length;

      // حساب امتحانات الأسبوع
      _weekExamsCount =
          _teacherExams.where((exam) {
            final examDate = DateTime(
              exam.startDate.year,
              exam.startDate.month,
              exam.startDate.day,
            );
            return examDate.isAfter(
                  weekStart.subtract(const Duration(days: 1)),
                ) &&
                examDate.isBefore(weekEnd.add(const Duration(days: 1)));
          }).length;

      // حساب الامتحانات المعلقة والمكتملة
      _pendingExamsCount =
          _teacherExams
              .where((exam) => exam.status == ExamStatus.scheduled)
              .length;
      _completedExamsCount =
          _teacherExams
              .where((exam) => exam.status == ExamStatus.completed)
              .length;
    });
  }

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في الامتحانات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن امتحان...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              autofocus: true,
              onChanged: (value) {
                setState(() {
                  _searchQuery = value;
                });
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performSearch();
                },
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة الامتحانات'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // فلتر حالة الامتحان
                DropdownButtonFormField<ExamStatus?>(
                  value: _statusFilter,
                  decoration: const InputDecoration(
                    labelText: 'حالة الامتحان',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ExamStatus.values.map(
                      (status) => DropdownMenuItem(
                        value: status,
                        child: Text(status.arabicName),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _statusFilter = value;
                    });
                  },
                ),
                const SizedBox(height: 16),

                // فلتر نوع الامتحان
                DropdownButtonFormField<ExamType?>(
                  value: _typeFilter,
                  decoration: const InputDecoration(
                    labelText: 'نوع الامتحان',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ExamType.values.map(
                      (type) => DropdownMenuItem(
                        value: type,
                        child: Text(type.arabicName),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _typeFilter = value;
                    });
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _statusFilter = null;
                    _typeFilter = null;
                  });
                  Navigator.pop(context);
                },
                child: const Text('إعادة تعيين'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('تطبيق'),
              ),
            ],
          ),
    );
  }

  /// تنفيذ البحث
  void _performSearch() {
    // TODO: تطبيق منطق البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('البحث عن: $_searchQuery'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// تحديث البيانات
  Future<void> _refreshData() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      await _loadTeacherExams();

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث البيانات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث البيانات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// عرض حوار إضافة تذكير
  void _showAddReminderDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة تذكير'),
            content: const Text('سيتم تطبيق ميزة التذكيرات قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }

  /// بناء تبويب امتحانات اليوم
  Widget _buildTodayExamsTab() {
    return const Center(
      child: Text(
        'امتحانات اليوم\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب امتحانات الأسبوع
  Widget _buildWeekExamsTab() {
    return const Center(
      child: Text(
        'امتحانات الأسبوع\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب امتحانات الشهر
  Widget _buildMonthExamsTab() {
    return const Center(
      child: Text(
        'امتحانات الشهر\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب جميع الامتحانات
  Widget _buildAllExamsTab() {
    return const Center(
      child: Text(
        'جميع الامتحانات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }
}

/// تعداد أنواع عرض الجدول
enum ScheduleViewType {
  today, // اليوم
  week, // الأسبوع
  month, // الشهر
  all, // الكل
}
