import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/assign_students_dialog.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class ClassDetailsView extends ConsumerWidget {
  const ClassDetailsView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classId = ref.watch(selectedClassIdProvider);
    final studentsAsyncValue = ref.watch(classStudentsStreamProvider(classId));
    final className = ref.watch(selectedClassNameProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text('طلاب فصل $className'),
        actions: [
          IconButton(
            icon: const Icon(Icons.group_add),
            onPressed: () {
              final currentStudents = studentsAsyncValue.asData?.value ?? [];
              final currentStudentIds = currentStudents.map((s) => s.id).toList();
              showDialog(
                context: context,
                builder: (context) => AssignStudentsDialog(classId: classId!, assignedStudentIds: currentStudentIds),
              );
            },
            tooltip: 'تسكين الطلاب',
          ),
        ],
      ),
      body: studentsAsyncValue.when(
        data: (students) {
          if (students.isEmpty) {
            return const Center(child: Text('هذا الفصل فارغ'));
          }
          return ListView.builder(
            itemCount: students.length,
            itemBuilder: (context, index) {
              final student = students[index];
              return ListTile(
                title: Text(student.name),
                subtitle: Text(student.studentNumber),
              );
            },
          );
        },
        loading: () => const LoadingIndicator(),
        error: (err, stack) => Text('خطأ: $err'),
      ),
    );
  }
}
