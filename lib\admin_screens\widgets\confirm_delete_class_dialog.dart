import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/services_provider.dart';

Future<void> showConfirmDeleteClassDialog(BuildContext context, WidgetRef ref, ClassModel classModel) {
  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('تأكيد الحذف'),
      content: Text('هل أنت متأكد من رغبتك في حذف الفصل ${classModel.name}؟'),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: () {
            ref.read(firebaseServiceProvider).deleteClass(classModel.id);
            ref.read(selectedClassIdProvider.notifier).state = null; // إلغاء اختيار الفصل بعد الحذ
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          child: const Text('حذف'),
        ),
      ],
    ),
  );
}
