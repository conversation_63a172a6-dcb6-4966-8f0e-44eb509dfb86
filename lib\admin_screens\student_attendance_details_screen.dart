import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:table_calendar/table_calendar.dart';

/// شاشة لعرض وتعديل تفاصيل حضور طالب معين باستخدام تقويم شهري.
/// يتم تحميل بيانات الحضور لكل شهر على حدة عند التنقل في التقويم.
/// ويتم حفظ أي تغيير في حالة الحضور بشكل فوري.
class StudentAttendanceDetailsScreen extends StatefulWidget {
  final StudentModel student;

  const StudentAttendanceDetailsScreen({super.key, required this.student});

  @override
  State<StudentAttendanceDetailsScreen> createState() => _StudentAttendanceDetailsScreenState();
}

class _StudentAttendanceDetailsScreenState extends State<StudentAttendanceDetailsScreen> {
  // مرجع لمجموعة الحضور الخاصة بالطالب في Firestore.
  // يتم بناؤه بناءً على معرّف الطالب الفريد.
  late final CollectionReference _attendanceRef;

  // خريطة لتخزين بيانات الحضور التي تم تحميلها من Firestore.
  // المفتاح هو `DateTime` (بتنسيق UTC لضمان التفرد) والقيمة هي حالة الحضور (نص).
  Map<DateTime, String> _attendanceData = {};

  // اليوم الذي يتم التركيز عليه حالياً في التقويم.
  DateTime _focusedDay = DateTime.now();

  // اليوم المحدد من قبل المستخدم في التقويم.
  DateTime? _selectedDay;

  // متغير لتتبع حالة الحفظ (لإظهار/إخفاء مؤشر التقدم).
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    // تهيئة مرجع Firestore باستخدام معرّف الطالب.
    // تم التعديل من `widget.student.uid` إلى `widget.student.id`.
    _attendanceRef = FirebaseFirestore.instance
        .collection('students')
        .doc(widget.student.id)
        .collection('attendance');
    _selectedDay = _focusedDay;
    // تحميل بيانات الحضور للشهر الحالي عند بدء تشغيل الشاشة.
    _loadAttendanceDataForMonth(_focusedDay);
  }

  /// دالة لتحميل بيانات الحضور لشهر معين من Firestore.
  /// يتم استدعاؤها عند بدء تشغيل الشاشة وعند تغيير الشهر في التقويم.
  Future<void> _loadAttendanceDataForMonth(DateTime month) async {
    // تحديد أول وآخر يوم في الشهر المطلوب لعمل استعلام دقيق.
    final firstDay = DateTime.utc(month.year, month.month, 1);
    final lastDay = DateTime.utc(month.year, month.month + 1, 0, 23, 59, 59);

    // جلب سجلات الحضور للشهر المحدد فقط.
    final snapshot = await _attendanceRef
        .where('date', isGreaterThanOrEqualTo: firstDay)
        .where('date', isLessThanOrEqualTo: lastDay)
        .get();

    // تحويل البيانات المسترجعة إلى خريطة.
    // نستخدم `DateTime.utc` لإنشاء مفتاح متناسق يتجاهل المنطقة الزمنية والتوقيت.
    // هذا يضمن أن `_attendanceData[DateTime.utc(2023, 10, 26)]` سيعمل دائماً
    // بغض النظر عن معلومات الوقت المخزنة في Timestamp.
    final data = {
      for (var doc in snapshot.docs)
        DateTime.utc(
          (doc['date'] as Timestamp).toDate().year,
          (doc['date'] as Timestamp).toDate().month,
          (doc['date'] as Timestamp).toDate().day,
        ): doc['status'] as String
    };

    // تحديث حالة الواجهة بالبيانات الجديدة إذا كانت الشاشة لا تزال معروضة.
    if (mounted) {
      setState(() {
        _attendanceData = data;
      });
    }
  }

  /// دالة لحفظ أو تحديث حالة الحضور ليوم معين في Firestore.
  /// يتم استدعاؤها عند اختيار حالة جديدة من مربع الحوار.
  Future<void> _updateAttendance(DateTime day, String status) async {
    if (!mounted) return;

    // إظهار مؤشر الحفظ.
    setState(() {
      _isSaving = true;
    });

    try {
      // استخدام التاريخ بتنسيق UTC (بدون معلومات الوقت) لضمان التناسق.
      final dateOnly = DateTime.utc(day.year, day.month, day.day);
      // إنشاء معرّف للمستند بناءً على التاريخ (مثال: '2023-10-26').
      final docId = DateFormat('yyyy-MM-dd').format(dateOnly);

      // حفظ البيانات في Firestore باستخدام `set` للكتابة فوق أي بيانات قديمة.
      await _attendanceRef.doc(docId).set({
        'date': Timestamp.fromDate(dateOnly),
        'status': status,
      });

      // تحديث البيانات محلياً لإعادة رسم الواجهة فوراً دون الحاجة لإعادة التحميل.
      if (mounted) {
        setState(() {
          _attendanceData[dateOnly] = status;
        });

        // إظهار رسالة تأكيد للمستخدم.
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة الحضور بنجاح', style: GoogleFonts.cairo()),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      // في حالة حدوث خطأ، يتم إظهار رسالة للمستخدم.
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ أثناء تحديث الحضور: $e', style: GoogleFonts.cairo()),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      // إخفاء مؤشر الحفظ في جميع الحالات (نجاح أو فشل).
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        // عنوان شريط التطبيق يوضح اسم الطالب.
        title: Text('سجل حضور ${widget.student.name}', style: GoogleFonts.cairo()),
      ),
      // استخدام Stack للسماح بوضع مؤشر الحفظ فوق المحتوى الرئيسي.
      body: Stack(
        children: [
          Column(
            children: [
              // ويدجت التقويم لعرض الحضور.
              TableCalendar(
                locale: 'ar_SA', // تحديد اللغة العربية للتقويم.
                firstDay: DateTime.utc(2022, 1, 1),
                lastDay: DateTime.utc(2030, 12, 31),
                focusedDay: _focusedDay,
                calendarFormat: CalendarFormat.month,
                // تحديد اليوم المختار.
                selectedDayPredicate: (day) => isSameDay(_selectedDay, day),
                // عند اختيار يوم جديد.
                onDaySelected: (selectedDay, focusedDay) {
                  if (!mounted) return;
                  setState(() {
                    _selectedDay = selectedDay;
                    _focusedDay = focusedDay;
                  });
                  // إظهار مربع حوار لتحديث حالة الحضور لليوم المختار.
                  _showUpdateStatusDialog(selectedDay);
                },
                // عند تغيير الصفحة (الشهر).
                onPageChanged: (focusedDay) {
                  _focusedDay = focusedDay;
                  // تحميل بيانات الشهر الجديد عند التنقل.
                  _loadAttendanceDataForMonth(focusedDay);
                },
                // بناء مخصص للعلامات التي تظهر تحت كل يوم لتوضيح حالة الحضور.
                calendarBuilders: CalendarBuilders(
                  markerBuilder: (context, day, events) {
                    // الحصول على حالة اليوم من البيانات المحلية.
                    final status = _attendanceData[DateTime.utc(day.year, day.month, day.day)];
                    if (status != null) {
                      // إظهار علامة ملونة بناءً على حالة الحضور.
                      return Positioned(
                        bottom: 1,
                        child: Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _getStatusColor(status),
                            shape: BoxShape.circle,
                          ),
                        ),
                      );
                    }
                    return null;
                  },
                ),
                // تصميم رأس التقويم.
                headerStyle: HeaderStyle(
                  titleCentered: true,
                  titleTextStyle: GoogleFonts.cairo(fontSize: 18, fontWeight: FontWeight.bold),
                  formatButtonVisible: false,
                ),
                // تصميم أيام التقويم.
                calendarStyle: CalendarStyle(
                  defaultTextStyle: GoogleFonts.cairo(),
                  weekendTextStyle: GoogleFonts.cairo(color: Colors.red[600]),
                  selectedTextStyle: GoogleFonts.cairo(color: Colors.white),
                  todayTextStyle: GoogleFonts.cairo(color: Colors.white),
                ),
              ),
              const Divider(),
              // قسم يوضح دلالات الألوان المستخدمة في التقويم.
              _buildLegend(),
            ],
          ),
          // عرض مؤشر الحفظ في منتصف الشاشة إذا كان `_isSaving` صحيحاً.
          if (_isSaving)
            const Center(
              child: LoadingIndicator(),
            ),
        ],
      ),
    );
  }

  /// دالة لبناء قسم دلالات الألوان.
  Widget _buildLegend() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Wrap(
        spacing: 16.0,
        runSpacing: 8.0,
        children: [
          _buildLegendItem('حاضر', Colors.green),
          _buildLegendItem('غائب', Colors.red),
          _buildLegendItem('متأخر', Colors.orange),
          _buildLegendItem('غائب بعذر', Colors.blue),
        ],
      ),
    );
  }

  /// دالة لبناء عنصر واحد في قسم دلالات الألوان.
  Widget _buildLegendItem(String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 12,
          height: 12,
          color: color,
        ),
        const SizedBox(width: 6),
        Text(text, style: GoogleFonts.cairo()),
      ],
    );
  }

  /// دالة لإظهار مربع حوار لتحديث حالة الحضور.
  void _showUpdateStatusDialog(DateTime day) {
    showDialog(
      context: context,
      builder: (context) {
        // قائمة حالات الحضور المتاحة.
        final statuses = ['حاضر', 'غائب', 'متأخر', 'غائب بعذر'];
        // الحصول على الحالة الحالية لليوم المحدد.
        String? currentStatus = _attendanceData[DateTime.utc(day.year, day.month, day.day)];

        return AlertDialog(
          title: Text('تحديث حالة يوم ${DateFormat.yMd('ar_SA').format(day)}', style: GoogleFonts.cairo()),
          content: StatefulBuilder(
            builder: (BuildContext context, StateSetter setState) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: statuses.map((status) {
                  return RadioListTile<String>(
                    title: Text(status, style: GoogleFonts.cairo()),
                    value: status,
                    groupValue: currentStatus,
                    onChanged: (String? value) {
                      // تحديث الحالة المختارة وإعادة بناء مربع الحوار.
                      setState(() {
                        currentStatus = value;
                      });
                    },
                  );
                }).toList(),
              );
            },
          ),
          actions: [
            TextButton(
              child: Text('إلغاء', style: GoogleFonts.cairo()),
              onPressed: () => Navigator.of(context).pop(),
            ),
            ElevatedButton(
              child: Text('حفظ', style: GoogleFonts.cairo()),
              onPressed: () {
                if (currentStatus != null) {
                  // إغلاق مربع الحوار أولاً.
                  Navigator.of(context).pop();
                  // ثم تحديث حالة الحضور في Firestore.
                  _updateAttendance(day, currentStatus!);
                }
              },
            ),
          ],
        );
      },
    );
  }

  /// دالة لتحديد لون العلامة بناءً على حالة الحضور.
  Color _getStatusColor(String status) {
    switch (status) {
      case 'حاضر':
        return Colors.green;
      case 'غائب':
        return Colors.red;
      case 'متأخر':
        return Colors.orange;
      case 'غائب بعذر':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
