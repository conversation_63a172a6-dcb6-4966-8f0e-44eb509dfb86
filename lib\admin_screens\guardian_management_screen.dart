import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/providers/guardian_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/admin_screens/widgets/guardian_form_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/link_students_dialog.dart';

/// شاشة إدارة أولياء الأمور المعاد هيكلتها.
class GuardianManagementScreen extends ConsumerWidget {
  const GuardianManagementScreen({super.key});

  /// دالة لعرض ديالوج تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    GuardianModel guardian,
  ) async {
    final confirmed = await showConfirmationDialog(
      context: context,
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد أنك تريد حذف ولي الأمر "${guardian.name}"؟ سيؤدي هذا إلى حذف حسابه.',
    );

    if (confirmed) {
      try {
        await ref.read(firebaseServiceProvider).deleteGuardian(guardian.id);
        showSuccessSnackBar(context, 'تم حذف ولي الأمر بنجاح.');
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final guardiansAsyncValue = ref.watch(guardiansStreamProvider);
    final filteredGuardians = ref.watch(filteredGuardiansProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة أولياء الأمور'),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: (value) {
                ref.read(guardianSearchQueryProvider.notifier).state = value;
              },
              decoration: const InputDecoration(
                labelText: 'ابحث بالاسم أو البريد الإلكتروني...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),
          Expanded(
            child: guardiansAsyncValue.when(
              data: (_) {
                if (filteredGuardians.isEmpty) {
                  return const Center(
                      child: Text('لم يتم العثور على نتائج.'));
                }
                return LayoutBuilder(builder: (context, constraints) {
                  return SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: DataTable(
                      columns: const [
                        DataColumn(label: Text('الاسم')),
                        DataColumn(label: Text('البريد الإلكتروني')),
                        DataColumn(label: Text('رقم الهاتف')),
                        DataColumn(label: Text('الطلاب المرتبطون')),
                        DataColumn(label: Text('إجراءات')),
                      ],
                      rows: filteredGuardians.map((guardian) {
                        return DataRow(cells: [
                          DataCell(Text(guardian.name)),
                          DataCell(Text(guardian.email)),
                          DataCell(Text(guardian.phoneNumber)),
                          DataCell(
                              Text(guardian.linkedStudents.length.toString())),
                          DataCell(Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              IconButton(
                                  icon: const Icon(Icons.link),
                                  tooltip: 'ربط الطلاب',
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (_) =>
                                          LinkStudentsDialog(guardian: guardian),
                                    );
                                  }),
                              IconButton(
                                  icon: const Icon(Icons.edit),
                                  onPressed: () {
                                    showDialog(
                                      context: context,
                                      builder: (_) =>
                                          GuardianFormDialog(guardian: guardian),
                                    );
                                  }),
                              IconButton(
                                  icon: const Icon(Icons.delete,
                                      color: Colors.red),
                                  onPressed: () =>
                                      _showDeleteConfirmationDialog(
                                          context, ref, guardian)),
                            ],
                          )),
                        ]);
                      }).toList(),
                    ),
                  );
                });
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
          onPressed: () {
            showDialog(
              context: context,
              builder: (_) => const GuardianFormDialog(),
            );
          },
          tooltip: 'إضافة ولي أمر',
          child: const Icon(Icons.add)),
    );
  }
}
