import 'dart:convert';
import 'dart:typed_data';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/lesson_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/widgets/shared_widgets/class_dropdown.dart';
import 'package:school_management_system/widgets/shared_widgets/subject_dropdown.dart';

class LessonFormDialog extends ConsumerStatefulWidget {
  final LessonModel? lesson;

  const LessonFormDialog({super.key, this.lesson});

  @override
  ConsumerState<LessonFormDialog> createState() => _LessonFormDialogState();
}

class _LessonFormDialogState extends ConsumerState<LessonFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _videoUrlController;
  late quill.QuillController _descriptionController;
  String? _selectedSubject;
  String? _selectedClassId;
  final List<XFile> _newImages = [];
  late List<String> _existingImageUrls;

  @override
  void initState() {
    super.initState();
    final lesson = widget.lesson;
    _titleController = TextEditingController(text: lesson?.title);
    _videoUrlController = TextEditingController(text: lesson?.videoUrl);
    _selectedSubject = lesson?.subject;
    _selectedClassId = lesson?.classId;
    _existingImageUrls = List.from(lesson?.imageUrls ?? []);

    try {
      if (lesson != null && lesson.description.isNotEmpty) {
        final descriptionJson = jsonDecode(lesson.description);
        _descriptionController = quill.QuillController(
          document: quill.Document.fromJson(descriptionJson),
          selection: const TextSelection.collapsed(offset: 0),
        );
      } else {
        _descriptionController = quill.QuillController.basic();
      }
    } catch (e) {
      _descriptionController = quill.QuillController(
        document: quill.Document()..insert(0, lesson?.description ?? ''),
        selection: const TextSelection.collapsed(offset: 0),
      );
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _videoUrlController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate() && _selectedSubject != null) {
      showDialog(
        context: context,
        builder: (_) => const Center(child: CircularProgressIndicator()),
        barrierDismissible: false,
      );

      final firebaseService = ref.read(firebaseServiceProvider);
      final isEditing = widget.lesson != null;

      try {
        // Handle image uploads and deletions
        final finalImageUrls = await _manageImages();

        final descriptionJson =
            jsonEncode(_descriptionController.document.toDelta().toJson());

        final lessonData = {
          'title': _titleController.text,
          'subject': _selectedSubject,
          'description': descriptionJson,
          'classId': _selectedClassId,
          'videoUrl': _videoUrlController.text,
          'imageUrls': finalImageUrls,
          'attachments': [], // Placeholder for future implementation
        };

        if (isEditing) {
          await firebaseService.updateDocument(
              'lessons', widget.lesson!.id, lessonData);
        } else {
          lessonData['createdAt'] = FieldValue.serverTimestamp();
          await firebaseService.addDocument('lessons', lessonData);
        }

        Navigator.of(context).pop(); // Close loading indicator
        Navigator.of(context).pop(); // Close dialog
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(isEditing
                ? 'تم تحديث الدرس بنجاح'
                : 'تم إضافة الدرس بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      } catch (e) {
        Navigator.of(context).pop(); // Close loading indicator
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<List<String>> _manageImages() async {
    final firebaseService = ref.read(firebaseServiceProvider);
    final isEditing = widget.lesson != null;
    List<String> finalImageUrls = List.from(_existingImageUrls);

    if (isEditing) {
      final originalUrls = Set<String>.from(widget.lesson!.imageUrls);
      final currentUrls = Set<String>.from(_existingImageUrls);
      final urlsToDelete = originalUrls.difference(currentUrls);
      for (final url in urlsToDelete) {
        await firebaseService.deleteImage(url);
      }
    }

    if (_newImages.isNotEmpty) {
      final newUploadedUrls = await firebaseService.uploadMultipleImages(
          _newImages, 'lesson_images');
      finalImageUrls.addAll(newUploadedUrls);
    }

    return finalImageUrls;
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.lesson == null ? 'إضافة درس جديد' : 'تعديل الدرس'),
      content: Form(
        key: _formKey,
        child: SizedBox(
          width: double.maxFinite,
          height: MediaQuery.of(context).size.height * 0.7,
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(labelText: 'عنوان الدرس'),
                  validator: (v) => v!.isEmpty ? 'العنوان مطلوب' : null,
                ),
                const SizedBox(height: 16),
                ClassDropdown(
                  selectedValue: _selectedClassId,
                  onChanged: (newValue) {
                    setState(() {
                      _selectedClassId = newValue;
                      // Reset subject when class changes
                      _selectedSubject = null;
                    });
                  },
                ),
                const SizedBox(height: 16),
                SubjectDropdown(
                  selectedValue: _selectedSubject,
                  onChanged: (newValue) {
                    setState(() {
                      _selectedSubject = newValue;
                    });
                  },
                  // Pass the selected class ID to filter subjects
                  classId: _selectedClassId,
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _videoUrlController,
                  decoration: const InputDecoration(
                    labelText: 'رابط فيديو (اختياري)',
                  ),
                ),
                const SizedBox(height: 16),
                const Text('شرح الدرس',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    children: [
                      quill.QuillSimpleToolbar(
                        controller: _descriptionController,
                        config: const quill.QuillSimpleToolbarConfig(
                            showAlignmentButtons: true),
                      ),
                      const Divider(height: 1),
                      SizedBox(
                        height: 250,
                        child: quill.QuillEditor.basic(
                          controller: _descriptionController,
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(height: 20),
                const Text('صور توضيحية',
                    style: TextStyle(fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                _buildImageManager(),
              ],
            ),
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: Text(widget.lesson == null ? 'إضافة' : 'حفظ التعديلات'),
        ),
      ],
    );
  }

  Widget _buildImageManager() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_existingImageUrls.isEmpty && _newImages.isEmpty)
          const Center(child: Text('لم يتم اختيار صور بعد.')),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 100,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: _existingImageUrls.length + _newImages.length,
          itemBuilder: (context, index) {
            Widget imageWidget;
            bool isExistingUrl = index < _existingImageUrls.length;

            if (isExistingUrl) {
              imageWidget = Image.network(_existingImageUrls[index], fit: BoxFit.cover);
            } else {
              final fileIndex = index - _existingImageUrls.length;
              imageWidget = FutureBuilder<Uint8List>(
                future: _newImages[fileIndex].readAsBytes(),
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return Image.memory(snapshot.data!, fit: BoxFit.cover);
                  } else {
                    return const Center(child: CircularProgressIndicator());
                  }
                },
              );
            }

            return Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: imageWidget,
                ),
                Positioned(
                  top: -8,
                  right: -8,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: Colors.red,
                      radius: 12,
                      child: Icon(Icons.close, color: Colors.white, size: 14),
                    ),
                    onPressed: () {
                      setState(() {
                        if (isExistingUrl) {
                          _existingImageUrls.removeAt(index);
                        } else {
                          _newImages.removeAt(index - _existingImageUrls.length);
                        }
                      });
                    },
                  ),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 16),
        Center(
          child: ElevatedButton.icon(
            icon: const Icon(Icons.add_a_photo),
            label: const Text('اختيار صور'),
            onPressed: () async {
              final pickedImages =
                  await ref.read(firebaseServiceProvider).pickMultipleImages();
              if (pickedImages.isNotEmpty) {
                setState(() {
                  _newImages.addAll(pickedImages);
                });
              }
            },
          ),
        ),
      ],
    );
  }
}
