{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-ca5f2cfb3fbc02ea4e00.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-f792c6f327d4008efc3b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-38853258ad9e8e8d3259.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-f792c6f327d4008efc3b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-38853258ad9e8e8d3259.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-ca5f2cfb3fbc02ea4e00.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}