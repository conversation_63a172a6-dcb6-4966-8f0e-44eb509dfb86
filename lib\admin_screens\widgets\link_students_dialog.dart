import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/guardian_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// ديالوج لربط الطلاب بولي أمر معين.
class LinkStudentsDialog extends ConsumerStatefulWidget {
  final GuardianModel guardian;
  const LinkStudentsDialog({super.key, required this.guardian});

  @override
  ConsumerState<LinkStudentsDialog> createState() => _LinkStudentsDialogState();
}

class _LinkStudentsDialogState extends ConsumerState<LinkStudentsDialog> {
  late Set<String> _selectedStudentUids;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _selectedStudentUids = widget.guardian.linkedStudents.toSet();
  }

  Future<void> _saveLinks() async {
    try {
      await ref
          .read(firebaseServiceProvider)
          .linkStudentsToGuardian(widget.guardian.id, _selectedStudentUids.toList());
      showSuccessSnackBar(context, 'تم تحديث قائمة الطلاب بنجاح');
      Navigator.of(context).pop();
    } catch (e) {
      showErrorSnackBar(context, 'فشل الحفظ: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final allStudentsAsync = ref.watch(allStudentsProvider);

    return AlertDialog(
      title: Text('ربط الطلاب بـ ${widget.guardian.name}'),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: TextField(
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value.toLowerCase();
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'ابحث عن طالب...',
                  prefixIcon: Icon(Icons.search),
                  border: OutlineInputBorder(),
                ),
              ),
            ),
            Expanded(
              child: allStudentsAsync.when(
                data: (allStudents) {
                  final filteredStudents = allStudents.where((student) {
                    return student.name.toLowerCase().contains(_searchQuery) ||
                        student.studentNumber.toLowerCase().contains(_searchQuery);
                  }).toList();

                  return ListView.builder(
                    itemCount: filteredStudents.length,
                    itemBuilder: (context, index) {
                      final student = filteredStudents[index];
                      final isSelected = _selectedStudentUids.contains(student.id);
                      return CheckboxListTile(
                        title: Text(student.name),
                        subtitle: Text(student.studentNumber),
                        value: isSelected,
                        onChanged: (bool? value) {
                          setState(() {
                            if (value == true) {
                              _selectedStudentUids.add(student.id);
                            } else {
                              _selectedStudentUids.remove(student.id);
                            }
                          });
                        },
                      );
                    },
                  );
                },
                loading: () => const LoadingIndicator(),
                error: (err, stack) => ErrorMessage(message: err.toString()),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء')),
        ElevatedButton(onPressed: _saveLinks, child: const Text('حفظ')),
      ],
    );
  }
}
