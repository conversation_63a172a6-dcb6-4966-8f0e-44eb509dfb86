import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج إحصائيات مادة واحدة في الامتحان
class SubjectAnalytics {
  final String subjectId;
  final String subjectName;
  final int totalStudents;        // إجمالي عدد الطلاب
  final int presentStudents;      // عدد الطلاب الحاضرين
  final int absentStudents;       // عدد الطلاب الغائبين
  final double averageGrade;      // متوسط الدرجات
  final double highestGrade;      // أعلى درجة
  final double lowestGrade;       // أقل درجة
  final int passCount;            // عدد الناجحين
  final int failCount;            // عدد الراسبين
  final double passPercentage;    // نسبة النجاح
  final double standardDeviation; // الانحراف المعياري
  final Map<String, int> gradeDistribution; // توزيع الدرجات (نطاقات)
  final List<TopStudent> topStudents; // أفضل الطلاب
  final List<String> strugglingStudents; // الطلاب المتعثرين

  const SubjectAnalytics({
    required this.subjectId,
    required this.subjectName,
    required this.totalStudents,
    required this.presentStudents,
    required this.absentStudents,
    required this.averageGrade,
    required this.highestGrade,
    required this.lowestGrade,
    required this.passCount,
    required this.failCount,
    required this.passPercentage,
    required this.standardDeviation,
    required this.gradeDistribution,
    required this.topStudents,
    required this.strugglingStudents,
  });

  /// إنشاء إحصائيات مادة من Map
  factory SubjectAnalytics.fromMap(Map<String, dynamic> data) {
    return SubjectAnalytics(
      subjectId: data['subjectId'] as String? ?? '',
      subjectName: data['subjectName'] as String? ?? '',
      totalStudents: data['totalStudents'] as int? ?? 0,
      presentStudents: data['presentStudents'] as int? ?? 0,
      absentStudents: data['absentStudents'] as int? ?? 0,
      averageGrade: (data['averageGrade'] as num?)?.toDouble() ?? 0.0,
      highestGrade: (data['highestGrade'] as num?)?.toDouble() ?? 0.0,
      lowestGrade: (data['lowestGrade'] as num?)?.toDouble() ?? 0.0,
      passCount: data['passCount'] as int? ?? 0,
      failCount: data['failCount'] as int? ?? 0,
      passPercentage: (data['passPercentage'] as num?)?.toDouble() ?? 0.0,
      standardDeviation: (data['standardDeviation'] as num?)?.toDouble() ?? 0.0,
      gradeDistribution: Map<String, int>.from(data['gradeDistribution'] ?? {}),
      topStudents: (data['topStudents'] as List<dynamic>?)
          ?.map((s) => TopStudent.fromMap(s as Map<String, dynamic>))
          .toList() ?? [],
      strugglingStudents: List<String>.from(data['strugglingStudents'] ?? []),
    );
  }

  /// تحويل إحصائيات المادة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'subjectId': subjectId,
      'subjectName': subjectName,
      'totalStudents': totalStudents,
      'presentStudents': presentStudents,
      'absentStudents': absentStudents,
      'averageGrade': averageGrade,
      'highestGrade': highestGrade,
      'lowestGrade': lowestGrade,
      'passCount': passCount,
      'failCount': failCount,
      'passPercentage': passPercentage,
      'standardDeviation': standardDeviation,
      'gradeDistribution': gradeDistribution,
      'topStudents': topStudents.map((s) => s.toMap()).toList(),
      'strugglingStudents': strugglingStudents,
    };
  }

  /// حساب معدل الحضور
  double get attendanceRate {
    if (totalStudents == 0) return 0.0;
    return (presentStudents / totalStudents) * 100;
  }

  /// تقييم أداء المادة
  String get performanceLevel {
    if (passPercentage >= 90) return 'ممتاز';
    if (passPercentage >= 80) return 'جيد جداً';
    if (passPercentage >= 70) return 'جيد';
    if (passPercentage >= 60) return 'مقبول';
    return 'ضعيف';
  }

  @override
  String toString() {
    return 'SubjectAnalytics(subject: $subjectName, average: $averageGrade, pass%: $passPercentage)';
  }
}

/// نموذج إحصائيات صف واحد في الامتحان
class ClassAnalytics {
  final String classId;
  final String className;
  final int totalStudents;        // إجمالي عدد الطلاب
  final double overallAverage;    // المتوسط العام لجميع المواد
  final Map<String, SubjectAnalytics> subjectAnalytics; // إحصائيات كل مادة
  final List<TopStudent> topStudents; // أفضل الطلاب في الصف
  final int totalPassCount;       // إجمالي الناجحين
  final int totalFailCount;       // إجمالي الراسبين
  final double overallPassPercentage; // نسبة النجاح الإجمالية

  const ClassAnalytics({
    required this.classId,
    required this.className,
    required this.totalStudents,
    required this.overallAverage,
    required this.subjectAnalytics,
    required this.topStudents,
    required this.totalPassCount,
    required this.totalFailCount,
    required this.overallPassPercentage,
  });

  /// إنشاء إحصائيات صف من Map
  factory ClassAnalytics.fromMap(Map<String, dynamic> data) {
    return ClassAnalytics(
      classId: data['classId'] as String? ?? '',
      className: data['className'] as String? ?? '',
      totalStudents: data['totalStudents'] as int? ?? 0,
      overallAverage: (data['overallAverage'] as num?)?.toDouble() ?? 0.0,
      subjectAnalytics: (data['subjectAnalytics'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
              key, SubjectAnalytics.fromMap(value as Map<String, dynamic>))) ?? {},
      topStudents: (data['topStudents'] as List<dynamic>?)
          ?.map((s) => TopStudent.fromMap(s as Map<String, dynamic>))
          .toList() ?? [],
      totalPassCount: data['totalPassCount'] as int? ?? 0,
      totalFailCount: data['totalFailCount'] as int? ?? 0,
      overallPassPercentage: (data['overallPassPercentage'] as num?)?.toDouble() ?? 0.0,
    );
  }

  /// تحويل إحصائيات الصف إلى Map
  Map<String, dynamic> toMap() {
    return {
      'classId': classId,
      'className': className,
      'totalStudents': totalStudents,
      'overallAverage': overallAverage,
      'subjectAnalytics': subjectAnalytics.map((key, value) => MapEntry(key, value.toMap())),
      'topStudents': topStudents.map((s) => s.toMap()).toList(),
      'totalPassCount': totalPassCount,
      'totalFailCount': totalFailCount,
      'overallPassPercentage': overallPassPercentage,
    };
  }

  /// أفضل مادة في الصف
  SubjectAnalytics? get bestSubject {
    if (subjectAnalytics.isEmpty) return null;
    return subjectAnalytics.values.reduce((a, b) => 
        a.averageGrade > b.averageGrade ? a : b);
  }

  /// أضعف مادة في الصف
  SubjectAnalytics? get weakestSubject {
    if (subjectAnalytics.isEmpty) return null;
    return subjectAnalytics.values.reduce((a, b) => 
        a.averageGrade < b.averageGrade ? a : b);
  }

  @override
  String toString() {
    return 'ClassAnalytics(class: $className, average: $overallAverage, pass%: $overallPassPercentage)';
  }
}

/// نموذج طالب متفوق
class TopStudent {
  final String studentId;
  final String studentName;
  final String studentNumber;
  final double totalGrade;
  final double percentage;
  final int rank;                 // الترتيب
  final String? profileImageUrl;

  const TopStudent({
    required this.studentId,
    required this.studentName,
    required this.studentNumber,
    required this.totalGrade,
    required this.percentage,
    required this.rank,
    this.profileImageUrl,
  });

  /// إنشاء طالب متفوق من Map
  factory TopStudent.fromMap(Map<String, dynamic> data) {
    return TopStudent(
      studentId: data['studentId'] as String? ?? '',
      studentName: data['studentName'] as String? ?? '',
      studentNumber: data['studentNumber'] as String? ?? '',
      totalGrade: (data['totalGrade'] as num?)?.toDouble() ?? 0.0,
      percentage: (data['percentage'] as num?)?.toDouble() ?? 0.0,
      rank: data['rank'] as int? ?? 0,
      profileImageUrl: data['profileImageUrl'] as String?,
    );
  }

  /// تحويل الطالب المتفوق إلى Map
  Map<String, dynamic> toMap() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'studentNumber': studentNumber,
      'totalGrade': totalGrade,
      'percentage': percentage,
      'rank': rank,
      'profileImageUrl': profileImageUrl,
    };
  }

  @override
  String toString() {
    return 'TopStudent(name: $studentName, grade: $totalGrade, rank: $rank)';
  }
}

/// نموذج الإحصائيات الشاملة للامتحان
class ExamAnalytics {
  final String id;
  final String examId;            // معرف الامتحان
  final String examName;          // اسم الامتحان
  final String academicYear;      // السنة الدراسية
  final String semester;          // الفصل الدراسي
  final Map<String, ClassAnalytics> classAnalytics; // إحصائيات كل صف
  final Map<String, SubjectAnalytics> overallSubjectAnalytics; // إحصائيات المواد عموماً
  final OverallStats overallStats; // الإحصائيات العامة
  final List<TopStudent> schoolTopStudents; // أفضل الطلاب في المدرسة
  final DateTime generatedAt;     // تاريخ إنشاء التحليل
  final String generatedBy;       // من أنشأ التحليل

  const ExamAnalytics({
    required this.id,
    required this.examId,
    required this.examName,
    required this.academicYear,
    required this.semester,
    required this.classAnalytics,
    required this.overallSubjectAnalytics,
    required this.overallStats,
    required this.schoolTopStudents,
    required this.generatedAt,
    required this.generatedBy,
  });

  /// إنشاء تحليلات امتحان من مستند Firestore
  factory ExamAnalytics.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ExamAnalytics(
      id: doc.id,
      examId: data['examId'] as String? ?? '',
      examName: data['examName'] as String? ?? '',
      academicYear: data['academicYear'] as String? ?? '',
      semester: data['semester'] as String? ?? '',
      classAnalytics: (data['classAnalytics'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
              key, ClassAnalytics.fromMap(value as Map<String, dynamic>))) ?? {},
      overallSubjectAnalytics: (data['overallSubjectAnalytics'] as Map<String, dynamic>?)
          ?.map((key, value) => MapEntry(
              key, SubjectAnalytics.fromMap(value as Map<String, dynamic>))) ?? {},
      overallStats: OverallStats.fromMap(data['overallStats'] as Map<String, dynamic>? ?? {}),
      schoolTopStudents: (data['schoolTopStudents'] as List<dynamic>?)
          ?.map((s) => TopStudent.fromMap(s as Map<String, dynamic>))
          .toList() ?? [],
      generatedAt: (data['generatedAt'] as Timestamp).toDate(),
      generatedBy: data['generatedBy'] as String? ?? '',
    );
  }

  /// تحويل تحليلات الامتحان إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'examId': examId,
      'examName': examName,
      'academicYear': academicYear,
      'semester': semester,
      'classAnalytics': classAnalytics.map((key, value) => MapEntry(key, value.toMap())),
      'overallSubjectAnalytics': overallSubjectAnalytics.map((key, value) => MapEntry(key, value.toMap())),
      'overallStats': overallStats.toMap(),
      'schoolTopStudents': schoolTopStudents.map((s) => s.toMap()).toList(),
      'generatedAt': Timestamp.fromDate(generatedAt),
      'generatedBy': generatedBy,
    };
  }

  /// أفضل صف في المدرسة
  ClassAnalytics? get bestClass {
    if (classAnalytics.isEmpty) return null;
    return classAnalytics.values.reduce((a, b) => 
        a.overallAverage > b.overallAverage ? a : b);
  }

  /// أضعف صف في المدرسة
  ClassAnalytics? get weakestClass {
    if (classAnalytics.isEmpty) return null;
    return classAnalytics.values.reduce((a, b) => 
        a.overallAverage < b.overallAverage ? a : b);
  }

  @override
  String toString() {
    return 'ExamAnalytics(exam: $examName, classes: ${classAnalytics.length}, overall: ${overallStats.overallAverage})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExamAnalytics && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج الإحصائيات العامة للامتحان
class OverallStats {
  final int totalStudents;        // إجمالي عدد الطلاب
  final int totalPresentStudents; // إجمالي الطلاب الحاضرين
  final int totalAbsentStudents;  // إجمالي الطلاب الغائبين
  final double overallAverage;    // المتوسط العام
  final double overallPassPercentage; // نسبة النجاح العامة
  final double attendanceRate;    // معدل الحضور
  final int totalSubjects;        // عدد المواد
  final int totalClasses;         // عدد الصفوف

  const OverallStats({
    required this.totalStudents,
    required this.totalPresentStudents,
    required this.totalAbsentStudents,
    required this.overallAverage,
    required this.overallPassPercentage,
    required this.attendanceRate,
    required this.totalSubjects,
    required this.totalClasses,
  });

  /// إنشاء إحصائيات عامة من Map
  factory OverallStats.fromMap(Map<String, dynamic> data) {
    return OverallStats(
      totalStudents: data['totalStudents'] as int? ?? 0,
      totalPresentStudents: data['totalPresentStudents'] as int? ?? 0,
      totalAbsentStudents: data['totalAbsentStudents'] as int? ?? 0,
      overallAverage: (data['overallAverage'] as num?)?.toDouble() ?? 0.0,
      overallPassPercentage: (data['overallPassPercentage'] as num?)?.toDouble() ?? 0.0,
      attendanceRate: (data['attendanceRate'] as num?)?.toDouble() ?? 0.0,
      totalSubjects: data['totalSubjects'] as int? ?? 0,
      totalClasses: data['totalClasses'] as int? ?? 0,
    );
  }

  /// تحويل الإحصائيات العامة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'totalStudents': totalStudents,
      'totalPresentStudents': totalPresentStudents,
      'totalAbsentStudents': totalAbsentStudents,
      'overallAverage': overallAverage,
      'overallPassPercentage': overallPassPercentage,
      'attendanceRate': attendanceRate,
      'totalSubjects': totalSubjects,
      'totalClasses': totalClasses,
    };
  }

  /// تقييم الأداء العام
  String get overallPerformance {
    if (overallPassPercentage >= 90) return 'ممتاز';
    if (overallPassPercentage >= 80) return 'جيد جداً';
    if (overallPassPercentage >= 70) return 'جيد';
    if (overallPassPercentage >= 60) return 'مقبول';
    return 'ضعيف';
  }

  @override
  String toString() {
    return 'OverallStats(students: $totalStudents, average: $overallAverage, pass%: $overallPassPercentage)';
  }
}
