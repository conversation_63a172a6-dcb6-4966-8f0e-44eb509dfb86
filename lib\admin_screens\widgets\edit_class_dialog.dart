import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/teacher_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/providers/teacher_providers.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class EditClassDialog extends ConsumerStatefulWidget {
  final ClassModel classModel;

  const EditClassDialog({super.key, required this.classModel});

  @override
  ConsumerState<EditClassDialog> createState() => _EditClassDialogState();
}

class _EditClassDialogState extends ConsumerState<EditClassDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _classNameController;
  String? _selectedTeacherId;

  @override
  void initState() {
    super.initState();
    _classNameController = TextEditingController(text: widget.classModel.name);
    // TODO: يجب جلب المعلم الحالي للفصل وتعيينه كقيمة افتراضية
    // _selectedTeacherId = widget.classModel.teacherId;
  }

  @override
  void dispose() {
    _classNameController.dispose();
    super.dispose();
  }

  void _submitForm() async {
    if (_formKey.currentState!.validate()) {
      // بناء خريطة البيانات للتحديث
      final dataToUpdate = {
        'name': _classNameController.text,
        'teacherId': _selectedTeacherId,
      };

      try {
        await ref
            .read(firebaseServiceProvider)
            .updateClass(widget.classModel.id, dataToUpdate);
        if (mounted) {
          Navigator.of(context).pop();
          showSuccessSnackBar(context, 'تم تحديث الفصل بنجاح');
        }
      } catch (e) {
        if (mounted) {
          showErrorSnackBar(context, 'حدث خطأ: $e');
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تعديل بيانات الفصل'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _classNameController,
              decoration: const InputDecoration(labelText: 'اسم الفصل'),
              validator:
                  (value) => value!.isEmpty ? 'الرجاء إدخال اسم الفصل' : null,
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final teachersAsyncValue = ref.watch(teachersStreamProvider);
                return teachersAsyncValue.when(
                  data: (teachers) {
                    return DropdownButtonFormField<String>(
                      value: _selectedTeacherId,
                      decoration: const InputDecoration(
                        labelText: 'المعلم المسؤول',
                      ),
                      items:
                          teachers.map((teacher) {
                            return DropdownMenuItem(
                              value: teacher.id,
                              child: Text(teacher.name),
                            );
                          }).toList(),
                      onChanged:
                          (value) => setState(() => _selectedTeacherId = value),
                      validator:
                          (value) =>
                              value == null ? 'الرجاء اختيار معلم' : null,
                    );
                  },
                  loading: () => const CircularProgressIndicator(),
                  error: (err, stack) => Text('خطأ: $err'),
                );
              },
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(onPressed: _submitForm, child: const Text('حفظ')),
      ],
    );
  }
}
