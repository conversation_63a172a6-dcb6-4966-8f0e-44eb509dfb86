import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// حالة شاشة استعادة كلمة المرور.
///
/// نستخدم freezed هنا لتسهيل إنشاء نسخ من الحالة (immutable).
class ForgotPasswordState {
  final bool isLoading;
  final String message;
  final bool isSuccess;

  ForgotPasswordState({
    this.isLoading = false,
    this.message = '',
    this.isSuccess = false,
  });

  ForgotPasswordState copyWith({
    bool? isLoading,
    String? message,
    bool? isSuccess,
  }) {
    return ForgotPasswordState(
      isLoading: isLoading ?? this.isLoading,
      message: message ?? this.message,
      isSuccess: isSuccess ?? this.isSuccess,
    );
  }
}

/// Controller (أو ViewModel) لإدارة منطق شاشة استعادة كلمة المرور.
class ForgotPasswordController extends StateNotifier<ForgotPasswordState> {
  final Ref _ref;

  ForgotPasswordController(this._ref) : super(ForgotPasswordState());

  /// دالة لإرسال رابط إعادة تعيين كلمة المرور.
  Future<void> sendPasswordResetEmail(String email) async {
    if (email.isEmpty || !email.contains('@')) {
      state = state.copyWith(message: 'الرجاء إدخال بريد إلكتروني صحيح.', isSuccess: false);
      return;
    }

    state = state.copyWith(isLoading: true, message: '');

    try {
      // استدعاء الدالة من طبقة الخدمات
      await _ref.read(firebaseServiceProvider).sendPasswordResetEmail(email.trim());
      state = state.copyWith(
        isLoading: false,
        isSuccess: true,
        message: 'تم إرسال رابط إعادة التعيين إلى بريدك الإلكتروني. يرجى التحقق منه.',
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        isSuccess: false,
        message: 'حدث خطأ: ${e.toString()}',
      );
    }
  }
}

/// Provider لـ ForgotPasswordController.
final forgotPasswordControllerProvider =
    StateNotifierProvider.autoDispose<ForgotPasswordController, ForgotPasswordState>((ref) {
  return ForgotPasswordController(ref);
});

// --- Login Screen ---

/// حالة شاشة تسجيل الدخول.
class LoginState {
  final bool isLoading;
  final String? error;

  LoginState({this.isLoading = false, this.error});

  LoginState copyWith({bool? isLoading, String? error}) {
    return LoginState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Controller لإدارة منطق شاشة تسجيل الدخول.
class LoginController extends StateNotifier<LoginState> {
  final Ref _ref;

  LoginController(this._ref) : super(LoginState());

  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    try {
      final user = await _ref
          .read(firebaseServiceProvider)
          .signInWithEmailAndPassword(email, password);
      if (user != null) {
        state = state.copyWith(isLoading: false);
        return true;
      } else {
        state = state.copyWith(isLoading: false, error: 'فشل تسجيل الدخول. يرجى التحقق من البيانات.');
        return false;
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, error: 'حدث خطأ: ${e.toString()}');
      return false;
    }
  }
}

/// Provider لـ LoginController.
final loginControllerProvider = StateNotifierProvider.autoDispose<LoginController, LoginState>((ref) {
  return LoginController(ref);
});
