import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

//======================================================================
// Providers for Admin Student Management Screen
//======================================================================

// Note: studentServiceProvider is removed as FirebaseService is now used directly.

final studentsStreamProvider = StreamProvider.autoDispose<List<StudentModel>>((ref) {
  // This now correctly points to the stream from the unified service.
  return ref.watch(firebaseServiceProvider).getAllStudentsStream();
});

final studentSearchQueryProvider = StateProvider.autoDispose<String>((ref) => '');

final filteredStudentsProvider = Provider.autoDispose<List<StudentModel>>((ref) {
  final students = ref.watch(studentsStreamProvider).asData?.value ?? [];
  final searchQuery = ref.watch(studentSearchQueryProvider).toLowerCase();

  if (searchQuery.isEmpty) {
    return students;
  }

  return students.where((student) {
    return student.name.toLowerCase().contains(searchQuery) ||
           (student.id.toLowerCase().contains(searchQuery));
  }).toList();
});


//======================================================================
// Providers for Student Mobile Screens
//======================================================================

// Provider to fetch the main dashboard data for a student
final studentDashboardProvider = FutureProvider.autoDispose.family<Map<String, dynamic>, String>((ref, studentId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getStudentDashboardData(studentId);
});

// Provider to fetch a student's grades
final studentGradesProvider = StreamProvider.autoDispose.family<List<Map<String, dynamic>>, String>((ref, studentId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getStudentGrades(studentId);
});

/// Provider لجلب بيانات طالب معين باستخدام معرفه
final currentStudentProvider =
    StreamProvider.autoDispose.family<StudentModel?, String>((ref, studentId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getStudentById(studentId);
});
