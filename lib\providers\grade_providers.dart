import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/grade_entry_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

//======================================================================
// Providers for Grade Management
//======================================================================

/// مزود تدفق جميع إدخالات الدرجات
final allGradeEntriesStreamProvider = StreamProvider.autoDispose<List<GradeEntry>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getAllGradeEntriesStream();
});

/// مزود تدفق إدخالات الدرجات حسب الامتحان
final gradeEntriesByExamProvider = StreamProvider.autoDispose.family<List<GradeEntry>, String>((ref, examId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getGradeEntriesByExam(examId);
});

/// مزود تدفق إدخالات الدرجات حسب المعلم
final gradeEntriesByTeacherProvider = StreamProvider.autoDispose.family<List<GradeEntry>, String>((ref, teacherId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getGradeEntriesByTeacher(teacherId);
});

/// مزود إدخال درجات واحد بمعرفه
final gradeEntryByIdProvider = FutureProvider.autoDispose.family<GradeEntry?, String>((ref, gradeEntryId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getGradeEntryById(gradeEntryId);
});

/// مزود حالة إنشاء إدخال درجات جديد
final gradeEntryCreationStateProvider = StateNotifierProvider.autoDispose<GradeEntryCreationNotifier, GradeEntryCreationState>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return GradeEntryCreationNotifier(firebaseService);
});

/// مزود حالة تحديث إدخال الدرجات
final gradeEntryUpdateStateProvider = StateNotifierProvider.autoDispose<GradeEntryUpdateNotifier, GradeEntryUpdateState>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return GradeEntryUpdateNotifier(firebaseService);
});

/// مزود البحث في إدخالات الدرجات
final gradeEntrySearchQueryProvider = StateProvider.autoDispose<String>((ref) => '');

/// مزود تصفية إدخالات الدرجات حسب الحالة
final gradeEntryStatusFilterProvider = StateProvider.autoDispose<GradeEntryStatus?>((ref) => null);

/// مزود إدخالات الدرجات المفلترة
final filteredGradeEntriesProvider = Provider.autoDispose<List<GradeEntry>>((ref) {
  final gradeEntries = ref.watch(allGradeEntriesStreamProvider).asData?.value ?? [];
  final searchQuery = ref.watch(gradeEntrySearchQueryProvider).toLowerCase();
  final statusFilter = ref.watch(gradeEntryStatusFilterProvider);

  var filtered = gradeEntries;

  // تطبيق تصفية البحث
  if (searchQuery.isNotEmpty) {
    filtered = filtered.where((entry) {
      return entry.subjectName.toLowerCase().contains(searchQuery) ||
             entry.className.toLowerCase().contains(searchQuery) ||
             entry.teacherName.toLowerCase().contains(searchQuery);
    }).toList();
  }

  // تطبيق تصفية الحالة
  if (statusFilter != null) {
    filtered = filtered.where((entry) => entry.status == statusFilter).toList();
  }

  return filtered;
});

/// مزود إحصائيات إدخال الدرجات
final gradeEntryStatsProvider = Provider.autoDispose<GradeEntryStats>((ref) {
  final allEntries = ref.watch(allGradeEntriesStreamProvider).asData?.value ?? [];

  final draftCount = allEntries.where((entry) => entry.status == GradeEntryStatus.draft).length;
  final submittedCount = allEntries.where((entry) => entry.status == GradeEntryStatus.submitted).length;
  final approvedCount = allEntries.where((entry) => entry.status == GradeEntryStatus.approved).length;
  final rejectedCount = allEntries.where((entry) => entry.status == GradeEntryStatus.rejected).length;

  return GradeEntryStats(
    totalEntries: allEntries.length,
    draftEntries: draftCount,
    submittedEntries: submittedCount,
    approvedEntries: approvedCount,
    rejectedEntries: rejectedCount,
  );
});

/// مزود إدخالات الدرجات التي تحتاج مراجعة
final pendingGradeEntriesProvider = Provider.autoDispose<List<GradeEntry>>((ref) {
  final allEntries = ref.watch(allGradeEntriesStreamProvider).asData?.value ?? [];
  return allEntries.where((entry) => entry.status == GradeEntryStatus.submitted).toList();
});

//======================================================================
// State Notifiers for Grade Management
//======================================================================

/// حالة إنشاء إدخال درجات جديد
class GradeEntryCreationState {
  final bool isLoading;
  final String? error;
  final String? createdEntryId;

  const GradeEntryCreationState({
    this.isLoading = false,
    this.error,
    this.createdEntryId,
  });

  GradeEntryCreationState copyWith({
    bool? isLoading,
    String? error,
    String? createdEntryId,
  }) {
    return GradeEntryCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdEntryId: createdEntryId ?? this.createdEntryId,
    );
  }
}

/// مدير حالة إنشاء إدخال الدرجات
class GradeEntryCreationNotifier extends StateNotifier<GradeEntryCreationState> {
  final dynamic _firebaseService;

  GradeEntryCreationNotifier(this._firebaseService) : super(const GradeEntryCreationState());

  /// إنشاء إدخال درجات جديد
  Future<void> createGradeEntry(GradeEntry gradeEntry) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final entryId = await _firebaseService.createGradeEntry(gradeEntry);
      state = state.copyWith(
        isLoading: false,
        createdEntryId: entryId,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في إنشاء إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    state = const GradeEntryCreationState();
  }
}

/// حالة تحديث إدخال الدرجات
class GradeEntryUpdateState {
  final bool isLoading;
  final String? error;
  final bool isUpdated;

  const GradeEntryUpdateState({
    this.isLoading = false,
    this.error,
    this.isUpdated = false,
  });

  GradeEntryUpdateState copyWith({
    bool? isLoading,
    String? error,
    bool? isUpdated,
  }) {
    return GradeEntryUpdateState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isUpdated: isUpdated ?? this.isUpdated,
    );
  }
}

/// مدير حالة تحديث إدخال الدرجات
class GradeEntryUpdateNotifier extends StateNotifier<GradeEntryUpdateState> {
  final dynamic _firebaseService;

  GradeEntryUpdateNotifier(this._firebaseService) : super(const GradeEntryUpdateState());

  /// تحديث إدخال درجات
  Future<void> updateGradeEntry(String entryId, GradeEntry gradeEntry) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.updateGradeEntry(entryId, gradeEntry);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحديث إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// إرسال إدخال الدرجات للمراجعة
  Future<void> submitGradeEntry(String entryId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.submitGradeEntry(entryId);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في إرسال إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// اعتماد إدخال الدرجات
  Future<void> approveGradeEntry(String entryId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.approveGradeEntry(entryId);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في اعتماد إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// رفض إدخال الدرجات
  Future<void> rejectGradeEntry(String entryId, String reason) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.rejectGradeEntry(entryId, reason);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في رفض إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// حذف إدخال درجات
  Future<void> deleteGradeEntry(String entryId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.deleteGradeEntry(entryId);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في حذف إدخال الدرجات: ${e.toString()}',
      );
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    state = const GradeEntryUpdateState();
  }
}

//======================================================================
// Data Models for Providers
//======================================================================

/// نموذج إحصائيات إدخال الدرجات
class GradeEntryStats {
  final int totalEntries;
  final int draftEntries;
  final int submittedEntries;
  final int approvedEntries;
  final int rejectedEntries;

  const GradeEntryStats({
    required this.totalEntries,
    required this.draftEntries,
    required this.submittedEntries,
    required this.approvedEntries,
    required this.rejectedEntries,
  });

  /// نسبة الإدخالات المعتمدة
  double get approvalPercentage {
    if (totalEntries == 0) return 0.0;
    return (approvedEntries / totalEntries) * 100;
  }

  /// نسبة الإدخالات المرفوضة
  double get rejectionPercentage {
    if (totalEntries == 0) return 0.0;
    return (rejectedEntries / totalEntries) * 100;
  }

  /// نسبة الإدخالات المرسلة للمراجعة
  double get pendingPercentage {
    if (totalEntries == 0) return 0.0;
    return (submittedEntries / totalEntries) * 100;
  }

  /// نسبة الإدخالات في المسودة
  double get draftPercentage {
    if (totalEntries == 0) return 0.0;
    return (draftEntries / totalEntries) * 100;
  }

  @override
  String toString() {
    return 'GradeEntryStats(total: $totalEntries, approved: $approvedEntries, rejected: $rejectedEntries, pending: $submittedEntries, draft: $draftEntries)';
  }
}
