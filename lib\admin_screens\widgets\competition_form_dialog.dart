import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/competition_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class CompetitionFormDialog extends ConsumerStatefulWidget {
  final CompetitionModel? competition;

  const CompetitionFormDialog({super.key, this.competition});

  @override
  _CompetitionFormDialogState createState() => _CompetitionFormDialogState();
}

class _CompetitionFormDialogState extends ConsumerState<CompetitionFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.competition?.title ?? '');
    _descriptionController = TextEditingController(text: widget.competition?.description ?? '');
    _startDate = widget.competition?.startDate;
    _endDate = widget.competition?.endDate;
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: (isStartDate ? _startDate : _endDate) ?? DateTime.now(),
      firstDate: DateTime(2000),
      lastDate: DateTime(2101),
    );
    if (picked != null) {
      setState(() {
        if (isStartDate) {
          _startDate = picked;
        } else {
          _endDate = picked;
        }
      });
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final firebaseService = ref.read(firebaseServiceProvider);
        final competitionData = {
          'title': _titleController.text,
          'description': _descriptionController.text,
          'startDate': _startDate,
          'endDate': _endDate,
        };

        if (widget.competition == null) {
          await firebaseService.addCompetition(competitionData);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تمت إضافة المسابقة بنجاح')),
          );
        } else {
          await firebaseService.updateCompetition(widget.competition!.id, competitionData);
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث المسابقة بنجاح')),
          );
        }
        Navigator.of(context).pop();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ: $e')),
        );
      } finally {
        if (mounted) {
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(widget.competition == null ? 'إضافة مسابقة جديدة' : 'تعديل المسابقة'),
      content: _isLoading
          ? const LoadingIndicator()
          : SingleChildScrollView(
              child: Form(
                key: _formKey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: _titleController,
                      decoration: const InputDecoration(labelText: 'عنوان المسابقة'),
                      validator: (value) => value!.isEmpty ? 'الرجاء إدخال العنوان' : null,
                    ),
                    TextFormField(
                      controller: _descriptionController,
                      decoration: const InputDecoration(labelText: 'الوصف'),
                      maxLines: 3,
                    ),
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Expanded(
                          child: Text('تاريخ البدء: ${_startDate?.toLocal().toString().split(' ')[0] ?? 'لم يحدد'}'),
                        ),
                        IconButton(
                          icon: const Icon(Icons.calendar_today),
                          onPressed: () => _selectDate(context, true),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Text('تاريخ الانتهاء: ${_endDate?.toLocal().toString().split(' ')[0] ?? 'لم يحدد'}'),
                        ),
                        IconButton(
                          icon: const Icon(Icons.calendar_today),
                          onPressed: () => _selectDate(context, false),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submit,
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
