import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/communication_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة الرسائل الخاصة بولي أمر معين.
final communicationStreamProvider = StreamProvider.autoDispose.family<List<CommunicationModel>, String>((ref, guardianId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getCommunicationStream(guardianId);
});

/// Controller لإدارة منطق إرسال الرسائل.
class CommunicationController extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;

  CommunicationController(this._ref) : super(const AsyncValue.data(null));

  Future<void> sendMessage(String guardianId, String message) async {
    if (message.trim().isEmpty) {
      return;
    }
    state = const AsyncValue.loading();
    try {
      await _ref.read(firebaseServiceProvider).sendCommunicationMessage(guardianId, message);
      state = const AsyncValue.data(null);
    } catch (e) {
      state = AsyncValue.error(e, StackTrace.current);
    }
  }
}

/// Provider لـ CommunicationController.
final communicationControllerProvider = StateNotifierProvider.autoDispose<CommunicationController, AsyncValue<void>>((ref) {
  return CommunicationController(ref);
});
