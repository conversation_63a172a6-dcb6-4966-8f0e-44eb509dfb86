"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getClassComprehensiveReport = exports.sendScheduledNotifications = exports.onNotificationCreated = exports.deleteUserAccount = exports.createUserAccount = void 0;
const v1_1 = require("firebase-functions/v1");
const admin = __importStar(require("firebase-admin"));
// تهيئة Firebase Admin SDK
admin.initializeApp();
const db = admin.firestore();
const messaging = admin.messaging();
// ===================================================================
//          (جديد) الدوال المحسنة لإدارة المستخدمين
// ===================================================================
/**
 * (جديد) دالة سحابية قابلة للاستدعاء لإنشاء مستخدم جديد مع دوره وبياناته.
 * تحل مشكلة تسجيل خروج المدير عند إنشاء حسابات جديدة.
 * @param {object} data - البيانات القادمة من العميل.
 * @param {string} data.email - البريد الإلكتروني للمستخدم الجديد.
 * @param {string} data.password - كلمة المرور للمستخدم الجديد.
 * @param {string} data.displayName - اسم العرض للمستخدم.
 * @param {string} data.role - دور المستخدم (e.g., 'student', 'teacher', 'admin').
 * @param {object} data.additionalData - بيانات إضافية ليتم حفظها في Firestore.
 * @param {https.CallableContext} context - سياق الدالة (يحتوي على معلومات المصادقة).
 * @returns {Promise<{uid: string}>} - معرف المستخدم الجديد.
 */
exports.createUserAccount = v1_1.https.onCall(async (data, context) => {
    var _a;
    // -- تعديل منطق التحقق من الصلاحيات --
    // 1. تحقق أولاً مما إذا كان هناك أي مسؤولين في النظام.
    const adminQuery = await db.collection("users")
        .where("role", "==", "admin").limit(1).get();
    const isFirstAdminCreation = adminQuery.empty;
    // 2. إذا لم يكن هذا أول مسؤول يتم إنشاؤه، تحقق من صلاحيات المستدعي.
    if (!isFirstAdminCreation && ((_a = context.auth) === null || _a === void 0 ? void 0 : _a.token.role) !== "admin") {
        throw new v1_1.https.HttpsError("permission-denied", "ليس لديك الصلاحية لإنشاء مستخدمين جدد.");
    }
    // إذا كان هذا هو أول مسؤول، نسمح بالمتابعة دون التحقق من الصلاحيات.
    const { email, password, displayName, role, additionalData } = data;
    // 2. التحقق من وجود البيانات المطلوبة
    if (!email || !password || !displayName || !role) {
        throw new v1_1.https.HttpsError("invalid-argument", "البيانات المرسلة غير كاملة (email, password, displayName, role).");
    }
    try {
        // 3. إنشاء المستخدم في Firebase Authentication
        const userRecord = await admin.auth().createUser({
            email: email,
            password: password,
            displayName: displayName,
            disabled: false, // تأكد من أن الحساب مفعل
        });
        // 4. تعيين دور المستخدم (Role) كـ Custom Claim
        // هذا يرفع مستوى الأمان ويتيح التحقق من الصلاحيات في Security Rules
        await admin.auth().setCustomUserClaims(userRecord.uid, { role: role });
        // 5. تحديد الـ collection المناسب بناءً على الدور
        let collectionPath = "users"; // المسار الافتراضي
        if (role === "student") {
            collectionPath = "students";
        }
        else if (role === "guardian") {
            collectionPath = "guardians";
        }
        // ملاحظة: المعلمون والإداريون يتم حفظهم في 'users'
        // 6. حفظ بيانات المستخدم الإضافية في Firestore
        const userData = Object.assign(Object.assign({}, additionalData), { email: email, displayName: displayName, role: role, createdAt: admin.firestore.FieldValue.serverTimestamp() });
        // إذا كان الدور طالبًا، يتم إنشاء مستند في 'students' و 'users'
        if (role === "student") {
            await db.collection("students").doc(userRecord.uid).set(userData);
            await db.collection("users").doc(userRecord.uid).set({
                email: email,
                displayName: displayName,
                role: role,
            });
        }
        else {
            await db.collection(collectionPath).doc(userRecord.uid).set(userData);
        }
        console.log(`Successfully created new user: ${userRecord.uid} with role: ${role}`);
        return { uid: userRecord.uid };
    }
    catch (error) {
        console.error("Error creating new user:", error);
        if (error.code === "auth/email-already-exists") {
            throw new v1_1.https.HttpsError("already-exists", "هذا البريد الإلكتروني مستخدم بالفعل.");
        }
        throw new v1_1.https.HttpsError("internal", "حدث خطأ غير متوقع أثناء إنشاء المستخدم.");
    }
});
/**
 * (مُحسَّن) دالة قابلة للاستدعاء لحذف مستخدم من Auth وجميع بياناته من Firestore.
 * @param {object} data - البيانات القادمة من العميل.
 * @param {string} data.uidToDelete - معرف المستخدم المراد حذفه.
 * @param {string[]} data.collectionsToDeleteFrom - قائمة بأسماء الـ collections التي يجب حذف مستندات المستخدم منها.
 * @param {https.CallableContext} context - سياق الدالة.
 * @returns {Promise<{success: boolean, message: string}>} - رسالة نجاح.
 */
exports.deleteUserAccount = v1_1.https.onCall(async (data, context) => {
    var _a, _b;
    // 1. التحقق من أن المستدعي هو مدير
    if (((_a = context.auth) === null || _a === void 0 ? void 0 : _a.token.role) !== "admin") {
        throw new v1_1.https.HttpsError("permission-denied", "ليس لديك الصلاحية لحذف المستخدمين.");
    }
    const { uidToDelete, collectionsToDeleteFrom } = data;
    const callerUid = (_b = context.auth) === null || _b === void 0 ? void 0 : _b.uid;
    if (!uidToDelete || !collectionsToDeleteFrom) {
        throw new v1_1.https.HttpsError("invalid-argument", "البيانات المرسلة غير كاملة (uidToDelete, collectionsToDeleteFrom).");
    }
    // 2. منع المدير من حذف نفسه
    if (uidToDelete === callerUid) {
        throw new v1_1.https.HttpsError("failed-precondition", "لا يمكنك حذف حسابك الخاص.");
    }
    try {
        // 3. حذف المستخدم من Firebase Authentication
        await admin.auth().deleteUser(uidToDelete);
        console.log(`Successfully deleted user ${uidToDelete} from Auth.`);
        // 4. حذف جميع مستندات المستخدم من الـ collections المحددة باستخدام Batch
        const batch = db.batch();
        collectionsToDeleteFrom.forEach((collectionName) => {
            const docRef = db.collection(collectionName).doc(uidToDelete);
            batch.delete(docRef);
        });
        await batch.commit();
        console.log(`Successfully deleted user data from Firestore for ${uidToDelete}.`);
        return { success: true, message: "تم حذف المستخدم وجميع بياناته بنجاح." };
    }
    catch (error) {
        console.error(`Failed to delete user ${uidToDelete}:`, error);
        if (error.code === "auth/user-not-found") {
            // إذا لم يكن المستخدم موجودًا في Auth، لا يزال بإمكاننا محاولة حذف بياناته من Firestore
            console.log("User not found in Auth, attempting to clean up Firestore data.");
            const batch = db.batch();
            collectionsToDeleteFrom.forEach((collectionName) => {
                const docRef = db.collection(collectionName).doc(uidToDelete);
                batch.delete(docRef);
            });
            await batch.commit();
            return { success: true, message: "تم تنظيف بيانات المستخدم من قاعدة البيانات." };
        }
        throw new v1_1.https.HttpsError("internal", "حدث خطأ أثناء حذف المستخدم.");
    }
});
// ===================================================================
//          الدوال الحالية (تم الإبقاء عليها)
// ===================================================================
/**
 * دالة يتم تشغيلها عند إنشاء إشعار جديد غير مجدول.
 */
exports.onNotificationCreated = v1_1.firestore
    .document("notifications/{notificationId}")
    .onCreate(async (snapshot) => {
    const notificationData = snapshot.data();
    // تحقق مما إذا كانت البيانات موجودة وأن الإشعار ليس مجدولاً
    if (!notificationData || notificationData.scheduledTime) {
        console.log("Notification is scheduled or data is missing. Skipping immediate send.");
        return null;
    }
    // استدعاء الدالة العامة لإرسال الإشعار
    return processAndSendNotification(notificationData, snapshot.id);
});
/**
 * دالة مجدولة تعمل كل دقيقة للتحقق من الإشعارات التي حان وقت إرسالها.
 */
exports.sendScheduledNotifications = v1_1.pubsub
    .schedule("every 1 minutes")
    .onRun(async () => {
    const now = admin.firestore.Timestamp.now();
    // جلب الإشعارات المجدولة التي حان وقتها ولم يتم إرسالها بعد
    const query = db
        .collection("notifications")
        .where("status", "==", "scheduled")
        .where("scheduledTime", "<=", now);
    const scheduledNotifications = await query.get();
    if (scheduledNotifications.empty) {
        console.log("No scheduled notifications to send.");
        return null;
    }
    const promises = scheduledNotifications.docs.map(async (doc) => {
        const notificationData = doc.data();
        await processAndSendNotification(notificationData, doc.id);
        // تحديث حالة الإشعار إلى "sent" بعد إرساله
        return doc.ref.update({ status: "sent" });
    });
    return Promise.all(promises);
});
/**
 * دالة مركزية لمعالجة وإرسال الإشعارات.
 * @param {FirebaseFirestore.DocumentData} notificationData بيانات الإشعار.
 * @param {string} notificationId معرف مستند الإشعار.
 */
async function processAndSendNotification(notificationData, notificationId) {
    const { title, content, target, targetClasses } = notificationData;
    if (!title || !content) {
        console.error("Notification is missing title or content.", { notificationId });
        return;
    }
    // جلب توكنات FCM بناءً على الجمهور المستهدف
    const tokens = await getTargetTokens(target, targetClasses);
    if (tokens.length === 0) {
        console.log("No FCM tokens found for the target audience.", {
            target,
            targetClasses,
        });
        return;
    }
    // بناء حمولة الرسالة
    const payload = {
        notification: {
            title: title,
            body: content,
            sound: "default",
        },
        data: {
            notificationId: notificationId,
            click_action: "FLUTTER_NOTIFICATION_CLICK", // مهم لتطبيقات فلاتر
        },
    };
    // إرسال الإشعار إلى التوكنات المحددة
    try {
        const response = await messaging.sendToDevice(tokens, payload);
        console.log(`Successfully sent message to ${response.successCount} devices.`);
        // يمكنك هنا التعامل مع الأخطاء أو التوكنات غير الصالحة
        if (response.failureCount > 0) {
            console.error(`Failed to send to ${response.failureCount} devices.`);
        }
    }
    catch (error) {
        console.error("Error sending notification:", error);
    }
}
/**
 * دالة لجلب توكنات FCM بناءً على الجمهور المستهدف.
 * @param {string} target نوع الجمهور.
 * @param {string[]} targetClasses قائمة بمعرفات الصفوف (إذا كان الاستهداف لصفوف محددة).
 * @return {Promise<string[]>} قائمة بتوكنات FCM.
 */
async function getTargetTokens(target, targetClasses) {
    let userDocs;
    switch (target) {
        case "teachers": {
            userDocs = await db.collection("users")
                .where("role", "==", "teacher").get();
            break;
        }
        case "guardians": {
            userDocs = await db.collection("users")
                .where("role", "==", "guardian").get();
            break;
        }
        case "specificClasses": {
            if (!targetClasses || targetClasses.length === 0) {
                return [];
            }
            // جلب الطلاب في الصفوف المحددة
            const studentsSnapshot = await db
                .collection("students")
                .where("classId", "in", targetClasses)
                .get();
            const guardianIds = studentsSnapshot.docs
                .map((doc) => doc.data().guardianId);
            if (guardianIds.length === 0) {
                return [];
            }
            // جلب أولياء الأمور المرتبطين بالطلاب
            userDocs = await db
                .collection("users")
                .where(admin.firestore.FieldPath.documentId(), "in", guardianIds)
                .get();
            break;
        }
        case "all":
        default: {
            userDocs = await db.collection("users").get();
            break;
        }
    }
    // استخراج التوكنات الصالحة من مستندات المستخدمين
    const tokens = userDocs.docs
        .map((doc) => doc.data().fcmToken)
        .filter((token) => !!token);
    return [...new Set(tokens)]; // إزالة التوكنات المكررة
}
/**
 * دالة قابلة للاستدعاء (Callable Function) لتجميع تقرير شامل عن طلاب فصل معين.
 * تحل هذه الدالة مشكلة N+1 عن طريق تجميع البيانات من جانب الخادم.
 * @param {{classId: string}} data - البيانات المدخلة التي تحتوي على معرف الفصل.
 * @returns {Promise<any[]>} - مصفوفة من بيانات الطلاب الشاملة.
 */
exports.getClassComprehensiveReport = v1_1.https.onCall(async (data, context) => {
    // التحقق من أن المستخدم الذي استدعى الدالة مصادق عليه (ممارسة جيدة)
    if (!context.auth) {
        throw new v1_1.https.HttpsError("unauthenticated", "يجب أن تكون مسجلاً للدخول لطلب التقارير.");
    }
    const classId = data.classId;
    if (!classId) {
        throw new v1_1.https.HttpsError("invalid-argument", "يجب توفير معرف الفصل (classId).");
    }
    try {
        // 1. جلب جميع الطلاب في الفصل المحدد
        const studentsSnapshot = await db
            .collection("students")
            .where("classId", "==", classId)
            .get();
        if (studentsSnapshot.empty) {
            console.log(`No students found for classId: ${classId}`);
            return [];
        }
        // 2. إنشاء مصفوفة من الوعود (Promises) لجلب البيانات الفرعية لكل طالب بالتوازي
        const reportPromises = studentsSnapshot.docs.map(async (studentDoc) => {
            const studentId = studentDoc.id;
            const studentData = studentDoc.data();
            // جلب البيانات الفرعية بالتوازي لكل طالب
            const [gradesSnapshot, attendanceSnapshot, feesSnapshot, paymentsSnapshot,] = await Promise.all([
                db.collection("students").doc(studentId).collection("grades").get(),
                db
                    .collection("students")
                    .doc(studentId)
                    .collection("attendance")
                    .orderBy("date", "desc")
                    .get(),
                db
                    .collection("students")
                    .doc(studentId)
                    .collection("assigned_fees")
                    .get(),
                db.collection("students").doc(studentId).collection("payments").get(),
            ]);
            // 3. معالجة وتجميع البيانات
            const grades = gradesSnapshot.docs.map((doc) => doc.data());
            const attendance = attendanceSnapshot.docs.map((doc) => doc.data());
            const assignedFees = feesSnapshot.docs.map((doc) => doc.data());
            const payments = paymentsSnapshot.docs.map((doc) => doc.data());
            // حساب الملخص المالي باستخدام reduce لمزيد من الكفاءة
            const { totalAssigned, totalPaid } = assignedFees.reduce((acc, fee) => {
                acc.totalAssigned += (fee.amount_total || 0);
                acc.totalPaid += (fee.amount_paid || 0);
                return acc;
            }, { totalAssigned: 0, totalPaid: 0 });
            // 4. بناء كائن التقرير النهائي للطالب
            return {
                studentInfo: studentData,
                grades,
                attendance,
                financials: {
                    assignedFees,
                    payments,
                    totalAssigned,
                    totalPaid,
                    totalRemaining: totalAssigned - totalPaid,
                },
            };
        });
        // انتظار اكتمال جميع عمليات جلب البيانات لجميع الطلاب
        const reportData = await Promise.all(reportPromises);
        return reportData;
    }
    catch (error) {
        console.error("Error generating class comprehensive report:", error);
        throw new v1_1.https.HttpsError("internal", "حدث خطأ أثناء إنشاء التقرير.");
    }
});
// تم استبدال الدالة القديمة 'deleteUser' بالدالة الجديدة 'deleteUserAccount'
// التي تتعامل مع الحذف من Auth و Firestore معًا.
//# sourceMappingURL=index.js.map