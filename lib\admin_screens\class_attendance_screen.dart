import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة لتسجيل حضور وغياب فصل دراسي كامل ليوم محدد
class ClassAttendanceScreen extends StatefulWidget {
  final String classId; // معرّف الفصل الدراسي

  const ClassAttendanceScreen({super.key, required this.classId});

  @override
  State<ClassAttendanceScreen> createState() => _ClassAttendanceScreenState();
}

class _ClassAttendanceScreenState extends State<ClassAttendanceScreen> {
  List<StudentModel> _students = []; // قائمة طلاب الفصل
  bool _isLoading = true; // حالة التحميل
  bool _isSaving = false; // حالة الحفظ
  // لتخزين حالة الحضور لكل طالب (studentId -> status)
  Map<String, String> _attendanceStatus = {};
  // قائمة حالات الحضور المتاحة
  final List<String> _statuses = ['حاضر', 'غائب', 'متأخر', 'غائب بعذر'];

  @override
  void initState() {
    super.initState();
    _fetchStudents(); // جلب الطلاب عند بدء تشغيل الشاشة
  }

  /// جلب قائمة الطلاب من Firestore بناءً على معرّف الفصل
  Future<void> _fetchStudents() async {
    try {
      final snapshot = await FirebaseFirestore.instance
          .collection('students')
          .where('classId', isEqualTo: widget.classId)
          .orderBy('name')
          .get();

      // تحويل البيانات إلى قائمة من نماذج الطلاب
      final students = snapshot.docs
          .map((doc) => StudentModel.fromMap(doc.data(), doc.id))
          .toList();

      setState(() {
        _students = students;
        // إعطاء قيمة أولية "حاضر" لكل الطلاب
        _attendanceStatus = {
          for (var student in _students) student.id: 'حاضر'
        };
        _isLoading = false;
      });
    } catch (e) {
      // معالجة الأخطاء في حال فشل جلب البيانات
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل في جلب الطلاب: $e')),
      );
    }
  }

  /// حفظ بيانات الحضور لجميع الطلاب دفعة واحدة
  Future<void> _saveAttendance() async {
    setState(() {
      _isSaving = true; // تفعيل مؤشر الحفظ
    });

    final batch = FirebaseFirestore.instance.batch(); // إنشاء عملية دفعة
    // الحصول على التاريخ الحالي بصيغة موحدة (بدون وقت)
    final today = DateFormat('yyyy-MM-dd').format(DateTime.now());

    // إضافة عملية كتابة لكل طالب في الدفعة
    _attendanceStatus.forEach((studentId, status) {
      final studentRef = FirebaseFirestore.instance.collection('students').doc(studentId);
      final attendanceRef = studentRef.collection('attendance').doc(today);
      batch.set(attendanceRef, {'status': status, 'date': Timestamp.now()});
    });

    try {
      await batch.commit(); // تنفيذ جميع عمليات الكتابة
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الحضور بنجاح!'),
          backgroundColor: Colors.green,
        ),
      );
      Navigator.pop(context); // العودة إلى الشاشة السابقة بعد الحفظ
    } catch (e) {
      // معالجة الأخطاء في حال فشل الحفظ
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في حفظ الحضور: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSaving = false; // إيقاف مؤشر الحفظ
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تسجيل حضور الفصل', style: GoogleFonts.cairo()),
      ),
      body: _isLoading
          ? const LoadingIndicator() // عرض مؤشر التحميل
          : _students.isEmpty
              ? Center(child: Text('لا يوجد طلاب في هذا الفصل.', style: GoogleFonts.cairo()))
              : Column(
                  children: [
                    Expanded(
                      child: ListView.builder(
                        padding: const EdgeInsets.all(8.0),
                        itemCount: _students.length,
                        itemBuilder: (context, index) {
                          final student = _students[index];
                          return Card(
                            margin: const EdgeInsets.symmetric(vertical: 6, horizontal: 8),
                            child: Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  // اسم ورقم الطالب
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          student.name,
                                          style: GoogleFonts.cairo(fontWeight: FontWeight.bold, fontSize: 16),
                                        ),
                                        Text(
                                          'الرقم: ${student.studentNumber}',
                                          style: GoogleFonts.cairo(color: Colors.grey[600]),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // القائمة المنسدلة لاختيار حالة الحضور
                                  DropdownButton<String>(
                                    value: _attendanceStatus[student.id],
                                    items: _statuses.map((String value) {
                                      return DropdownMenuItem<String>(
                                        value: value,
                                        child: Text(value, style: GoogleFonts.cairo()),
                                      );
                                    }).toList(),
                                    onChanged: (newValue) {
                                      setState(() {
                                        _attendanceStatus[student.id] = newValue!;
                                      });
                                    },
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    // زر الحفظ
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: _isSaving
                          ? const LoadingIndicator()
                          : ElevatedButton.icon(
                              onPressed: _saveAttendance,
                              icon: const Icon(Icons.save),
                              label: Text('حفظ الحضور', style: GoogleFonts.cairo()),
                              style: ElevatedButton.styleFrom(
                                minimumSize: const Size(double.infinity, 50),
                                textStyle: const TextStyle(fontSize: 18),
                              ),
                            ),
                    ),
                  ],
                ),
    );
  }
}
