
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات الأنشطة والفعاليات
class ActivityModel {
  final String id;
  final String title; // عنوان النشاط
  final String description; // وصف النشاط (يمكن أن يكون نصاً طويلاً)
  final DateTime date; // تاريخ النشاط
  final List<String> imageUrls; // قائمة روابط الصور للنشاط
  final String? videoUrl; // رابط فيديو (يوتيوب، فيميو، الخ) - اختياري
  // قائمة من الخرائط لتخزين الملفات المرفقة، كل خريطة تحتوي على اسم الملف ورابطه
  final List<Map<String, String>> attachments; 

  ActivityModel({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    this.imageUrls = const [],
    this.videoUrl,
    this.attachments = const [],
  });

  /// دالة لتحويل البيانات من Firestore إلى كائن ActivityModel
  factory ActivityModel.fromMap(Map<String, dynamic> data, String documentId) {
    return ActivityModel(
      id: documentId,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      // تحويل التاريخ من Timestamp في Firestore إلى DateTime
      date: (data['date'] as Timestamp).toDate(),
      // تحويل قائمة روابط الصور، مع التأكد من أنها List<dynamic> ثم تحويلها إلى List<String>
      imageUrls: data['imageUrls'] != null ? List<String>.from(data['imageUrls']) : [],
      videoUrl: data['videoUrl'],
      // تحويل قائمة المرفقات
      attachments: data['attachments'] != null 
          ? List<Map<String, String>>.from(
              (data['attachments'] as List).map((item) => Map<String, String>.from(item))
            )
          : [],
    );
  }

  /// دالة لتحويل الكائن إلى Map لتخزينه في Firestore
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'date': Timestamp.fromDate(date),
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'attachments': attachments,
    };
  }
}
