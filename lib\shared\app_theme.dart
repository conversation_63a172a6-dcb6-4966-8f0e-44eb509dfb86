import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primarySwatch: Colors.blue,
      primaryColor: Colors.blue.shade800,
      // accentColor is deprecated, use colorScheme instead
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: Colors.blue,
        accentColor: Colors.greenAccent,
        brightness: Brightness.light,
      ),
      scaffoldBackgroundColor: Colors.grey[50],
      fontFamily: GoogleFonts.cairo().fontFamily,
      appBarTheme: AppBarTheme(
        backgroundColor: Colors.blue.shade800,
        elevation: 2,
        titleTextStyle: TextStyle(
          fontFamily: GoogleFonts.cairo().fontFamily,
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      textTheme: TextTheme(
        bodyLarge: TextStyle(
          height: 1.5,
          fontSize: 16,
          color: Colors.grey[800],
        ),
        bodyMedium: TextStyle(
          height: 1.5,
          fontSize: 14,
          color: Colors.grey[700],
        ),
        headlineSmall: TextStyle(
          fontWeight: FontWeight.bold,
          color: Colors.blue.shade900,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.blue.shade800,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.grey[300]!),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(color: Colors.blue.shade800),
        ),
      ),
    );
  }
}
