import 'package:cloud_firestore/cloud_firestore.dart';

/// تعداد أنواع المناهج
enum SyllabusType {
  comprehensive, // شامل
  condensed,     // مختصر
  review,        // مراجعة
  intensive,     // مكثف
}

extension SyllabusTypeExtension on SyllabusType {
  String get arabicName {
    switch (this) {
      case SyllabusType.comprehensive:
        return 'شامل';
      case SyllabusType.condensed:
        return 'مختصر';
      case SyllabusType.review:
        return 'مراجعة';
      case SyllabusType.intensive:
        return 'مكثف';
    }
  }
}

/// تعداد مستويات الصعوبة
enum DifficultyLevel {
  easy,    // سهل
  medium,  // متوسط
  hard,    // صعب
}

extension DifficultyLevelExtension on DifficultyLevel {
  String get arabicName {
    switch (this) {
      case DifficultyLevel.easy:
        return 'سهل';
      case DifficultyLevel.medium:
        return 'متوسط';
      case DifficultyLevel.hard:
        return 'صعب';
    }
  }
}

/// نموذج فصل في موضوع المنهج
class SyllabusChapter {
  final String id;
  final String name;
  final String description;
  final int estimatedMinutes;
  final List<String> keyPoints;
  final String? pageReference;
  final bool isCompleted;
  final int order;

  const SyllabusChapter({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedMinutes,
    required this.keyPoints,
    this.pageReference,
    required this.isCompleted,
    required this.order,
  });

  factory SyllabusChapter.fromMap(Map<String, dynamic> map) {
    return SyllabusChapter(
      id: map['id'] as String? ?? '',
      name: map['name'] as String? ?? '',
      description: map['description'] as String? ?? '',
      estimatedMinutes: map['estimatedMinutes'] as int? ?? 0,
      keyPoints: List<String>.from(map['keyPoints'] ?? []),
      pageReference: map['pageReference'] as String?,
      isCompleted: map['isCompleted'] as bool? ?? false,
      order: map['order'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'estimatedMinutes': estimatedMinutes,
      'keyPoints': keyPoints,
      'pageReference': pageReference,
      'isCompleted': isCompleted,
      'order': order,
    };
  }

  SyllabusChapter copyWith({
    String? id,
    String? name,
    String? description,
    int? estimatedMinutes,
    List<String>? keyPoints,
    String? pageReference,
    bool? isCompleted,
    int? order,
  }) {
    return SyllabusChapter(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      estimatedMinutes: estimatedMinutes ?? this.estimatedMinutes,
      keyPoints: keyPoints ?? this.keyPoints,
      pageReference: pageReference ?? this.pageReference,
      isCompleted: isCompleted ?? this.isCompleted,
      order: order ?? this.order,
    );
  }
}

/// نموذج موضوع في المنهج
class SyllabusTopicModel {
  final String id;
  final String name;
  final String description;
  final double weight; // الوزن النسبي من 0 إلى 100
  final int marks; // الدرجات المخصصة
  final int estimatedHours;
  final List<SyllabusChapter> chapters;
  final bool isCompleted;
  final int order;

  const SyllabusTopicModel({
    required this.id,
    required this.name,
    required this.description,
    required this.weight,
    required this.marks,
    required this.estimatedHours,
    required this.chapters,
    required this.isCompleted,
    required this.order,
  });

  factory SyllabusTopicModel.fromMap(Map<String, dynamic> map) {
    return SyllabusTopicModel(
      id: map['id'] as String? ?? '',
      name: map['name'] as String? ?? '',
      description: map['description'] as String? ?? '',
      weight: (map['weight'] as num?)?.toDouble() ?? 0.0,
      marks: map['marks'] as int? ?? 0,
      estimatedHours: map['estimatedHours'] as int? ?? 0,
      chapters: (map['chapters'] as List<dynamic>?)
          ?.map((chapter) => SyllabusChapter.fromMap(chapter as Map<String, dynamic>))
          .toList() ?? [],
      isCompleted: map['isCompleted'] as bool? ?? false,
      order: map['order'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'weight': weight,
      'marks': marks,
      'estimatedHours': estimatedHours,
      'chapters': chapters.map((chapter) => chapter.toMap()).toList(),
      'isCompleted': isCompleted,
      'order': order,
    };
  }

  SyllabusTopicModel copyWith({
    String? id,
    String? name,
    String? description,
    double? weight,
    int? marks,
    int? estimatedHours,
    List<SyllabusChapter>? chapters,
    bool? isCompleted,
    int? order,
  }) {
    return SyllabusTopicModel(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      weight: weight ?? this.weight,
      marks: marks ?? this.marks,
      estimatedHours: estimatedHours ?? this.estimatedHours,
      chapters: chapters ?? this.chapters,
      isCompleted: isCompleted ?? this.isCompleted,
      order: order ?? this.order,
    );
  }
}

/// نموذج منهج الامتحان للإدارة
class ExamSyllabusModel {
  final String id;
  final String examId;
  final String examName;
  final String subjectId;
  final String teacherId;
  final String syllabusName;
  final String description;
  final List<SyllabusTopicModel> topics;
  final int totalMarks;
  final int estimatedStudyHours;
  final DifficultyLevel difficultyLevel;
  final SyllabusType syllabusType;
  final bool isPublished;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ExamSyllabusModel({
    required this.id,
    required this.examId,
    required this.examName,
    required this.subjectId,
    required this.teacherId,
    required this.syllabusName,
    required this.description,
    required this.topics,
    required this.totalMarks,
    required this.estimatedStudyHours,
    required this.difficultyLevel,
    required this.syllabusType,
    required this.isPublished,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ExamSyllabusModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ExamSyllabusModel(
      id: doc.id,
      examId: data['examId'] as String? ?? '',
      examName: data['examName'] as String? ?? '',
      subjectId: data['subjectId'] as String? ?? '',
      teacherId: data['teacherId'] as String? ?? '',
      syllabusName: data['syllabusName'] as String? ?? '',
      description: data['description'] as String? ?? '',
      topics: (data['topics'] as List<dynamic>?)
          ?.map((topic) => SyllabusTopicModel.fromMap(topic as Map<String, dynamic>))
          .toList() ?? [],
      totalMarks: data['totalMarks'] as int? ?? 0,
      estimatedStudyHours: data['estimatedStudyHours'] as int? ?? 0,
      difficultyLevel: DifficultyLevel.values.firstWhere(
        (e) => e.toString() == data['difficultyLevel'],
        orElse: () => DifficultyLevel.medium,
      ),
      syllabusType: SyllabusType.values.firstWhere(
        (e) => e.toString() == data['syllabusType'],
        orElse: () => SyllabusType.comprehensive,
      ),
      isPublished: data['isPublished'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'examId': examId,
      'examName': examName,
      'subjectId': subjectId,
      'teacherId': teacherId,
      'syllabusName': syllabusName,
      'description': description,
      'topics': topics.map((topic) => topic.toMap()).toList(),
      'totalMarks': totalMarks,
      'estimatedStudyHours': estimatedStudyHours,
      'difficultyLevel': difficultyLevel.toString(),
      'syllabusType': syllabusType.toString(),
      'isPublished': isPublished,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  ExamSyllabusModel copyWith({
    String? id,
    String? examId,
    String? examName,
    String? subjectId,
    String? teacherId,
    String? syllabusName,
    String? description,
    List<SyllabusTopicModel>? topics,
    int? totalMarks,
    int? estimatedStudyHours,
    DifficultyLevel? difficultyLevel,
    SyllabusType? syllabusType,
    bool? isPublished,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ExamSyllabusModel(
      id: id ?? this.id,
      examId: examId ?? this.examId,
      examName: examName ?? this.examName,
      subjectId: subjectId ?? this.subjectId,
      teacherId: teacherId ?? this.teacherId,
      syllabusName: syllabusName ?? this.syllabusName,
      description: description ?? this.description,
      topics: topics ?? this.topics,
      totalMarks: totalMarks ?? this.totalMarks,
      estimatedStudyHours: estimatedStudyHours ?? this.estimatedStudyHours,
      difficultyLevel: difficultyLevel ?? this.difficultyLevel,
      syllabusType: syllabusType ?? this.syllabusType,
      isPublished: isPublished ?? this.isPublished,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}
