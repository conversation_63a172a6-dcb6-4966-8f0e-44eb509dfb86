import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/models/competition_model.dart';
import 'package:school_management_system/models/lesson_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة الدروس كـ Stream.
///
/// يتم تحديث الواجهة تلقائيًا عند إضافة أو تعديل أو حذف أي درس.
final lessonsStreamProvider = StreamProvider.autoDispose<List<LessonModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // نفترض أن getCollection تم تعديله ليعيد Stream<List<Model>>
  // أو سنقوم بالتحويل هنا. بناءً على كود FirebaseService، هو يعيد Stream<List<T>>
  return firebaseService.getCollection(
    path: 'lessons',
    builder: (data, documentId) => LessonModel.fromMap(data, documentId),
  );
});

/// Provider لجلب قائمة الأنشطة كـ Stream.
final activitiesStreamProvider = StreamProvider.autoDispose<QuerySnapshot>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getActivitiesStream();
});

/// Provider لجلب قائمة المسابقات كـ Stream.
final competitionsStreamProvider = StreamProvider.autoDispose<List<CompetitionModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getCollection(
    path: 'competitions',
    builder: (data, documentId) => CompetitionModel.fromMap(data, documentId),
  );
});

/// Provider لجلب قائمة الإعلانات العامة من Firestore.
/// يستخدم .autoDispose لضمان تنظيف الـ provider عند عدم استخدامه.
final publicAnnouncementsStreamProvider = StreamProvider.autoDispose((ref) {
  // مشاهدة (watch) مزود خدمة Firebase للوصول إلى دوال Firestore.
  final firebaseService = ref.watch(firebaseServiceProvider);
  // استدعاء الدالة التي تعيد Stream الخاص بالإعلانات.
  return firebaseService.getAnnouncements();
});

// ================== Lessons Providers (Mobile) ==================

/// Provider لجلب قائمة جميع المواد المتاحة من الدروس.
final lessonSubjectsProvider = FutureProvider.autoDispose<List<String>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getUniqueFieldValues('lessons', 'subject');
});

/// Provider لتخزين نص البحث عن الدروس.
final lessonSearchQueryProvider = StateProvider.autoDispose<String>((ref) => '');

/// Provider لتخزين المادة المختارة للتصفية.
final selectedLessonSubjectProvider = StateProvider.autoDispose<String?>((ref) => null);

/// Provider لفلترة قائمة الدروس بناءً على البحث والتصفية.
final filteredLessonsProvider = Provider.autoDispose<List<LessonModel>>((ref) {
  // مراقبة الـ Stream الأصلي للدروس
  final lessonsAsyncValue = ref.watch(lessonsStreamProvider);
  // مراقبة حالة البحث
  final searchQuery = ref.watch(lessonSearchQueryProvider).toLowerCase();
  // مراقبة حالة التصفية
  final selectedSubject = ref.watch(selectedLessonSubjectProvider);

  return lessonsAsyncValue.when(
    data: (lessons) {
      var filtered = lessons;

      // تطبيق البحث
      if (searchQuery.isNotEmpty) {
        filtered = filtered.where((lesson) =>
            lesson.title.toLowerCase().contains(searchQuery)).toList();
      }

      // تطبيق التصفية
      if (selectedSubject != null) {
        filtered = filtered.where((lesson) =>
            lesson.subject == selectedSubject).toList();
      }

      return filtered;
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
