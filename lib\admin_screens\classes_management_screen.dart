import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/class_details_view.dart';
import 'package:school_management_system/admin_screens/widgets/class_dialogs.dart';
import 'package:school_management_system/admin_screens/widgets/classes_list.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class ClassesManagementScreen extends ConsumerWidget {
  const ClassesManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classesAsyncValue = ref.watch(classesStreamProvider);
    final selectedClassId = ref.watch(selectedClassIdProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الفصول الدراسية'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          IconButton(
            icon: const Icon(Icons.analytics),
            tooltip: 'إحصائيات الفصول',
            onPressed: () => _showClassStatistics(context, ref),
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير البيانات',
            onPressed: () => _exportClassData(context, ref),
          ),
          // زر إضافة فصل
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => showAddClassDialog(context),
            tooltip: 'إضافة فصل جديد',
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات السريعة
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: classesAsyncValue.when(
              data:
                  (classes) => Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildQuickStat(
                        'إجمالي الفصول',
                        '${classes.length}',
                        Icons.class_,
                        Colors.blue,
                      ),
                      _buildQuickStat(
                        'الفصول النشطة',
                        '${classes.where((c) => c.isActive).length}',
                        Icons.check_circle,
                        Colors.green,
                      ),
                      _buildQuickStat(
                        'الفصول المعطلة',
                        '${classes.where((c) => !c.isActive).length}',
                        Icons.pause_circle,
                        Colors.orange,
                      ),
                      _buildQuickStat(
                        'متوسط الطلاب',
                        '${_calculateAverageStudents(classes)}',
                        Icons.people,
                        Colors.purple,
                      ),
                    ],
                  ),
              loading: () => const SizedBox.shrink(),
              error: (_, __) => const SizedBox.shrink(),
            ),
          ),

          // المحتوى الرئيسي
          Expanded(
            child: Row(
              children: [
                // --- قائمة الفصول ---
                Container(
                  width: 300,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border(
                      right: BorderSide(color: Colors.grey.shade200),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade200),
                          ),
                        ),
                        child: const Row(
                          children: [
                            Icon(Icons.class_, color: Colors.blue),
                            SizedBox(width: 8),
                            Text(
                              'قائمة الفصول',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Expanded(
                        child: classesAsyncValue.when(
                          data: (classes) => ClassesList(classes: classes),
                          loading: () => const LoadingIndicator(),
                          error:
                              (err, stack) => Center(child: Text('خطأ: $err')),
                        ),
                      ),
                    ],
                  ),
                ),

                // --- تفاصيل الفصل المحدد ---
                Expanded(
                  child:
                      selectedClassId == null
                          ? _buildEmptyState()
                          : const ClassDetailsView(),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: color.withAlpha((255 * 0.1).round()),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withAlpha((255 * 0.3).round())),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              value,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.class_, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'اختر فصلاً لعرض التفاصيل',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر تفاصيل الفصل والطلاب هنا',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// حساب متوسط عدد الطلاب
  String _calculateAverageStudents(List<ClassModel> classes) {
    if (classes.isEmpty) return "0";
    final totalStudents = classes.fold<int>(
      0,
      (sum, cls) => sum + cls.studentCount,
    );
    return (totalStudents / classes.length).toStringAsFixed(1);
  }

  /// عرض إحصائيات الفصول
  void _showClassStatistics(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              height: MediaQuery.of(context).size.height * 0.6,
              padding: const EdgeInsets.all(24),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      const Icon(Icons.analytics, color: Colors.blue),
                      const SizedBox(width: 12),
                      const Text(
                        'إحصائيات الفصول الدراسية',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),

                  const Divider(),

                  const Expanded(
                    child: Center(
                      child: Text(
                        'ميزة الإحصائيات التفصيلية قيد التطوير',
                        style: TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// تصدير بيانات الفصول
  void _exportClassData(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ تصدير بيانات الفصول
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التصدير قيد التطوير')));
  }
}
