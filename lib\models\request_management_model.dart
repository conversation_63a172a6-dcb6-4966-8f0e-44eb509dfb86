import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج إدارة الطلبات المتقدم
///
/// يمثل طلب مقدم من ولي أمر أو طالب في النظام المدرسي مع جميع التفاصيل والمراحل
/// يدعم أنواع متعددة من الطلبات مع نظام موافقات متدرج وتتبع شامل للحالة
///
/// الميزات المتقدمة:
/// - أنواع متعددة من الطلبات (شهادات، وثائق، اجتماعات، شكاوى، اقتراحات)
/// - نظام موافقات متدرج حسب نوع الطلب وأهميته
/// - تتبع شامل لحالة الطلب عبر جميع المراحل
/// - إدارة المرفقات والوثائق المطلوبة
/// - نظام أولويات للطلبات العاجلة
/// - تقدير زمني لإنجاز الطلب
/// - تاريخ شامل لجميع الإجراءات المتخذة
/// - نظام تقييم جودة الخدمة
/// - إشعارات تلقائية لجميع الأطراف المعنية
/// - ربط الطلبات بالأنظمة الأخرى في المدرسة
class RequestManagementModel {
  /// معرف الطلب الفريد
  final String id;

  /// رقم الطلب المرجعي (يظهر للمستخدمين)
  final String requestNumber;

  /// معرف مقدم الطلب (ولي أمر أو طالب)
  final String requesterId;

  /// اسم مقدم الطلب
  final String requesterName;

  /// نوع مقدم الطلب (ولي أمر، طالب، موظف)
  final RequesterType requesterType;

  /// معرف الطالب المرتبط بالطلب (إن وجد)
  final String? studentId;

  /// اسم الطالب المرتبط بالطلب
  final String? studentName;

  /// معرف الصف الدراسي للطالب
  final String? classId;

  /// اسم الصف الدراسي
  final String? className;

  /// نوع الطلب الرئيسي
  final RequestType requestType;

  /// فئة الطلب الفرعية
  final RequestCategory category;

  /// أولوية الطلب
  final RequestPriority priority;

  /// حالة الطلب الحالية
  final RequestStatus status;

  /// مرحلة المعالجة الحالية
  final ProcessingStage currentStage;

  /// عنوان الطلب
  final String title;

  /// وصف تفصيلي للطلب
  final String description;

  /// السبب أو المبرر للطلب
  final String reason;

  /// تفاصيل إضافية أو ملاحظات خاصة
  final String? additionalDetails;

  /// تاريخ تقديم الطلب
  final DateTime submittedAt;

  /// التاريخ المطلوب لإنجاز الطلب
  final DateTime? requestedCompletionDate;

  /// التاريخ المتوقع لإنجاز الطلب (حسب النظام)
  final DateTime? estimatedCompletionDate;

  /// التاريخ الفعلي لإنجاز الطلب
  final DateTime? actualCompletionDate;

  /// تاريخ آخر تحديث للطلب
  final DateTime lastUpdated;

  /// معرف من قام بآخر تحديث
  final String? lastUpdatedBy;

  /// قائمة المرفقات المطلوبة
  final List<RequiredDocument> requiredDocuments;

  /// قائمة المرفقات المقدمة من المستخدم
  final List<RequestAttachment> attachments;

  /// قائمة الموافقات المطلوبة
  final List<ApprovalStep> approvalSteps;

  /// الموافقة الحالية المطلوبة
  final ApprovalStep? currentApprovalStep;

  /// قائمة الإجراءات المتخذة على الطلب
  final List<RequestAction> actions;

  /// قائمة التعليقات والملاحظات
  final List<RequestComment> comments;

  /// الرسوم المطلوبة للطلب (إن وجدت)
  final RequestFees? fees;

  /// حالة دفع الرسوم
  final PaymentStatus paymentStatus;

  /// معلومات الدفع
  final PaymentInfo? paymentInfo;

  /// تقييم جودة الخدمة من المستخدم
  final ServiceRating? serviceRating;

  /// ملاحظات إضافية من الإدارة
  final String? adminNotes;

  /// ملاحظات داخلية (لا تظهر للمستخدم)
  final String? internalNotes;

  /// العلامات والتصنيفات
  final List<String> tags;

  /// مستوى الخصوصية
  final PrivacyLevel privacyLevel;

  /// هل الطلب عاجل؟
  final bool isUrgent;

  /// هل الطلب يحتاج متابعة خاصة؟
  final bool requiresSpecialAttention;

  /// هل تم إرسال إشعارات للمستخدم؟
  final bool notificationsSent;

  /// تاريخ آخر إشعار تم إرساله
  final DateTime? lastNotificationSent;

  /// عدد مرات المتابعة
  final int followUpCount;

  /// تاريخ المتابعة التالية
  final DateTime? nextFollowUpDate;

  /// الطلبات المرتبطة (إن وجدت)
  final List<String> relatedRequestIds;

  /// معرف الطلب الأصلي (في حالة التجديد أو التعديل)
  final String? originalRequestId;

  /// هل هذا طلب متجدد؟
  final bool isRenewal;

  /// عدد مرات التجديد
  final int renewalCount;

  /// تاريخ انتهاء صلاحية الطلب (للطلبات المؤقتة)
  final DateTime? expiryDate;

  /// معلومات إضافية (JSON)
  final Map<String, dynamic> metadata;

  const RequestManagementModel({
    required this.id,
    required this.requestNumber,
    required this.requesterId,
    required this.requesterName,
    required this.requesterType,
    this.studentId,
    this.studentName,
    this.classId,
    this.className,
    required this.requestType,
    required this.category,
    required this.priority,
    required this.status,
    required this.currentStage,
    required this.title,
    required this.description,
    required this.reason,
    this.additionalDetails,
    required this.submittedAt,
    this.requestedCompletionDate,
    this.estimatedCompletionDate,
    this.actualCompletionDate,
    required this.lastUpdated,
    this.lastUpdatedBy,
    this.requiredDocuments = const [],
    this.attachments = const [],
    this.approvalSteps = const [],
    this.currentApprovalStep,
    this.actions = const [],
    this.comments = const [],
    this.fees,
    this.paymentStatus = PaymentStatus.notRequired,
    this.paymentInfo,
    this.serviceRating,
    this.adminNotes,
    this.internalNotes,
    this.tags = const [],
    this.privacyLevel = PrivacyLevel.normal,
    this.isUrgent = false,
    this.requiresSpecialAttention = false,
    this.notificationsSent = false,
    this.lastNotificationSent,
    this.followUpCount = 0,
    this.nextFollowUpDate,
    this.relatedRequestIds = const [],
    this.originalRequestId,
    this.isRenewal = false,
    this.renewalCount = 0,
    this.expiryDate,
    this.metadata = const {},
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory RequestManagementModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return RequestManagementModel(
      id: doc.id,
      requestNumber: data['requestNumber'] ?? '',
      requesterId: data['requesterId'] ?? '',
      requesterName: data['requesterName'] ?? '',
      requesterType: RequesterType.values.firstWhere(
        (e) => e.toString() == data['requesterType'],
        orElse: () => RequesterType.parent,
      ),
      studentId: data['studentId'],
      studentName: data['studentName'],
      classId: data['classId'],
      className: data['className'],
      requestType: RequestType.values.firstWhere(
        (e) => e.toString() == data['requestType'],
        orElse: () => RequestType.certificate,
      ),
      category: RequestCategory.values.firstWhere(
        (e) => e.toString() == data['category'],
        orElse: () => RequestCategory.academic,
      ),
      priority: RequestPriority.values.firstWhere(
        (e) => e.toString() == data['priority'],
        orElse: () => RequestPriority.normal,
      ),
      status: RequestStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => RequestStatus.submitted,
      ),
      currentStage: ProcessingStage.values.firstWhere(
        (e) => e.toString() == data['currentStage'],
        orElse: () => ProcessingStage.initial,
      ),
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      reason: data['reason'] ?? '',
      additionalDetails: data['additionalDetails'],
      submittedAt:
          (data['submittedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      requestedCompletionDate:
          (data['requestedCompletionDate'] as Timestamp?)?.toDate(),
      estimatedCompletionDate:
          (data['estimatedCompletionDate'] as Timestamp?)?.toDate(),
      actualCompletionDate:
          (data['actualCompletionDate'] as Timestamp?)?.toDate(),
      lastUpdated:
          (data['lastUpdated'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastUpdatedBy: data['lastUpdatedBy'],
      requiredDocuments:
          (data['requiredDocuments'] as List<dynamic>? ?? [])
              .map((e) => RequiredDocument.fromMap(e as Map<String, dynamic>))
              .toList(),
      attachments:
          (data['attachments'] as List<dynamic>? ?? [])
              .map((e) => RequestAttachment.fromMap(e as Map<String, dynamic>))
              .toList(),
      approvalSteps:
          (data['approvalSteps'] as List<dynamic>? ?? [])
              .map((e) => ApprovalStep.fromMap(e as Map<String, dynamic>))
              .toList(),
      currentApprovalStep:
          data['currentApprovalStep'] != null
              ? ApprovalStep.fromMap(
                data['currentApprovalStep'] as Map<String, dynamic>,
              )
              : null,
      actions:
          (data['actions'] as List<dynamic>? ?? [])
              .map((e) => RequestAction.fromMap(e as Map<String, dynamic>))
              .toList(),
      comments:
          (data['comments'] as List<dynamic>? ?? [])
              .map((e) => RequestComment.fromMap(e as Map<String, dynamic>))
              .toList(),
      fees:
          data['fees'] != null
              ? RequestFees.fromMap(data['fees'] as Map<String, dynamic>)
              : null,
      paymentStatus: PaymentStatus.values.firstWhere(
        (e) => e.toString() == data['paymentStatus'],
        orElse: () => PaymentStatus.notRequired,
      ),
      paymentInfo:
          data['paymentInfo'] != null
              ? PaymentInfo.fromMap(data['paymentInfo'] as Map<String, dynamic>)
              : null,
      serviceRating:
          data['serviceRating'] != null
              ? ServiceRating.fromMap(
                data['serviceRating'] as Map<String, dynamic>,
              )
              : null,
      adminNotes: data['adminNotes'],
      internalNotes: data['internalNotes'],
      tags: List<String>.from(data['tags'] ?? []),
      privacyLevel: PrivacyLevel.values.firstWhere(
        (e) => e.toString() == data['privacyLevel'],
        orElse: () => PrivacyLevel.normal,
      ),
      isUrgent: data['isUrgent'] ?? false,
      requiresSpecialAttention: data['requiresSpecialAttention'] ?? false,
      notificationsSent: data['notificationsSent'] ?? false,
      lastNotificationSent:
          (data['lastNotificationSent'] as Timestamp?)?.toDate(),
      followUpCount: data['followUpCount'] ?? 0,
      nextFollowUpDate: (data['nextFollowUpDate'] as Timestamp?)?.toDate(),
      relatedRequestIds: List<String>.from(data['relatedRequestIds'] ?? []),
      originalRequestId: data['originalRequestId'],
      isRenewal: data['isRenewal'] ?? false,
      renewalCount: data['renewalCount'] ?? 0,
      expiryDate: (data['expiryDate'] as Timestamp?)?.toDate(),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
    );
  }

  /// تحويل النموذج إلى خريطة للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'requestNumber': requestNumber,
      'requesterId': requesterId,
      'requesterName': requesterName,
      'requesterType': requesterType.toString(),
      'studentId': studentId,
      'studentName': studentName,
      'classId': classId,
      'className': className,
      'requestType': requestType.toString(),
      'category': category.toString(),
      'priority': priority.toString(),
      'status': status.toString(),
      'currentStage': currentStage.toString(),
      'title': title,
      'description': description,
      'reason': reason,
      'additionalDetails': additionalDetails,
      'submittedAt': Timestamp.fromDate(submittedAt),
      'requestedCompletionDate':
          requestedCompletionDate != null
              ? Timestamp.fromDate(requestedCompletionDate!)
              : null,
      'estimatedCompletionDate':
          estimatedCompletionDate != null
              ? Timestamp.fromDate(estimatedCompletionDate!)
              : null,
      'actualCompletionDate':
          actualCompletionDate != null
              ? Timestamp.fromDate(actualCompletionDate!)
              : null,
      'lastUpdated': Timestamp.fromDate(lastUpdated),
      'lastUpdatedBy': lastUpdatedBy,
      'requiredDocuments': requiredDocuments.map((e) => e.toMap()).toList(),
      'attachments': attachments.map((e) => e.toMap()).toList(),
      'approvalSteps': approvalSteps.map((e) => e.toMap()).toList(),
      'currentApprovalStep': currentApprovalStep?.toMap(),
      'actions': actions.map((e) => e.toMap()).toList(),
      'comments': comments.map((e) => e.toMap()).toList(),
      'fees': fees?.toMap(),
      'paymentStatus': paymentStatus.toString(),
      'paymentInfo': paymentInfo?.toMap(),
      'serviceRating': serviceRating?.toMap(),
      'adminNotes': adminNotes,
      'internalNotes': internalNotes,
      'tags': tags,
      'privacyLevel': privacyLevel.toString(),
      'isUrgent': isUrgent,
      'requiresSpecialAttention': requiresSpecialAttention,
      'notificationsSent': notificationsSent,
      'lastNotificationSent':
          lastNotificationSent != null
              ? Timestamp.fromDate(lastNotificationSent!)
              : null,
      'followUpCount': followUpCount,
      'nextFollowUpDate':
          nextFollowUpDate != null
              ? Timestamp.fromDate(nextFollowUpDate!)
              : null,
      'relatedRequestIds': relatedRequestIds,
      'originalRequestId': originalRequestId,
      'isRenewal': isRenewal,
      'renewalCount': renewalCount,
      'expiryDate': expiryDate != null ? Timestamp.fromDate(expiryDate!) : null,
      'metadata': metadata,
    };
  }

  /// إنشاء نسخة محدثة من الطلب
  RequestManagementModel copyWith({
    String? requestNumber,
    String? requesterName,
    String? studentName,
    String? className,
    RequestType? requestType,
    RequestCategory? category,
    RequestPriority? priority,
    RequestStatus? status,
    ProcessingStage? currentStage,
    String? title,
    String? description,
    String? reason,
    String? additionalDetails,
    DateTime? requestedCompletionDate,
    DateTime? estimatedCompletionDate,
    DateTime? actualCompletionDate,
    DateTime? lastUpdated,
    String? lastUpdatedBy,
    List<RequiredDocument>? requiredDocuments,
    List<RequestAttachment>? attachments,
    List<ApprovalStep>? approvalSteps,
    ApprovalStep? currentApprovalStep,
    List<RequestAction>? actions,
    List<RequestComment>? comments,
    RequestFees? fees,
    PaymentStatus? paymentStatus,
    PaymentInfo? paymentInfo,
    ServiceRating? serviceRating,
    String? adminNotes,
    String? internalNotes,
    List<String>? tags,
    PrivacyLevel? privacyLevel,
    bool? isUrgent,
    bool? requiresSpecialAttention,
    bool? notificationsSent,
    DateTime? lastNotificationSent,
    int? followUpCount,
    DateTime? nextFollowUpDate,
    List<String>? relatedRequestIds,
    bool? isRenewal,
    int? renewalCount,
    DateTime? expiryDate,
    Map<String, dynamic>? metadata,
  }) {
    return RequestManagementModel(
      id: id,
      requestNumber: requestNumber ?? this.requestNumber,
      requesterId: requesterId,
      requesterName: requesterName ?? this.requesterName,
      requesterType: requesterType,
      studentId: studentId,
      studentName: studentName ?? this.studentName,
      classId: classId,
      className: className ?? this.className,
      requestType: requestType ?? this.requestType,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      currentStage: currentStage ?? this.currentStage,
      title: title ?? this.title,
      description: description ?? this.description,
      reason: reason ?? this.reason,
      additionalDetails: additionalDetails ?? this.additionalDetails,
      submittedAt: submittedAt,
      requestedCompletionDate:
          requestedCompletionDate ?? this.requestedCompletionDate,
      estimatedCompletionDate:
          estimatedCompletionDate ?? this.estimatedCompletionDate,
      actualCompletionDate: actualCompletionDate ?? this.actualCompletionDate,
      lastUpdated: lastUpdated ?? DateTime.now(),
      lastUpdatedBy: lastUpdatedBy ?? this.lastUpdatedBy,
      requiredDocuments: requiredDocuments ?? this.requiredDocuments,
      attachments: attachments ?? this.attachments,
      approvalSteps: approvalSteps ?? this.approvalSteps,
      currentApprovalStep: currentApprovalStep ?? this.currentApprovalStep,
      actions: actions ?? this.actions,
      comments: comments ?? this.comments,
      fees: fees ?? this.fees,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      paymentInfo: paymentInfo ?? this.paymentInfo,
      serviceRating: serviceRating ?? this.serviceRating,
      adminNotes: adminNotes ?? this.adminNotes,
      internalNotes: internalNotes ?? this.internalNotes,
      tags: tags ?? this.tags,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      isUrgent: isUrgent ?? this.isUrgent,
      requiresSpecialAttention:
          requiresSpecialAttention ?? this.requiresSpecialAttention,
      notificationsSent: notificationsSent ?? this.notificationsSent,
      lastNotificationSent: lastNotificationSent ?? this.lastNotificationSent,
      followUpCount: followUpCount ?? this.followUpCount,
      nextFollowUpDate: nextFollowUpDate ?? this.nextFollowUpDate,
      relatedRequestIds: relatedRequestIds ?? this.relatedRequestIds,
      originalRequestId: originalRequestId,
      isRenewal: isRenewal ?? this.isRenewal,
      renewalCount: renewalCount ?? this.renewalCount,
      expiryDate: expiryDate ?? this.expiryDate,
      metadata: metadata ?? this.metadata,
    );
  }

  /// التحقق من صحة الطلب
  bool get isValid {
    return requesterId.isNotEmpty &&
        requesterName.isNotEmpty &&
        title.isNotEmpty &&
        description.isNotEmpty &&
        reason.isNotEmpty;
  }

  /// التحقق من إمكانية تعديل الطلب
  bool get canBeModified {
    return status == RequestStatus.submitted ||
        status == RequestStatus.underReview ||
        status == RequestStatus.pendingDocuments;
  }

  /// التحقق من إمكانية إلغاء الطلب
  bool get canBeCancelled {
    return status != RequestStatus.completed &&
        status != RequestStatus.cancelled &&
        status != RequestStatus.rejected;
  }

  /// التحقق من اكتمال المستندات المطلوبة
  bool get hasAllRequiredDocuments {
    if (requiredDocuments.isEmpty) return true;

    for (final required in requiredDocuments) {
      final hasDocument = attachments.any(
        (attachment) => attachment.documentType == required.documentType,
      );
      if (!hasDocument) return false;
    }
    return true;
  }

  /// التحقق من اكتمال جميع الموافقات
  bool get hasAllApprovals {
    if (approvalSteps.isEmpty) return true;

    return approvalSteps.every(
      (step) => step.status == ApprovalStatus.approved,
    );
  }

  /// الحصول على نسبة إنجاز الطلب
  double get completionPercentage {
    if (status == RequestStatus.completed) return 100.0;
    if (status == RequestStatus.cancelled || status == RequestStatus.rejected)
      return 0.0;

    int totalSteps = 4; // مراحل أساسية: تقديم، مراجعة، موافقة، إنجاز
    int completedSteps = 0;

    if (status.index >= RequestStatus.submitted.index) completedSteps++;
    if (status.index >= RequestStatus.underReview.index) completedSteps++;
    if (hasAllApprovals) completedSteps++;
    if (status == RequestStatus.completed) completedSteps++;

    return (completedSteps / totalSteps) * 100;
  }

  /// الحصول على الوقت المتبقي لإنجاز الطلب
  Duration? get timeRemaining {
    if (estimatedCompletionDate == null) return null;
    if (status == RequestStatus.completed) return null;

    final now = DateTime.now();
    if (estimatedCompletionDate!.isBefore(now)) return null;

    return estimatedCompletionDate!.difference(now);
  }

  /// التحقق من تأخر الطلب
  bool get isOverdue {
    if (estimatedCompletionDate == null) return false;
    if (status == RequestStatus.completed) return false;

    return DateTime.now().isAfter(estimatedCompletionDate!);
  }

  /// الحصول على وصف حالة الطلب
  String get statusDescription {
    switch (status) {
      case RequestStatus.draft:
        return 'مسودة';
      case RequestStatus.submitted:
        return 'مُقدم';
      case RequestStatus.underReview:
        return 'قيد المراجعة';
      case RequestStatus.pendingDocuments:
        return 'في انتظار المستندات';
      case RequestStatus.pendingApproval:
        return 'في انتظار الموافقة';
      case RequestStatus.approved:
        return 'موافق عليه';
      case RequestStatus.inProgress:
        return 'قيد التنفيذ';
      case RequestStatus.completed:
        return 'مكتمل';
      case RequestStatus.rejected:
        return 'مرفوض';
      case RequestStatus.cancelled:
        return 'ملغي';
      case RequestStatus.onHold:
        return 'معلق';
    }
  }

  /// الحصول على وصف أولوية الطلب
  String get priorityDescription {
    switch (priority) {
      case RequestPriority.low:
        return 'منخفضة';
      case RequestPriority.normal:
        return 'عادية';
      case RequestPriority.high:
        return 'عالية';
      case RequestPriority.urgent:
        return 'عاجلة';
      case RequestPriority.critical:
        return 'حرجة';
    }
  }

  /// الحصول على لون مناسب لحالة الطلب
  String get statusColor {
    switch (status) {
      case RequestStatus.draft:
        return '#9E9E9E'; // رمادي
      case RequestStatus.submitted:
        return '#2196F3'; // أزرق
      case RequestStatus.underReview:
        return '#FF9800'; // برتقالي
      case RequestStatus.pendingDocuments:
        return '#FFC107'; // أصفر
      case RequestStatus.pendingApproval:
        return '#9C27B0'; // بنفسجي
      case RequestStatus.approved:
        return '#4CAF50'; // أخضر فاتح
      case RequestStatus.inProgress:
        return '#00BCD4'; // سماوي
      case RequestStatus.completed:
        return '#4CAF50'; // أخضر
      case RequestStatus.rejected:
        return '#F44336'; // أحمر
      case RequestStatus.cancelled:
        return '#607D8B'; // رمادي داكن
      case RequestStatus.onHold:
        return '#795548'; // بني
    }
  }

  /// الحصول على لون مناسب لأولوية الطلب
  String get priorityColor {
    switch (priority) {
      case RequestPriority.low:
        return '#4CAF50'; // أخضر
      case RequestPriority.normal:
        return '#2196F3'; // أزرق
      case RequestPriority.high:
        return '#FF9800'; // برتقالي
      case RequestPriority.urgent:
        return '#F44336'; // أحمر
      case RequestPriority.critical:
        return '#9C27B0'; // بنفسجي
    }
  }
}

// ===================================================================
// الفئات المساعدة والتعدادات
// ===================================================================

/// وثيقة مطلوبة للطلب
class RequiredDocument {
  /// نوع الوثيقة
  final DocumentType documentType;

  /// اسم الوثيقة
  final String name;

  /// وصف الوثيقة
  final String description;

  /// هل الوثيقة إجبارية؟
  final bool isRequired;

  /// تنسيقات الملفات المقبولة
  final List<String> acceptedFormats;

  /// الحد الأقصى لحجم الملف (بالميجابايت)
  final double maxSizeMB;

  const RequiredDocument({
    required this.documentType,
    required this.name,
    required this.description,
    this.isRequired = true,
    this.acceptedFormats = const ['pdf', 'jpg', 'png', 'doc', 'docx'],
    this.maxSizeMB = 5.0,
  });

  factory RequiredDocument.fromMap(Map<String, dynamic> map) {
    return RequiredDocument(
      documentType: DocumentType.values.firstWhere(
        (e) => e.toString() == map['documentType'],
        orElse: () => DocumentType.other,
      ),
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      isRequired: map['isRequired'] ?? true,
      acceptedFormats: List<String>.from(map['acceptedFormats'] ?? []),
      maxSizeMB: (map['maxSizeMB'] ?? 5.0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'documentType': documentType.toString(),
      'name': name,
      'description': description,
      'isRequired': isRequired,
      'acceptedFormats': acceptedFormats,
      'maxSizeMB': maxSizeMB,
    };
  }
}

/// مرفق الطلب
class RequestAttachment {
  /// معرف المرفق
  final String id;

  /// اسم الملف
  final String fileName;

  /// رابط الملف
  final String fileUrl;

  /// نوع الوثيقة
  final DocumentType documentType;

  /// نوع الملف
  final String fileType;

  /// حجم الملف بالبايت
  final int fileSizeBytes;

  /// تاريخ الرفع
  final DateTime uploadedAt;

  /// معرف من قام بالرفع
  final String uploadedBy;

  /// ملاحظات على المرفق
  final String? notes;

  const RequestAttachment({
    required this.id,
    required this.fileName,
    required this.fileUrl,
    required this.documentType,
    required this.fileType,
    required this.fileSizeBytes,
    required this.uploadedAt,
    required this.uploadedBy,
    this.notes,
  });

  factory RequestAttachment.fromMap(Map<String, dynamic> map) {
    return RequestAttachment(
      id: map['id'] ?? '',
      fileName: map['fileName'] ?? '',
      fileUrl: map['fileUrl'] ?? '',
      documentType: DocumentType.values.firstWhere(
        (e) => e.toString() == map['documentType'],
        orElse: () => DocumentType.other,
      ),
      fileType: map['fileType'] ?? '',
      fileSizeBytes: map['fileSizeBytes'] ?? 0,
      uploadedAt: (map['uploadedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      uploadedBy: map['uploadedBy'] ?? '',
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fileName': fileName,
      'fileUrl': fileUrl,
      'documentType': documentType.toString(),
      'fileType': fileType,
      'fileSizeBytes': fileSizeBytes,
      'uploadedAt': Timestamp.fromDate(uploadedAt),
      'uploadedBy': uploadedBy,
      'notes': notes,
    };
  }

  /// الحصول على حجم الملف بصيغة قابلة للقراءة
  String get readableFileSize {
    if (fileSizeBytes < 1024) return '$fileSizeBytes B';
    if (fileSizeBytes < 1024 * 1024)
      return '${(fileSizeBytes / 1024).toStringAsFixed(1)} KB';
    return '${(fileSizeBytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
}

/// خطوة الموافقة
class ApprovalStep {
  /// معرف خطوة الموافقة
  final String id;

  /// اسم خطوة الموافقة
  final String name;

  /// وصف خطوة الموافقة
  final String description;

  /// ترتيب الخطوة
  final int order;

  /// معرف المسؤول عن الموافقة
  final String approverId;

  /// اسم المسؤول عن الموافقة
  final String approverName;

  /// دور المسؤول عن الموافقة
  final String approverRole;

  /// حالة الموافقة
  final ApprovalStatus status;

  /// تاريخ الموافقة أو الرفض
  final DateTime? actionDate;

  /// ملاحظات الموافقة أو الرفض
  final String? notes;

  /// هل هذه الخطوة إجبارية؟
  final bool isRequired;

  const ApprovalStep({
    required this.id,
    required this.name,
    required this.description,
    required this.order,
    required this.approverId,
    required this.approverName,
    required this.approverRole,
    this.status = ApprovalStatus.pending,
    this.actionDate,
    this.notes,
    this.isRequired = true,
  });

  factory ApprovalStep.fromMap(Map<String, dynamic> map) {
    return ApprovalStep(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      order: map['order'] ?? 0,
      approverId: map['approverId'] ?? '',
      approverName: map['approverName'] ?? '',
      approverRole: map['approverRole'] ?? '',
      status: ApprovalStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => ApprovalStatus.pending,
      ),
      actionDate: (map['actionDate'] as Timestamp?)?.toDate(),
      notes: map['notes'],
      isRequired: map['isRequired'] ?? true,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'order': order,
      'approverId': approverId,
      'approverName': approverName,
      'approverRole': approverRole,
      'status': status.toString(),
      'actionDate': actionDate != null ? Timestamp.fromDate(actionDate!) : null,
      'notes': notes,
      'isRequired': isRequired,
    };
  }

  /// إنشاء نسخة محدثة من خطوة الموافقة
  ApprovalStep copyWith({
    String? name,
    String? description,
    int? order,
    String? approverId,
    String? approverName,
    String? approverRole,
    ApprovalStatus? status,
    DateTime? actionDate,
    String? notes,
    bool? isRequired,
  }) {
    return ApprovalStep(
      id: id,
      name: name ?? this.name,
      description: description ?? this.description,
      order: order ?? this.order,
      approverId: approverId ?? this.approverId,
      approverName: approverName ?? this.approverName,
      approverRole: approverRole ?? this.approverRole,
      status: status ?? this.status,
      actionDate: actionDate ?? this.actionDate,
      notes: notes ?? this.notes,
      isRequired: isRequired ?? this.isRequired,
    );
  }
}

/// إجراء متخذ على الطلب
class RequestAction {
  /// معرف الإجراء
  final String id;

  /// نوع الإجراء
  final ActionType actionType;

  /// وصف الإجراء
  final String description;

  /// معرف من قام بالإجراء
  final String performedBy;

  /// اسم من قام بالإجراء
  final String performedByName;

  /// تاريخ الإجراء
  final DateTime performedAt;

  /// ملاحظات على الإجراء
  final String? notes;

  const RequestAction({
    required this.id,
    required this.actionType,
    required this.description,
    required this.performedBy,
    required this.performedByName,
    required this.performedAt,
    this.notes,
  });

  factory RequestAction.fromMap(Map<String, dynamic> map) {
    return RequestAction(
      id: map['id'] ?? '',
      actionType: ActionType.values.firstWhere(
        (e) => e.toString() == map['actionType'],
        orElse: () => ActionType.other,
      ),
      description: map['description'] ?? '',
      performedBy: map['performedBy'] ?? '',
      performedByName: map['performedByName'] ?? '',
      performedAt:
          (map['performedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'actionType': actionType.toString(),
      'description': description,
      'performedBy': performedBy,
      'performedByName': performedByName,
      'performedAt': Timestamp.fromDate(performedAt),
      'notes': notes,
    };
  }
}

/// تعليق على الطلب
class RequestComment {
  /// معرف التعليق
  final String id;

  /// نص التعليق
  final String comment;

  /// معرف كاتب التعليق
  final String authorId;

  /// اسم كاتب التعليق
  final String authorName;

  /// دور كاتب التعليق
  final String authorRole;

  /// تاريخ التعليق
  final DateTime createdAt;

  /// هل التعليق داخلي (لا يظهر للمستخدم)؟
  final bool isInternal;

  const RequestComment({
    required this.id,
    required this.comment,
    required this.authorId,
    required this.authorName,
    required this.authorRole,
    required this.createdAt,
    this.isInternal = false,
  });

  factory RequestComment.fromMap(Map<String, dynamic> map) {
    return RequestComment(
      id: map['id'] ?? '',
      comment: map['comment'] ?? '',
      authorId: map['authorId'] ?? '',
      authorName: map['authorName'] ?? '',
      authorRole: map['authorRole'] ?? '',
      createdAt: (map['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isInternal: map['isInternal'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'comment': comment,
      'authorId': authorId,
      'authorName': authorName,
      'authorRole': authorRole,
      'createdAt': Timestamp.fromDate(createdAt),
      'isInternal': isInternal,
    };
  }
}

/// رسوم الطلب
class RequestFees {
  /// المبلغ الأساسي
  final double baseAmount;

  /// الرسوم الإضافية
  final double additionalFees;

  /// الخصومات
  final double discounts;

  /// المبلغ الإجمالي
  final double totalAmount;

  /// العملة
  final String currency;

  /// وصف الرسوم
  final String description;

  const RequestFees({
    required this.baseAmount,
    this.additionalFees = 0.0,
    this.discounts = 0.0,
    required this.totalAmount,
    this.currency = 'SAR',
    required this.description,
  });

  factory RequestFees.fromMap(Map<String, dynamic> map) {
    return RequestFees(
      baseAmount: (map['baseAmount'] ?? 0.0).toDouble(),
      additionalFees: (map['additionalFees'] ?? 0.0).toDouble(),
      discounts: (map['discounts'] ?? 0.0).toDouble(),
      totalAmount: (map['totalAmount'] ?? 0.0).toDouble(),
      currency: map['currency'] ?? 'SAR',
      description: map['description'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'baseAmount': baseAmount,
      'additionalFees': additionalFees,
      'discounts': discounts,
      'totalAmount': totalAmount,
      'currency': currency,
      'description': description,
    };
  }
}

/// معلومات الدفع
class PaymentInfo {
  /// معرف المعاملة
  final String transactionId;

  /// طريقة الدفع
  final PaymentMethod paymentMethod;

  /// المبلغ المدفوع
  final double amountPaid;

  /// تاريخ الدفع
  final DateTime paymentDate;

  /// حالة الدفع
  final PaymentStatus status;

  /// ملاحظات الدفع
  final String? notes;

  const PaymentInfo({
    required this.transactionId,
    required this.paymentMethod,
    required this.amountPaid,
    required this.paymentDate,
    required this.status,
    this.notes,
  });

  factory PaymentInfo.fromMap(Map<String, dynamic> map) {
    return PaymentInfo(
      transactionId: map['transactionId'] ?? '',
      paymentMethod: PaymentMethod.values.firstWhere(
        (e) => e.toString() == map['paymentMethod'],
        orElse: () => PaymentMethod.cash,
      ),
      amountPaid: (map['amountPaid'] ?? 0.0).toDouble(),
      paymentDate:
          (map['paymentDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => PaymentStatus.pending,
      ),
      notes: map['notes'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'transactionId': transactionId,
      'paymentMethod': paymentMethod.toString(),
      'amountPaid': amountPaid,
      'paymentDate': Timestamp.fromDate(paymentDate),
      'status': status.toString(),
      'notes': notes,
    };
  }
}

/// تقييم جودة الخدمة
class ServiceRating {
  /// التقييم العام (من 1 إلى 5)
  final int overallRating;

  /// تقييم سرعة الاستجابة
  final int responseSpeedRating;

  /// تقييم جودة الخدمة
  final int serviceQualityRating;

  /// تقييم سهولة الاستخدام
  final int usabilityRating;

  /// تعليقات المستخدم
  final String? feedback;

  /// تاريخ التقييم
  final DateTime ratedAt;

  const ServiceRating({
    required this.overallRating,
    required this.responseSpeedRating,
    required this.serviceQualityRating,
    required this.usabilityRating,
    this.feedback,
    required this.ratedAt,
  });

  factory ServiceRating.fromMap(Map<String, dynamic> map) {
    return ServiceRating(
      overallRating: map['overallRating'] ?? 1,
      responseSpeedRating: map['responseSpeedRating'] ?? 1,
      serviceQualityRating: map['serviceQualityRating'] ?? 1,
      usabilityRating: map['usabilityRating'] ?? 1,
      feedback: map['feedback'],
      ratedAt: (map['ratedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'overallRating': overallRating,
      'responseSpeedRating': responseSpeedRating,
      'serviceQualityRating': serviceQualityRating,
      'usabilityRating': usabilityRating,
      'feedback': feedback,
      'ratedAt': Timestamp.fromDate(ratedAt),
    };
  }

  /// متوسط التقييم
  double get averageRating {
    return (overallRating +
            responseSpeedRating +
            serviceQualityRating +
            usabilityRating) /
        4.0;
  }
}

// ===================================================================
// التعدادات (Enums)
// ===================================================================

/// نوع مقدم الطلب
enum RequesterType {
  parent, // ولي أمر
  student, // طالب
  employee, // موظف
  teacher, // معلم
  admin, // إدارة
}

/// نوع الطلب
enum RequestType {
  certificate, // شهادة
  document, // وثيقة
  meeting, // اجتماع
  complaint, // شكوى
  suggestion, // اقتراح
  leave, // إجازة
  transfer, // نقل
  enrollment, // تسجيل
  withdrawal, // انسحاب
  transcript, // كشف درجات
  recommendation, // خطاب توصية
  verification, // تصديق
  other, // أخرى
}

/// فئة الطلب
enum RequestCategory {
  academic, // أكاديمي
  administrative, // إداري
  financial, // مالي
  medical, // طبي
  disciplinary, // تأديبي
  technical, // تقني
  social, // اجتماعي
  legal, // قانوني
  other, // أخرى
}

/// أولوية الطلب
enum RequestPriority {
  low, // منخفضة
  normal, // عادية
  high, // عالية
  urgent, // عاجلة
  critical, // حرجة
}

/// حالة الطلب
enum RequestStatus {
  draft, // مسودة
  submitted, // مُقدم
  underReview, // قيد المراجعة
  pendingDocuments, // في انتظار المستندات
  pendingApproval, // في انتظار الموافقة
  approved, // موافق عليه
  inProgress, // قيد التنفيذ
  completed, // مكتمل
  rejected, // مرفوض
  cancelled, // ملغي
  onHold, // معلق
}

/// مرحلة المعالجة
enum ProcessingStage {
  initial, // مرحلة أولية
  documentation, // مرحلة التوثيق
  review, // مرحلة المراجعة
  approval, // مرحلة الموافقة
  processing, // مرحلة المعالجة
  completion, // مرحلة الإنجاز
  delivery, // مرحلة التسليم
}

/// نوع الوثيقة
enum DocumentType {
  nationalId, // الهوية الوطنية
  passport, // جواز السفر
  birthCertificate, // شهادة الميلاد
  medicalReport, // تقرير طبي
  transcript, // كشف درجات
  photo, // صورة شخصية
  contract, // عقد
  authorization, // تفويض
  bankStatement, // كشف حساب بنكي
  other, // أخرى
}

/// حالة الموافقة
enum ApprovalStatus {
  pending, // في الانتظار
  approved, // موافق عليه
  rejected, // مرفوض
  conditionalApproval, // موافقة مشروطة
}

/// نوع الإجراء
enum ActionType {
  created, // إنشاء
  updated, // تحديث
  submitted, // تقديم
  reviewed, // مراجعة
  approved, // موافقة
  rejected, // رفض
  cancelled, // إلغاء
  completed, // إنجاز
  documentUploaded, // رفع وثيقة
  commentAdded, // إضافة تعليق
  statusChanged, // تغيير الحالة
  assignmentChanged, // تغيير المسؤول
  reminderSent, // إرسال تذكير
  other, // أخرى
}

/// حالة الدفع
enum PaymentStatus {
  notRequired, // غير مطلوب
  pending, // في الانتظار
  paid, // مدفوع
  partiallyPaid, // مدفوع جزئياً
  refunded, // مسترد
  failed, // فشل
}

/// طريقة الدفع
enum PaymentMethod {
  cash, // نقداً
  creditCard, // بطاقة ائتمان
  debitCard, // بطاقة خصم
  bankTransfer, // تحويل بنكي
  onlinePayment, // دفع إلكتروني
  check, // شيك
  other, // أخرى
}

/// مستوى الخصوصية
enum PrivacyLevel {
  normal, // عادي
  confidential, // سري
  restricted, // مقيد
}
