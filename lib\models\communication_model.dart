import 'package:cloud_firestore/cloud_firestore.dart';

class CommunicationModel {
  final String id;
  final String guardianId;
  final String message;
  final String reply;
  final bool isRead;
  final DateTime timestamp;

  CommunicationModel({
    required this.id,
    required this.guardianId,
    required this.message,
    required this.reply,
    required this.isRead,
    required this.timestamp,
  });

  factory CommunicationModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return CommunicationModel(
      id: doc.id,
      guardianId: data['guardianId'] ?? '',
      message: data['message'] ?? '',
      reply: data['reply'] ?? '',
      isRead: data['isRead'] ?? false,
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'guardianId': guardianId,
      'message': message,
      'reply': reply,
      'isRead': isRead,
      'timestamp': Timestamp.fromDate(timestamp),
    };
  }
}
