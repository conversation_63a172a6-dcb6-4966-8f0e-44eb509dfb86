import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class AssignStudentsDialog extends ConsumerStatefulWidget {
  final String classId;
  final List<String> assignedStudentIds;

  const AssignStudentsDialog({super.key, required this.classId, required this.assignedStudentIds});

  @override
  ConsumerState<AssignStudentsDialog> createState() => _AssignStudentsDialogState();
}

class _AssignStudentsDialogState extends ConsumerState<AssignStudentsDialog> {
  late Set<String> _selectedStudentIds;

  @override
  void initState() {
    super.initState();
    _selectedStudentIds = widget.assignedStudentIds.toSet();
  }

  void _submit() async {
    try {
      await ref.read(firebaseServiceProvider).assignStudentsToClass(
        widget.classId,
        _selectedStudentIds.toList(),
      );
      if (mounted) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم تسكين الطلاب بنجاح')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('حدث خطأ أثناء تسكين الطلاب: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('تسكين الطلاب في الفصل'),
      content: SizedBox(
        width: double.maxFinite,
        child: Consumer(
          builder: (context, ref, child) {
            final allStudentsAsyncValue = ref.watch(studentsStreamProvider);
            return allStudentsAsyncValue.when(
              data: (allStudents) {
                return ListView.builder(
                  itemCount: allStudents.length,
                  itemBuilder: (context, index) {
                    final student = allStudents[index];
                    final isSelected = _selectedStudentIds.contains(student.id);
                    return CheckboxListTile(
                      title: Text(student.name),
                      value: isSelected,
                      onChanged: (bool? value) {
                        setState(() {
                          if (value == true) {
                            _selectedStudentIds.add(student.id);
                          } else {
                            _selectedStudentIds.remove(student.id);
                          }
                        });
                      },
                    );
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => Text('خطأ: $err'),
            );
          },
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: _submit,
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
