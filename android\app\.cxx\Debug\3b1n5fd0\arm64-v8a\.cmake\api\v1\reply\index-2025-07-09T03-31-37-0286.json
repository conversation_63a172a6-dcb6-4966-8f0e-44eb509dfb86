{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cmake.exe", "cpack": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/cpack.exe", "ctest": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/bin/ctest.exe", "root": "F:/baha/Baha/Android/Sdk/cmake/3.22.1/share/cmake-3.22"}, "version": {"isDirty": true, "major": 3, "minor": 22, "patch": 1, "string": "3.22.1-g37088a8-dirty", "suffix": "g37088a8"}}, "objects": [{"jsonFile": "codemodel-v2-27b37d14bb8001d134f2.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}, {"jsonFile": "cache-v2-348096f581639d59e96e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-16b9bae51d27c8df465f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-348096f581639d59e96e.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-16b9bae51d27c8df465f.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-27b37d14bb8001d134f2.json", "kind": "codemodel", "version": {"major": 2, "minor": 3}}}}}