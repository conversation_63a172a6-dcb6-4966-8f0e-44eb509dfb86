import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/subject_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/providers/subject_providers.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class SubjectFormDialog extends ConsumerStatefulWidget {
  final Subject? subject;

  const SubjectFormDialog({super.key, this.subject});

  @override
  ConsumerState<SubjectFormDialog> createState() => _SubjectFormDialogState();
}

class _SubjectFormDialogState extends ConsumerState<SubjectFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  String? _selectedClassId;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.subject?.name);
    _selectedClassId = widget.subject?.classId;
  }

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      setState(() => _isLoading = true);
      try {
        final firebaseService = ref.read(firebaseServiceProvider);
        if (widget.subject == null) {
          await firebaseService.addSubject(
              _nameController.text, _selectedClassId!);
          showSuccessSnackBar(context, 'تمت إضافة المادة بنجاح');
        } else {
          await firebaseService.updateSubject(
              widget.subject!.id, _nameController.text, _selectedClassId!);
          showSuccessSnackBar(context, 'تم تحديث المادة بنجاح');
        }
        Navigator.of(context).pop();
      } catch (e) {
        showErrorSnackBar(context, 'فشل حفظ المادة: $e');
      } finally {
        if (mounted) {
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final classesAsync = ref.watch(classesForSubjectsProvider);
    return AlertDialog(
      title: Text(widget.subject == null ? 'إضافة مادة جديدة' : 'تعديل المادة'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'اسم المادة'),
                validator: (value) =>
                    value!.isEmpty ? 'الرجاء إدخال اسم المادة' : null,
              ),
              const SizedBox(height: 20),
              classesAsync.when(
                data: (classes) => DropdownButtonFormField<String>(
                  value: _selectedClassId,
                  hint: const Text('اختر الصف'),
                  decoration: const InputDecoration(
                    labelText: 'الصف',
                    border: OutlineInputBorder(),
                  ),
                  items: [
                    const DropdownMenuItem<String>(
                      value: 'all',
                      child: Text('مادة عامة (لجميع الصفوف)'),
                    ),
                    ...classes.map((ClassModel classModel) {
                      return DropdownMenuItem<String>(
                        value: classModel.id,
                        child: Text(classModel.name),
                      );
                    }).toList(),
                  ],
                  onChanged: (value) {
                    setState(() {
                      _selectedClassId = value;
                    });
                  },
                  validator: (value) =>
                      value == null ? 'الرجاء اختيار صف' : null,
                ),
                loading: () => const LoadingIndicator(),
                error: (err, stack) => Text('خطأ: $err'),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _submit,
          child: _isLoading
              ? const SizedBox(
                  width: 20, height: 20, child: CircularProgressIndicator())
              : const Text('حفظ'),
        ),
      ],
    );
  }
}
