
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

Future<void> showConfirmDeleteDialog(BuildContext context, WidgetRef ref, StudentModel student) {
  return showDialog(
    context: context,
    builder: (context) => AlertDialog(
      title: const Text('تأكيد الحذف'),
      content: Text('هل أنت متأكد من رغبتك في حذف الطالب ${student.name}؟'),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: () {
            ref.read(firebaseServiceProvider).deleteStudent(student);
            Navigator.of(context).pop();
          },
          style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
          child: const Text('حذف'),
        ),
      ],
    ),
  );
}
