import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/fees_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/admin_screens/widgets/fee_type_form_dialog.dart';

class FeesTypesManagementScreen extends ConsumerWidget {
  const FeesTypesManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final feeTypesAsyncValue = ref.watch(feeTypesStreamProvider);
    final filteredFeeTypes = ref.watch(filteredFeeTypesProvider);
    final searchQueryController = TextEditingController();

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة أنواع الرسوم'),
        centerTitle: true,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: searchQueryController,
              decoration: const InputDecoration(
                labelText: 'ابحث عن نوع الرسوم...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                ref.read(feeTypeSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          Expanded(
            child: feeTypesAsyncValue.when(
              data: (_) { // We use the filtered provider for the UI
                if (filteredFeeTypes.isEmpty) {
                  return const Center(child: Text('لا توجد أنواع رسوم تطابق البحث.'));
                }
                return ListView.builder(
                  itemCount: filteredFeeTypes.length,
                  itemBuilder: (context, index) {
                    final feeType = filteredFeeTypes[index];
                    return Card(
                      margin: const EdgeInsets.all(8.0),
                      child: ListTile(
                        title: Text(feeType.name),
                        subtitle: Text('المبلغ: ${feeType.defaultValue.toStringAsFixed(2)}'),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon: const Icon(Icons.edit, color: Colors.blue),
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (_) => FeeTypeFormDialog(feeType: feeType),
                                );
                              },
                            ),
                            IconButton(
                              icon: const Icon(Icons.delete, color: Colors.red),
                              onPressed: () => _confirmDeleteDialog(context, ref, feeType.id),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (_) => const FeeTypeFormDialog(),
          );
        },
        child: const Icon(Icons.add),
      ),
    );
  }

  void _confirmDeleteDialog(BuildContext context, WidgetRef ref, String feeTypeId) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من حذف نوع الرسوم هذا؟'),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                try {
                  await ref.read(firebaseServiceProvider).deleteFeeType(feeTypeId);
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('تم الحذف بنجاح')),
                  );
                } catch (e) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('حدث خطأ أثناء الحذف: $e')),
                  );
                }
              },
              child: const Text('حذف'),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            ),
          ],
        );
      },
    );
  }
}
