import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج يمثل نوع الرسوم في النظام
class FeeTypeModel {
  final String id; // معرف المستند في Firestore
  final String name; // اسم نوع الرسوم
  final double defaultValue; // القيمة الافتراضية للرسوم
  final String? description; // وصف اختياري
  final bool isRequired; // هل الرسوم إلزامية

  FeeTypeModel({
    required this.id,
    required this.name,
    required this.defaultValue,
    this.description,
    required this.isRequired,
  });

  /// تحويل خريطة البيانات (من Firestore) إلى كائن FeeTypeModel
  factory FeeTypeModel.fromMap(Map<String, dynamic> data, String documentId) {
    return FeeTypeModel(
      id: documentId,
      name: data['name'] ?? '',
      // قراءة default_amount من Firestore وتحويلها إلى defaultValue
      defaultValue: (data['default_amount'] as num?)?.toDouble() ?? 0.0,
      description: data['description'],
      isRequired: data['is_required'] ?? false,
    );
  }

  /// تحويل كائن FeeTypeModel إلى خريطة لإضافتها في Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      // تخزين defaultValue كـ default_amount في Firestore
      'default_amount': defaultValue,
      'description': description,
      'is_required': isRequired,
    };
  }
}