
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات ولي الأمر
class GuardianModel {
  final String id; // مُعرّف المستند الفريد من Firestore
  final String name; // الاسم الكامل
  final String email; // البريد الإلكتروني
  final String phoneNumber; // رقم الهاتف
  final Timestamp createdAt; // تاريخ إنشاء الحساب
  final List<String> linkedStudents; // قائمة بمعرّفات الطلاب المرتبطين

  GuardianModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    required this.createdAt,
    required this.linkedStudents,
  });

  /// دالة لتحويل البيانات من Firestore إلى كائن GuardianModel
  factory GuardianModel.fromMap(Map<String, dynamic> data, String documentId) {
    // تحويل قائمة الطلاب المرتبطين بأمان
    final List<dynamic> studentsFromDb = data['linked_students'] ?? [];
    final List<String> studentUids = studentsFromDb.map((e) => e.toString()).toList();

    return GuardianModel(
      id: documentId,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phoneNumber: data['phone_number'] ?? '',
      createdAt: data['created_at'] ?? Timestamp.now(),
      linkedStudents: studentUids,
    );
  }

  /// دالة لتحويل كائن GuardianModel إلى Map لإرساله إلى Firestore
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'email': email,
      'phone_number': phoneNumber,
      'created_at': createdAt,
      'linked_students': linkedStudents,
    };
  }
}
