import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/exam_model.dart';

/// نموذج درجات الطالب المفصل
/// 
/// هذا النموذج يحتوي على جميع المعلومات المطلوبة لعرض
/// نتائج الطالب بشكل شامل ومفصل في شاشة النتائج
/// 
/// يتضمن:
/// - معلومات الطالب والامتحان
/// - الدرجة المحصلة والعظمى
/// - معلومات المادة والمعلم
/// - تاريخ الامتحان ونوعه
/// - ملاحظات المعلم والحالة
/// - إحصائيات إضافية للمقارنة
class StudentGradeModel {
  /// معرف فريد للدرجة
  final String id;
  
  /// معرف الطالب صاحب الدرجة
  final String studentId;
  
  /// معرف الامتحان المرتبط بالدرجة
  final String examId;
  
  /// اسم الامتحان
  final String examName;
  
  /// معرف المادة
  final String subjectId;
  
  /// اسم المادة
  final String subjectName;
  
  /// معرف المعلم الذي أدخل الدرجة
  final String teacherId;
  
  /// اسم المعلم
  final String teacherName;
  
  /// الدرجة المحصلة للطالب
  final double grade;
  
  /// الدرجة العظمى للامتحان
  final double maxGrade;
  
  /// نسبة الدرجة المئوية
  double get percentage => (grade / maxGrade) * 100;
  
  /// تاريخ إجراء الامتحان
  final DateTime examDate;
  
  /// نوع الامتحان (نهائي، نصفي، شهري، واجب)
  final ExamType examType;
  
  /// مدة الامتحان بالدقائق
  final int examDuration;
  
  /// هل تم نشر الدرجة للطالب؟
  final bool isPublished;
  
  /// تاريخ نشر الدرجة
  final DateTime? publishedAt;
  
  /// ملاحظات المعلم على أداء الطالب
  final String? teacherNotes;
  
  /// تقييم المعلم لأداء الطالب (ممتاز، جيد جداً، جيد، مقبول، ضعيف)
  final String? performanceRating;
  
  /// متوسط درجات الصف في هذا الامتحان
  final double? classAverage;
  
  /// أعلى درجة في الصف
  final double? highestInClass;
  
  /// أقل درجة في الصف
  final double? lowestInClass;
  
  /// ترتيب الطالب في الصف لهذا الامتحان
  final int? rankInClass;
  
  /// إجمالي عدد الطلاب الذين شاركوا في الامتحان
  final int? totalStudents;
  
  /// الفصل الدراسي (الأول، الثاني، الصيفي)
  final String semester;
  
  /// السنة الدراسية
  final String academicYear;
  
  /// تاريخ إنشاء السجل
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث
  final DateTime updatedAt;

  const StudentGradeModel({
    required this.id,
    required this.studentId,
    required this.examId,
    required this.examName,
    required this.subjectId,
    required this.subjectName,
    required this.teacherId,
    required this.teacherName,
    required this.grade,
    required this.maxGrade,
    required this.examDate,
    required this.examType,
    required this.examDuration,
    required this.isPublished,
    this.publishedAt,
    this.teacherNotes,
    this.performanceRating,
    this.classAverage,
    this.highestInClass,
    this.lowestInClass,
    this.rankInClass,
    this.totalStudents,
    required this.semester,
    required this.academicYear,
    required this.createdAt,
    required this.updatedAt,
  });

  /// إنشاء نموذج من بيانات Firestore
  factory StudentGradeModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StudentGradeModel(
      id: doc.id,
      studentId: data['studentId'] as String? ?? '',
      examId: data['examId'] as String? ?? '',
      examName: data['examName'] as String? ?? '',
      subjectId: data['subjectId'] as String? ?? '',
      subjectName: data['subjectName'] as String? ?? '',
      teacherId: data['teacherId'] as String? ?? '',
      teacherName: data['teacherName'] as String? ?? '',
      grade: (data['grade'] as num?)?.toDouble() ?? 0.0,
      maxGrade: (data['maxGrade'] as num?)?.toDouble() ?? 100.0,
      examDate: (data['examDate'] as Timestamp).toDate(),
      examType: ExamType.values.firstWhere(
        (e) => e.toString() == data['examType'],
        orElse: () => ExamType.monthly,
      ),
      examDuration: data['examDuration'] as int? ?? 60,
      isPublished: data['isPublished'] as bool? ?? false,
      publishedAt: data['publishedAt'] != null 
          ? (data['publishedAt'] as Timestamp).toDate() 
          : null,
      teacherNotes: data['teacherNotes'] as String?,
      performanceRating: data['performanceRating'] as String?,
      classAverage: (data['classAverage'] as num?)?.toDouble(),
      highestInClass: (data['highestInClass'] as num?)?.toDouble(),
      lowestInClass: (data['lowestInClass'] as num?)?.toDouble(),
      rankInClass: data['rankInClass'] as int?,
      totalStudents: data['totalStudents'] as int?,
      semester: data['semester'] as String? ?? '',
      academicYear: data['academicYear'] as String? ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  /// تحويل النموذج إلى بيانات Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'studentId': studentId,
      'examId': examId,
      'examName': examName,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'grade': grade,
      'maxGrade': maxGrade,
      'examDate': Timestamp.fromDate(examDate),
      'examType': examType.toString(),
      'examDuration': examDuration,
      'isPublished': isPublished,
      'publishedAt': publishedAt != null ? Timestamp.fromDate(publishedAt!) : null,
      'teacherNotes': teacherNotes,
      'performanceRating': performanceRating,
      'classAverage': classAverage,
      'highestInClass': highestInClass,
      'lowestInClass': lowestInClass,
      'rankInClass': rankInClass,
      'totalStudents': totalStudents,
      'semester': semester,
      'academicYear': academicYear,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// نسخ النموذج مع تعديل بعض الخصائص
  StudentGradeModel copyWith({
    String? id,
    String? studentId,
    String? examId,
    String? examName,
    String? subjectId,
    String? subjectName,
    String? teacherId,
    String? teacherName,
    double? grade,
    double? maxGrade,
    DateTime? examDate,
    ExamType? examType,
    int? examDuration,
    bool? isPublished,
    DateTime? publishedAt,
    String? teacherNotes,
    String? performanceRating,
    double? classAverage,
    double? highestInClass,
    double? lowestInClass,
    int? rankInClass,
    int? totalStudents,
    String? semester,
    String? academicYear,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return StudentGradeModel(
      id: id ?? this.id,
      studentId: studentId ?? this.studentId,
      examId: examId ?? this.examId,
      examName: examName ?? this.examName,
      subjectId: subjectId ?? this.subjectId,
      subjectName: subjectName ?? this.subjectName,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      grade: grade ?? this.grade,
      maxGrade: maxGrade ?? this.maxGrade,
      examDate: examDate ?? this.examDate,
      examType: examType ?? this.examType,
      examDuration: examDuration ?? this.examDuration,
      isPublished: isPublished ?? this.isPublished,
      publishedAt: publishedAt ?? this.publishedAt,
      teacherNotes: teacherNotes ?? this.teacherNotes,
      performanceRating: performanceRating ?? this.performanceRating,
      classAverage: classAverage ?? this.classAverage,
      highestInClass: highestInClass ?? this.highestInClass,
      lowestInClass: lowestInClass ?? this.lowestInClass,
      rankInClass: rankInClass ?? this.rankInClass,
      totalStudents: totalStudents ?? this.totalStudents,
      semester: semester ?? this.semester,
      academicYear: academicYear ?? this.academicYear,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// الحصول على التقدير النصي للدرجة
  String get letterGrade {
    final percent = percentage;
    if (percent >= 90) return 'ممتاز';
    if (percent >= 80) return 'جيد جداً';
    if (percent >= 70) return 'جيد';
    if (percent >= 60) return 'مقبول';
    return 'ضعيف';
  }

  /// الحصول على لون التقدير
  String get gradeColor {
    final percent = percentage;
    if (percent >= 90) return '#4CAF50'; // أخضر
    if (percent >= 80) return '#8BC34A'; // أخضر فاتح
    if (percent >= 70) return '#FFC107'; // أصفر
    if (percent >= 60) return '#FF9800'; // برتقالي
    return '#F44336'; // أحمر
  }

  /// هل الطالب نجح في هذا الامتحان؟
  bool get isPassed => percentage >= 60.0;

  /// الحصول على الأداء مقارنة بمتوسط الصف
  String get performanceVsClass {
    if (classAverage == null) return 'غير متوفر';
    
    final diff = grade - classAverage!;
    if (diff > 10) return 'أعلى بكثير من المتوسط';
    if (diff > 5) return 'أعلى من المتوسط';
    if (diff > -5) return 'قريب من المتوسط';
    if (diff > -10) return 'أقل من المتوسط';
    return 'أقل بكثير من المتوسط';
  }
}
