import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/activities_management_tab.dart';
import 'package:school_management_system/admin_screens/competitions_management_tab.dart';
import 'package:school_management_system/admin_screens/lesson_management_tab.dart';
import 'package:school_management_system/admin_screens/widgets/activity_form_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/competition_form_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/lesson_form_dialog.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/models/competition_model.dart';
import 'package:school_management_system/models/lesson_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

class ContentManagementScreen extends ConsumerStatefulWidget {
  const ContentManagementScreen({super.key});

  @override
  ConsumerState<ContentManagementScreen> createState() =>
      _ContentManagementScreenState();
}

class _ContentManagementScreenState
    extends ConsumerState<ContentManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _showAddDialog() {
    switch (_tabController.index) {
      case 0:
        showDialog(context: context, builder: (_) => const LessonFormDialog());
        break;
      case 1:
        showDialog(context: context, builder: (_) => const ActivityFormDialog());
        break;
      case 2:
        showDialog(context: context, builder: (_) => const CompetitionFormDialog());
        break;
    }
  }

  Future<bool?> _showDeleteConfirmationDialog(
      BuildContext context, String message) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(message),
        actions: [
          TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('إلغاء')),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة المحتوى'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.book), text: 'الدروس'),
            Tab(icon: Icon(Icons.local_activity), text: 'الأنشطة'),
            Tab(icon: Icon(Icons.emoji_events), text: 'المسابقات'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          LessonManagementTab(
            onEdit: (lesson) => showDialog(
                context: context,
                builder: (_) => LessonFormDialog(lesson: lesson)),
            onDelete: (lesson) async {
              final confirm = await _showDeleteConfirmationDialog(
                  context,
                  'هل أنت متأكد من رغبتك في حذف درس "${lesson.title}"؟ سيتم حذف جميع الصور والمرفقات المرتبطة به.');
              if (confirm == true) {
                try {
                  for (String url in lesson.imageUrls) {
                    await ref.read(firebaseServiceProvider).deleteImage(url);
                  }
                  await ref.read(firebaseServiceProvider).deleteDocument('lessons', lesson.id);
                   if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم الحذف بنجاح'), backgroundColor: Colors.green),
                    );
                  }
                } catch (e) {
                   if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ أثناء الحذف: $e'), backgroundColor: Colors.red),
                    );
                  }
                }
              }
            },
          ),
          ActivitiesManagementTab(
            onEdit: (activity) => showDialog(
                context: context,
                builder: (_) => ActivityFormDialog(activity: activity)),
            onDelete: (activity) async {
              final confirm = await _showDeleteConfirmationDialog(
                  context,
                  'هل أنت متأكد من رغبتك في حذف "${activity.title}"؟ سيتم حذف جميع الصور المرتبطة به.');
              if (confirm == true) {
                 try {
                    for (String url in activity.imageUrls) {
                      await ref.read(firebaseServiceProvider).deleteImage(url);
                    }
                    await ref.read(firebaseServiceProvider).deleteDocument('activities', activity.id);
                     if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('تم الحذف بنجاح'), backgroundColor: Colors.green),
                      );
                    }
                  } catch (e) {
                     if (mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('خطأ أثناء الحذف: $e'), backgroundColor: Colors.red),
                      );
                    }
                  }
              }
            },
          ),
          CompetitionsManagementTab(
            onEdit: (competition) => showDialog(
                context: context,
                builder: (_) => CompetitionFormDialog(competition: competition)),
            onDelete: (CompetitionModel competition) async {
               final confirm = await _showDeleteConfirmationDialog(
                  context,
                  'هل أنت متأكد من رغبتك في حذف "${competition.title}"؟');
              if (confirm == true) {
                try {
                  await ref.read(firebaseServiceProvider).deleteCompetition(competition.id);
                   if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم الحذف بنجاح'), backgroundColor: Colors.green),
                    );
                  }
                } catch (e) {
                   if (mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ أثناء الحذف: $e'), backgroundColor: Colors.red),
                    );
                  }
                }
              }
            },
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddDialog,
        tooltip: 'إضافة عنصر جديد',
        child: const Icon(Icons.add),
      ),
    );
  }
}
