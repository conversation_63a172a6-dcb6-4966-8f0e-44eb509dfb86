import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/conversation_model.dart';
import 'package:school_management_system/models/message_model.dart';
import 'package:school_management_system/providers/messaging_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';

/// شاشة إدارة التواصل المتقدمة للإدارة
///
/// هذه الشاشة تمكن الإدارة من:
/// - مراقبة جميع المحادثات في النظام
/// - الرد على استفسارات أولياء الأمور
/// - إدارة الشكاوى والاقتراحات
/// - تصنيف وفلترة المحادثات
/// - إنشاء إعلانات ورسائل جماعية
/// - مراقبة إحصائيات التواصل
/// - أرشفة المحادثات المكتملة
///
/// التصميم:
/// - واجهة تشبه تطبيقات المراسلة الحديثة
/// - قائمة جانبية للمحادثات
/// - منطقة الرسائل الرئيسية
/// - شريط أدوات متقدم للإدارة
/// - فلاتر وبحث شامل
/// - إحصائيات في الوقت الفعلي
class AdvancedMessagingScreen extends ConsumerStatefulWidget {
  /// معرف المدير أو الموظف الذي يستخدم النظام
  final String adminId;

  /// اسم المدير أو الموظف
  final String adminName;

  /// دور المدير أو الموظف
  final ParticipantRole adminRole;

  const AdvancedMessagingScreen({
    super.key,
    required this.adminId,
    required this.adminName,
    required this.adminRole,
  });

  @override
  ConsumerState<AdvancedMessagingScreen> createState() =>
      _AdvancedMessagingScreenState();
}

class _AdvancedMessagingScreenState
    extends ConsumerState<AdvancedMessagingScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  late TabController _tabController;

  /// متحكم الرسوم المتحركة للرسائل
  late AnimationController _messageAnimationController;

  /// متحكم البحث في المحادثات
  final TextEditingController _searchController = TextEditingController();

  /// متحكم كتابة الرسالة الجديدة
  final TextEditingController _messageController = TextEditingController();

  /// مفتاح النموذج للتحقق من صحة البيانات
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  // ===================================================================
  // متغيرات الحالة
  // ===================================================================

  /// المحادثة المحددة حالياً
  String? _selectedConversationId;

  /// نوع العرض (قائمة، تفاصيل، إحصائيات)
  ViewMode _viewMode = ViewMode.conversations;

  /// حالة تحميل البيانات
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // إنشاء متحكم الرسوم المتحركة
    _messageAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // بدء الرسوم المتحركة
    _messageAnimationController.forward();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _messageAnimationController.dispose();
    _searchController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات والإجراءات
      appBar: AppBar(
        title: const Text(
          'إدارة التواصل المتقدمة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo[800], // لون مميز للإدارة
        elevation: 2,

        // التبويبات السفلية في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 12,
          ),
          tabs: [
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.forum, size: 18),
                  // عداد المحادثات النشطة
                  Positioned(
                    right: 0,
                    top: 0,
                    child: _buildNotificationBadge(
                      ref
                          .watch(communicationStatsProvider(widget.adminId))
                          .when(
                            data: (stats) => stats['activeConversations'] ?? 0,
                            loading: () => 0,
                            error: (_, __) => 0,
                          ),
                    ),
                  ),
                ],
              ),
              text: 'المحادثات',
            ),
            Tab(
              icon: Stack(
                children: [
                  const Icon(Icons.priority_high, size: 18),
                  // عداد المحادثات المهمة
                  Positioned(
                    right: 0,
                    top: 0,
                    child: _buildNotificationBadge(
                      ref
                          .watch(importantConversationsProvider(widget.adminId))
                          .when(
                            data: (conversations) => conversations.length,
                            loading: () => 0,
                            error: (_, __) => 0,
                          ),
                    ),
                  ),
                ],
              ),
              text: 'المهمة',
            ),
            const Tab(
              icon: Icon(Icons.analytics, size: 18),
              text: 'الإحصائيات',
            ),
            const Tab(icon: Icon(Icons.settings, size: 18), text: 'الإعدادات'),
          ],
        ),

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث في المحادثات',
          ),

          // زر الفلاتر
          IconButton(
            icon: const Icon(Icons.filter_list, color: Colors.white),
            onPressed: () => _showFilterDialog(),
            tooltip: 'فلترة المحادثات',
          ),

          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.more_vert, color: Colors.white),
            onPressed: () => _showOptionsMenu(),
            tooltip: 'المزيد من الخيارات',
          ),
        ],
      ),

      // محتوى التبويبات الرئيسية
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب المحادثات العامة
          _buildConversationsTab(),

          // تبويب المحادثات المهمة
          _buildImportantConversationsTab(),

          // تبويب الإحصائيات
          _buildStatisticsTab(),

          // تبويب الإعدادات
          _buildSettingsTab(),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),

      // زر عائم للإجراءات السريعة
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  // ===================================================================
  // دوال بناء التبويبات
  // ===================================================================

  /// بناء تبويب المحادثات العامة
  Widget _buildConversationsTab() {
    final conversationsAsync = ref.watch(
      filteredConversationsProvider(widget.adminId),
    );

    return conversationsAsync.when(
      loading: () => const Center(child: LoadingIndicator()),
      error:
          (error, stack) => Center(
            child: ErrorMessage(message: 'خطأ في تحميل المحادثات: $error'),
          ),
      data: (conversations) {
        if (conversations.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.forum_outlined, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text(
                  'لا توجد محادثات حالياً',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'ستظهر المحادثات الجديدة هنا',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return Row(
          children: [
            // قائمة المحادثات الجانبية
            Expanded(flex: 1, child: _buildConversationsList(conversations)),

            // منطقة الرسائل الرئيسية
            if (_selectedConversationId != null)
              Expanded(
                flex: 2,
                child: _buildMessagesArea(_selectedConversationId!),
              )
            else
              const Expanded(
                flex: 2,
                child: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.chat_bubble_outline,
                        size: 64,
                        color: Colors.grey,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'اختر محادثة لعرض الرسائل',
                        style: TextStyle(fontSize: 18, color: Colors.grey),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        );
      },
    );
  }

  /// بناء تبويب المحادثات المهمة
  Widget _buildImportantConversationsTab() {
    final importantConversationsAsync = ref.watch(
      importantConversationsProvider(widget.adminId),
    );

    return importantConversationsAsync.when(
      loading: () => const Center(child: LoadingIndicator()),
      error:
          (error, stack) => Center(
            child: ErrorMessage(
              message: 'خطأ في تحميل المحادثات المهمة: $error',
            ),
          ),
      data: (conversations) {
        if (conversations.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.priority_high, size: 64, color: Colors.orange),
                SizedBox(height: 16),
                Text(
                  'لا توجد محادثات مهمة حالياً',
                  style: TextStyle(fontSize: 18, color: Colors.grey),
                ),
                SizedBox(height: 8),
                Text(
                  'المحادثات ذات الأولوية العالية ستظهر هنا',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          );
        }

        return _buildConversationsList(conversations);
      },
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    final statsAsync = ref.watch(communicationStatsProvider(widget.adminId));

    return statsAsync.when(
      loading: () => const Center(child: LoadingIndicator()),
      error:
          (error, stack) => Center(
            child: ErrorMessage(message: 'خطأ في تحميل الإحصائيات: $error'),
          ),
      data: (stats) => _buildStatisticsContent(stats),
    );
  }

  /// بناء تبويب الإعدادات
  Widget _buildSettingsTab() {
    return const Center(
      child: Text(
        'إعدادات التواصل\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  // ===================================================================
  // دوال بناء المكونات
  // ===================================================================

  /// بناء قائمة المحادثات
  Widget _buildConversationsList(List<ConversationModel> conversations) {
    return Container(
      decoration: BoxDecoration(
        border: Border(right: BorderSide(color: Colors.grey[300]!)),
      ),
      child: ListView.builder(
        itemCount: conversations.length,
        itemBuilder: (context, index) {
          final conversation = conversations[index];
          final isSelected = _selectedConversationId == conversation.id;

          return _buildConversationTile(conversation, isSelected);
        },
      ),
    );
  }

  /// بناء عنصر المحادثة
  Widget _buildConversationTile(
    ConversationModel conversation,
    bool isSelected,
  ) {
    return Container(
      color: isSelected ? Colors.indigo[50] : null,
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getPriorityColor(conversation.priority),
          child: Icon(
            _getTypeIcon(conversation.type),
            color: Colors.white,
            size: 20,
          ),
        ),
        title: Text(
          conversation.subject,
          style: TextStyle(
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (conversation.lastMessageText != null)
              Text(
                conversation.lastMessageText!,
                style: const TextStyle(fontSize: 12),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            const SizedBox(height: 2),
            Row(
              children: [
                Icon(Icons.access_time, size: 12, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Text(
                  _formatTime(conversation.updatedAt),
                  style: TextStyle(fontSize: 10, color: Colors.grey[600]),
                ),
                const Spacer(),
                if (conversation.unreadCounts[widget.adminId] != null &&
                    conversation.unreadCounts[widget.adminId]! > 0)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Text(
                      '${conversation.unreadCounts[widget.adminId]}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
        onTap: () {
          setState(() {
            _selectedConversationId = conversation.id;
          });
        },
        trailing: PopupMenuButton<String>(
          icon: const Icon(Icons.more_vert, size: 16),
          onSelected: (value) => _handleConversationAction(value, conversation),
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'archive',
                  child: Row(
                    children: [
                      Icon(Icons.archive, size: 16),
                      SizedBox(width: 8),
                      Text('أرشفة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'close',
                  child: Row(
                    children: [
                      Icon(Icons.close, size: 16),
                      SizedBox(width: 8),
                      Text('إغلاق'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'priority',
                  child: Row(
                    children: [
                      Icon(Icons.flag, size: 16),
                      SizedBox(width: 8),
                      Text('تغيير الأولوية'),
                    ],
                  ),
                ),
              ],
        ),
      ),
    );
  }

  /// بناء منطقة الرسائل
  Widget _buildMessagesArea(String conversationId) {
    return const Center(
      child: Text(
        'منطقة الرسائل\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء محتوى الإحصائيات
  Widget _buildStatisticsContent(Map<String, dynamic> stats) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          // بطاقات الإحصائيات السريعة
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي المحادثات',
                  '${stats['totalConversations'] ?? 0}',
                  Icons.forum,
                  Colors.blue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'المحادثات النشطة',
                  '${stats['activeConversations'] ?? 0}',
                  Icons.chat,
                  Colors.green,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  'إجمالي الرسائل',
                  '${stats['totalMessages'] ?? 0}',
                  Icons.message,
                  Colors.orange,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  'غير مقروءة',
                  '${stats['unreadMessages'] ?? 0}',
                  Icons.mark_email_unread,
                  Colors.red,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: const TextStyle(fontSize: 12, color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء شارة الإشعارات
  Widget _buildNotificationBadge(int count) {
    if (count == 0) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: Colors.red,
        borderRadius: BorderRadius.circular(8),
      ),
      constraints: const BoxConstraints(minWidth: 12, minHeight: 12),
      child: Text(
        count > 99 ? '99+' : '$count',
        style: const TextStyle(
          color: Colors.white,
          fontSize: 8,
          fontWeight: FontWeight.bold,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    final statsAsync = ref.watch(communicationStatsProvider(widget.adminId));

    return statsAsync.when(
      data:
          (stats) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.indigo[50],
              border: Border(top: BorderSide(color: Colors.indigo[200]!)),
            ),
            child: Row(
              children: [
                Icon(Icons.forum, size: 16, color: Colors.indigo[600]),
                const SizedBox(width: 4),
                Text(
                  'المحادثات: ${stats['activeConversations'] ?? 0}',
                  style: TextStyle(fontSize: 12, color: Colors.indigo[600]),
                ),
                const Spacer(),
                Icon(Icons.message, size: 16, color: Colors.indigo[600]),
                const SizedBox(width: 4),
                Text(
                  'الرسائل: ${stats['totalMessages'] ?? 0}',
                  style: TextStyle(fontSize: 12, color: Colors.indigo[600]),
                ),
              ],
            ),
          ),
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  /// بناء الزر العائم
  Widget _buildFloatingActionButton() {
    return FloatingActionButton.extended(
      onPressed: () => _showNewConversationDialog(),
      backgroundColor: Colors.indigo[600],
      icon: const Icon(Icons.add_comment, color: Colors.white),
      label: const Text(
        'محادثة جديدة',
        style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
      ),
    );
  }

  // ===================================================================
  // دوال الأحداث والحوارات
  // ===================================================================

  /// عرض حوار البحث
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث في المحادثات'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن محادثة أو رسالة...',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (value) {
                ref.read(conversationSearchQueryProvider.notifier).state =
                    value;
              },
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الفلاتر
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('فلترة المحادثات'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // فلتر النوع
                DropdownButtonFormField<ConversationType?>(
                  value: ref.read(conversationTypeFilterProvider),
                  decoration: const InputDecoration(labelText: 'نوع المحادثة'),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ConversationType.values.map(
                      (type) => DropdownMenuItem(
                        value: type,
                        child: Text(_getTypeLabel(type)),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    ref.read(conversationTypeFilterProvider.notifier).state =
                        value;
                  },
                ),
                const SizedBox(height: 16),

                // فلتر الأولوية
                DropdownButtonFormField<ConversationPriority?>(
                  value: ref.read(conversationPriorityFilterProvider),
                  decoration: const InputDecoration(labelText: 'الأولوية'),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ConversationPriority.values.map(
                      (priority) => DropdownMenuItem(
                        value: priority,
                        child: Text(_getPriorityLabel(priority)),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    ref
                        .read(conversationPriorityFilterProvider.notifier)
                        .state = value;
                  },
                ),
                const SizedBox(height: 16),

                // فلتر الحالة
                DropdownButtonFormField<ConversationStatus?>(
                  value: ref.read(conversationStatusFilterProvider),
                  decoration: const InputDecoration(labelText: 'الحالة'),
                  items: [
                    const DropdownMenuItem(value: null, child: Text('الكل')),
                    ...ConversationStatus.values.map(
                      (status) => DropdownMenuItem(
                        value: status,
                        child: Text(_getStatusLabel(status)),
                      ),
                    ),
                  ],
                  onChanged: (value) {
                    ref.read(conversationStatusFilterProvider.notifier).state =
                        value;
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () {
                  // إعادة تعيين الفلاتر
                  ref.read(conversationTypeFilterProvider.notifier).state =
                      null;
                  ref.read(conversationPriorityFilterProvider.notifier).state =
                      null;
                  ref.read(conversationStatusFilterProvider.notifier).state =
                      null;
                  Navigator.pop(context);
                },
                child: const Text('إعادة تعيين'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('تطبيق'),
              ),
            ],
          ),
    );
  }

  /// عرض قائمة الخيارات
  void _showOptionsMenu() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.refresh),
                title: const Text('تحديث البيانات'),
                onTap: () {
                  Navigator.pop(context);
                  _refreshData();
                },
              ),
              ListTile(
                leading: const Icon(Icons.archive),
                title: const Text('المحادثات المؤرشفة'),
                onTap: () {
                  Navigator.pop(context);
                  _showArchivedConversations();
                },
              ),
              ListTile(
                leading: const Icon(Icons.download),
                title: const Text('تصدير التقارير'),
                onTap: () {
                  Navigator.pop(context);
                  _exportReports();
                },
              ),
              ListTile(
                leading: const Icon(Icons.settings),
                title: const Text('إعدادات التواصل'),
                onTap: () {
                  Navigator.pop(context);
                  _showCommunicationSettings();
                },
              ),
            ],
          ),
    );
  }

  /// عرض حوار محادثة جديدة
  void _showNewConversationDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('محادثة جديدة'),
            content: const Text('سيتم تطبيق إنشاء محادثة جديدة قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إنشاء'),
              ),
            ],
          ),
    );
  }

  /// معالجة إجراءات المحادثة
  void _handleConversationAction(
    String action,
    ConversationModel conversation,
  ) {
    switch (action) {
      case 'archive':
        _archiveConversation(conversation);
        break;
      case 'close':
        _closeConversation(conversation);
        break;
      case 'priority':
        _changePriority(conversation);
        break;
    }
  }

  // ===================================================================
  // دوال الإجراءات
  // ===================================================================

  /// أرشفة محادثة
  void _archiveConversation(ConversationModel conversation) async {
    try {
      final messagingService = ref.read(messagingServiceProvider);
      await messagingService.archiveConversation(
        conversation.id,
        widget.adminId,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم أرشفة المحادثة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في أرشفة المحادثة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إغلاق محادثة
  void _closeConversation(ConversationModel conversation) async {
    try {
      final messagingService = ref.read(messagingServiceProvider);
      await messagingService.closeConversation(conversation.id);

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم إغلاق المحادثة بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في إغلاق المحادثة: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// تغيير أولوية المحادثة
  void _changePriority(ConversationModel conversation) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تغيير الأولوية'),
            content: const Text('سيتم تطبيق تغيير الأولوية قريباً'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  /// تحديث البيانات
  void _refreshData() {
    setState(() {
      _isLoading = true;
    });

    // محاكاة تحديث البيانات
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    });
  }

  /// عرض المحادثات المؤرشفة
  void _showArchivedConversations() {
    // TODO: تطبيق عرض المحادثات المؤرشفة
  }

  /// تصدير التقارير
  void _exportReports() {
    // TODO: تطبيق تصدير التقارير
  }

  /// عرض إعدادات التواصل
  void _showCommunicationSettings() {
    // TODO: تطبيق إعدادات التواصل
  }

  // ===================================================================
  // دوال مساعدة
  // ===================================================================

  /// الحصول على لون الأولوية
  Color _getPriorityColor(ConversationPriority priority) {
    switch (priority) {
      case ConversationPriority.low:
        return Colors.green;
      case ConversationPriority.normal:
        return Colors.blue;
      case ConversationPriority.high:
        return Colors.orange;
      case ConversationPriority.urgent:
        return Colors.red;
      case ConversationPriority.critical:
        return Colors.purple;
    }
  }

  /// الحصول على أيقونة النوع
  IconData _getTypeIcon(ConversationType type) {
    switch (type) {
      case ConversationType.inquiry:
        return Icons.help;
      case ConversationType.complaint:
        return Icons.report;
      case ConversationType.suggestion:
        return Icons.lightbulb;
      case ConversationType.request:
        return Icons.assignment;
      case ConversationType.emergency:
        return Icons.emergency;
      case ConversationType.general:
        return Icons.chat;
      case ConversationType.academic:
        return Icons.school;
      case ConversationType.behavioral:
        return Icons.psychology;
      case ConversationType.financial:
        return Icons.attach_money;
      case ConversationType.medical:
        return Icons.medical_services;
    }
  }

  /// الحصول على تسمية النوع
  String _getTypeLabel(ConversationType type) {
    switch (type) {
      case ConversationType.inquiry:
        return 'استفسار';
      case ConversationType.complaint:
        return 'شكوى';
      case ConversationType.suggestion:
        return 'اقتراح';
      case ConversationType.request:
        return 'طلب';
      case ConversationType.emergency:
        return 'طارئ';
      case ConversationType.general:
        return 'عام';
      case ConversationType.academic:
        return 'أكاديمي';
      case ConversationType.behavioral:
        return 'سلوكي';
      case ConversationType.financial:
        return 'مالي';
      case ConversationType.medical:
        return 'طبي';
    }
  }

  /// الحصول على تسمية الأولوية
  String _getPriorityLabel(ConversationPriority priority) {
    switch (priority) {
      case ConversationPriority.low:
        return 'منخفضة';
      case ConversationPriority.normal:
        return 'عادية';
      case ConversationPriority.high:
        return 'مهمة';
      case ConversationPriority.urgent:
        return 'عاجلة';
      case ConversationPriority.critical:
        return 'حرجة';
    }
  }

  /// الحصول على تسمية الحالة
  String _getStatusLabel(ConversationStatus status) {
    switch (status) {
      case ConversationStatus.active:
        return 'نشطة';
      case ConversationStatus.closed:
        return 'مغلقة';
      case ConversationStatus.archived:
        return 'مؤرشفة';
      case ConversationStatus.pending:
        return 'معلقة';
      case ConversationStatus.resolved:
        return 'محلولة';
    }
  }

  /// تنسيق الوقت للعرض
  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }
}

/// أنماط العرض
enum ViewMode {
  conversations, // عرض المحادثات
  details, // عرض التفاصيل
  statistics, // عرض الإحصائيات
}
