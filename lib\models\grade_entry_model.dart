import 'package:cloud_firestore/cloud_firestore.dart';

/// تعداد حالات إدخال الدرجات
enum GradeEntryStatus {
  draft,      // مسودة (لم يتم الإرسال بعد)
  submitted,  // تم الإرسال للمراجعة
  approved,   // تم اعتماد الدرجات
  rejected    // تم رفض الدرجات (تحتاج تعديل)
}

/// تعداد أنواع التعديل على الدرجات
enum GradeModificationType {
  correction,     // تصحيح خطأ
  bonus,          // درجات إضافية
  penalty,        // خصم درجات
  absence,        // غياب
  cheating        // غش
}

/// امتدادات مفيدة لحالات إدخال الدرجات
extension GradeEntryStatusExtension on GradeEntryStatus {
  /// الاسم العربي لحالة إدخال الدرجات
  String get arabicName {
    switch (this) {
      case GradeEntryStatus.draft:
        return 'مسودة';
      case GradeEntryStatus.submitted:
        return 'مرسل للمراجعة';
      case GradeEntryStatus.approved:
        return 'معتمد';
      case GradeEntryStatus.rejected:
        return 'مرفوض';
    }
  }
}

/// امتدادات مفيدة لأنواع التعديل
extension GradeModificationTypeExtension on GradeModificationType {
  /// الاسم العربي لنوع التعديل
  String get arabicName {
    switch (this) {
      case GradeModificationType.correction:
        return 'تصحيح خطأ';
      case GradeModificationType.bonus:
        return 'درجات إضافية';
      case GradeModificationType.penalty:
        return 'خصم درجات';
      case GradeModificationType.absence:
        return 'غياب';
      case GradeModificationType.cheating:
        return 'غش';
    }
  }
}

/// نموذج قسم من أقسام الامتحان مع درجته
/// مثل "السؤال الأول" أو "القسم النظري"
class ExamSection {
  final String id;
  final String name;              // اسم القسم مثل "السؤال الأول"
  final int maxMarks;             // الدرجة الكاملة لهذا القسم
  final String? description;      // وصف القسم
  final int order;                // ترتيب القسم في الامتحان

  const ExamSection({
    required this.id,
    required this.name,
    required this.maxMarks,
    this.description,
    required this.order,
  });

  /// إنشاء قسم امتحان من Map
  factory ExamSection.fromMap(Map<String, dynamic> data) {
    return ExamSection(
      id: data['id'] as String? ?? '',
      name: data['name'] as String? ?? '',
      maxMarks: data['maxMarks'] as int? ?? 0,
      description: data['description'] as String?,
      order: data['order'] as int? ?? 0,
    );
  }

  /// تحويل قسم الامتحان إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'maxMarks': maxMarks,
      'description': description,
      'order': order,
    };
  }

  @override
  String toString() {
    return 'ExamSection(id: $id, name: $name, maxMarks: $maxMarks)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExamSection && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج درجة طالب واحد في امتحان
/// يحتوي على درجات جميع أقسام الامتحان
class StudentGrade {
  final String studentId;
  final String studentName;
  final String studentNumber;
  final Map<String, double> sectionGrades; // درجات كل قسم (معرف القسم -> الدرجة)
  final double totalGrade;        // المجموع الكلي
  final String? teacherNotes;     // ملاحظات المعلم
  final bool isPresent;           // هل الطالب حاضر
  final String? absenceReason;    // سبب الغياب إن وجد
  final List<GradeModification> modifications; // تعديلات على الدرجة
  final DateTime? entryDate;      // تاريخ إدخال الدرجة
  final String? enteredBy;        // من أدخل الدرجة

  const StudentGrade({
    required this.studentId,
    required this.studentName,
    required this.studentNumber,
    required this.sectionGrades,
    required this.totalGrade,
    this.teacherNotes,
    required this.isPresent,
    this.absenceReason,
    required this.modifications,
    this.entryDate,
    this.enteredBy,
  });

  /// إنشاء درجة طالب من Map
  factory StudentGrade.fromMap(Map<String, dynamic> data) {
    return StudentGrade(
      studentId: data['studentId'] as String? ?? '',
      studentName: data['studentName'] as String? ?? '',
      studentNumber: data['studentNumber'] as String? ?? '',
      sectionGrades: Map<String, double>.from(data['sectionGrades'] ?? {}),
      totalGrade: (data['totalGrade'] as num?)?.toDouble() ?? 0.0,
      teacherNotes: data['teacherNotes'] as String?,
      isPresent: data['isPresent'] as bool? ?? true,
      absenceReason: data['absenceReason'] as String?,
      modifications: (data['modifications'] as List<dynamic>?)
          ?.map((m) => GradeModification.fromMap(m as Map<String, dynamic>))
          .toList() ?? [],
      entryDate: data['entryDate'] != null 
          ? (data['entryDate'] as Timestamp).toDate() 
          : null,
      enteredBy: data['enteredBy'] as String?,
    );
  }

  /// تحويل درجة الطالب إلى Map
  Map<String, dynamic> toMap() {
    return {
      'studentId': studentId,
      'studentName': studentName,
      'studentNumber': studentNumber,
      'sectionGrades': sectionGrades,
      'totalGrade': totalGrade,
      'teacherNotes': teacherNotes,
      'isPresent': isPresent,
      'absenceReason': absenceReason,
      'modifications': modifications.map((m) => m.toMap()).toList(),
      'entryDate': entryDate != null ? Timestamp.fromDate(entryDate!) : null,
      'enteredBy': enteredBy,
    };
  }

  /// حساب المجموع من درجات الأقسام
  double get calculatedTotal {
    return sectionGrades.values.fold(0.0, (sum, grade) => sum + grade);
  }

  /// التحقق من صحة الدرجات
  bool isValidForSections(List<ExamSection> sections) {
    // التحقق من وجود درجة لكل قسم
    for (final section in sections) {
      if (!sectionGrades.containsKey(section.id)) return false;
      
      final grade = sectionGrades[section.id]!;
      // التحقق من أن الدرجة لا تتجاوز الحد الأقصى
      if (grade < 0 || grade > section.maxMarks) return false;
    }
    
    return true;
  }

  @override
  String toString() {
    return 'StudentGrade(studentId: $studentId, studentName: $studentName, totalGrade: $totalGrade)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudentGrade && other.studentId == studentId;
  }

  @override
  int get hashCode => studentId.hashCode;
}

/// نموذج تعديل على درجة طالب
/// يسجل أي تغيير يتم على الدرجة مع السبب والتاريخ
class GradeModification {
  final String id;
  final GradeModificationType type; // نوع التعديل
  final double oldValue;            // القيمة القديمة
  final double newValue;            // القيمة الجديدة
  final String reason;              // سبب التعديل
  final DateTime modifiedAt;        // تاريخ التعديل
  final String modifiedBy;          // من قام بالتعديل
  final String? approvedBy;         // من اعتمد التعديل
  final DateTime? approvedAt;       // تاريخ الاعتماد

  const GradeModification({
    required this.id,
    required this.type,
    required this.oldValue,
    required this.newValue,
    required this.reason,
    required this.modifiedAt,
    required this.modifiedBy,
    this.approvedBy,
    this.approvedAt,
  });

  /// إنشاء تعديل درجة من Map
  factory GradeModification.fromMap(Map<String, dynamic> data) {
    return GradeModification(
      id: data['id'] as String? ?? '',
      type: GradeModificationType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => GradeModificationType.correction,
      ),
      oldValue: (data['oldValue'] as num?)?.toDouble() ?? 0.0,
      newValue: (data['newValue'] as num?)?.toDouble() ?? 0.0,
      reason: data['reason'] as String? ?? '',
      modifiedAt: (data['modifiedAt'] as Timestamp).toDate(),
      modifiedBy: data['modifiedBy'] as String? ?? '',
      approvedBy: data['approvedBy'] as String?,
      approvedAt: data['approvedAt'] != null 
          ? (data['approvedAt'] as Timestamp).toDate() 
          : null,
    );
  }

  /// تحويل تعديل الدرجة إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'oldValue': oldValue,
      'newValue': newValue,
      'reason': reason,
      'modifiedAt': Timestamp.fromDate(modifiedAt),
      'modifiedBy': modifiedBy,
      'approvedBy': approvedBy,
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
    };
  }

  /// حساب مقدار التغيير
  double get changeAmount {
    return newValue - oldValue;
  }

  /// التحقق من كون التعديل معتمد
  bool get isApproved {
    return approvedBy != null && approvedAt != null;
  }

  @override
  String toString() {
    return 'GradeModification(id: $id, type: ${type.arabicName}, change: $changeAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GradeModification && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج إدخال درجات امتحان كامل
/// يحتوي على درجات جميع الطلاب في امتحان مادة معينة
class GradeEntry {
  final String id;
  final String examId;            // معرف الامتحان
  final String subjectId;         // معرف المادة
  final String subjectName;       // اسم المادة
  final String classId;           // معرف الصف
  final String className;         // اسم الصف
  final String teacherId;         // معرف المعلم
  final String teacherName;       // اسم المعلم
  final List<ExamSection> sections; // أقسام الامتحان
  final int totalMarks;           // الدرجة الكاملة للامتحان
  final List<StudentGrade> studentGrades; // درجات الطلاب
  final GradeEntryStatus status;  // حالة إدخال الدرجات
  final String? rejectionReason;  // سبب الرفض إن وجد
  final DateTime createdAt;       // تاريخ الإنشاء
  final String createdBy;         // منشئ الإدخال
  final DateTime? submittedAt;    // تاريخ الإرسال
  final DateTime? approvedAt;     // تاريخ الاعتماد
  final String? approvedBy;       // من اعتمد الدرجات

  const GradeEntry({
    required this.id,
    required this.examId,
    required this.subjectId,
    required this.subjectName,
    required this.classId,
    required this.className,
    required this.teacherId,
    required this.teacherName,
    required this.sections,
    required this.totalMarks,
    required this.studentGrades,
    required this.status,
    this.rejectionReason,
    required this.createdAt,
    required this.createdBy,
    this.submittedAt,
    this.approvedAt,
    this.approvedBy,
  });

  /// إنشاء إدخال درجات من مستند Firestore
  factory GradeEntry.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return GradeEntry(
      id: doc.id,
      examId: data['examId'] as String? ?? '',
      subjectId: data['subjectId'] as String? ?? '',
      subjectName: data['subjectName'] as String? ?? '',
      classId: data['classId'] as String? ?? '',
      className: data['className'] as String? ?? '',
      teacherId: data['teacherId'] as String? ?? '',
      teacherName: data['teacherName'] as String? ?? '',
      sections: (data['sections'] as List<dynamic>?)
          ?.map((s) => ExamSection.fromMap(s as Map<String, dynamic>))
          .toList() ?? [],
      totalMarks: data['totalMarks'] as int? ?? 100,
      studentGrades: (data['studentGrades'] as List<dynamic>?)
          ?.map((g) => StudentGrade.fromMap(g as Map<String, dynamic>))
          .toList() ?? [],
      status: GradeEntryStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => GradeEntryStatus.draft,
      ),
      rejectionReason: data['rejectionReason'] as String?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
      submittedAt: data['submittedAt'] != null 
          ? (data['submittedAt'] as Timestamp).toDate() 
          : null,
      approvedAt: data['approvedAt'] != null 
          ? (data['approvedAt'] as Timestamp).toDate() 
          : null,
      approvedBy: data['approvedBy'] as String?,
    );
  }

  /// تحويل إدخال الدرجات إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'examId': examId,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'classId': classId,
      'className': className,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'sections': sections.map((s) => s.toMap()).toList(),
      'totalMarks': totalMarks,
      'studentGrades': studentGrades.map((g) => g.toMap()).toList(),
      'status': status.toString(),
      'rejectionReason': rejectionReason,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'submittedAt': submittedAt != null ? Timestamp.fromDate(submittedAt!) : null,
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
      'approvedBy': approvedBy,
    };
  }

  /// حساب متوسط الدرجات
  double get averageGrade {
    if (studentGrades.isEmpty) return 0.0;
    final presentStudents = studentGrades.where((g) => g.isPresent).toList();
    if (presentStudents.isEmpty) return 0.0;
    
    final total = presentStudents.fold(0.0, (sum, grade) => sum + grade.totalGrade);
    return total / presentStudents.length;
  }

  /// حساب أعلى درجة
  double get highestGrade {
    if (studentGrades.isEmpty) return 0.0;
    final presentStudents = studentGrades.where((g) => g.isPresent).toList();
    if (presentStudents.isEmpty) return 0.0;
    
    return presentStudents.map((g) => g.totalGrade).reduce((a, b) => a > b ? a : b);
  }

  /// حساب أقل درجة
  double get lowestGrade {
    if (studentGrades.isEmpty) return 0.0;
    final presentStudents = studentGrades.where((g) => g.isPresent).toList();
    if (presentStudents.isEmpty) return 0.0;
    
    return presentStudents.map((g) => g.totalGrade).reduce((a, b) => a < b ? a : b);
  }

  /// حساب عدد الطلاب الناجحين
  int get passCount {
    final passingGrade = totalMarks * 0.5; // 50% للنجاح
    return studentGrades.where((g) => g.isPresent && g.totalGrade >= passingGrade).length;
  }

  /// حساب عدد الطلاب الراسبين
  int get failCount {
    final passingGrade = totalMarks * 0.5; // 50% للنجاح
    return studentGrades.where((g) => g.isPresent && g.totalGrade < passingGrade).length;
  }

  /// حساب نسبة النجاح
  double get passPercentage {
    final presentStudents = studentGrades.where((g) => g.isPresent).length;
    if (presentStudents == 0) return 0.0;
    return (passCount / presentStudents) * 100;
  }

  /// التحقق من اكتمال إدخال الدرجات
  bool get isComplete {
    return studentGrades.every((grade) => 
      grade.isPresent ? grade.sectionGrades.isNotEmpty : true
    );
  }

  @override
  String toString() {
    return 'GradeEntry(id: $id, subject: $subjectName, class: $className, status: ${status.arabicName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GradeEntry && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
