import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة لوحة معلومات الامتحانات الرئيسية
///
/// هذه الشاشة تعرض نظرة عامة شاملة على نظام الامتحانات في المدرسة
/// وتشمل الإحصائيات السريعة والتنبيهات والإجراءات السريعة
///
/// الوظائف الرئيسية:
/// - عرض بطاقات إحصائية سريعة للامتحانات
/// - إظهار التنبيهات والإشعارات المهمة
/// - توفير إجراءات سريعة للمدير
/// - عرض قائمة الامتحانات الحديثة
class ExamsDashboardScreen extends ConsumerStatefulWidget {
  const ExamsDashboardScreen({super.key});

  @override
  ConsumerState<ExamsDashboardScreen> createState() =>
      _ExamsDashboardScreenState();
}

class _ExamsDashboardScreenState extends ConsumerState<ExamsDashboardScreen> {
  @override
  Widget build(BuildContext context) {
    // جلب إحصائيات الامتحانات من المزود
    final examStats = ref.watch(examStatsProvider);

    // جلب قائمة الامتحانات الجارية
    final ongoingExamsAsync = ref.watch(ongoingExamsStreamProvider);

    // جلب قائمة الامتحانات القادمة
    final upcomingExamsAsync = ref.watch(upcomingExamsStreamProvider);

    return Scaffold(
      // شريط التطبيق العلوي مع عنوان الشاشة
      appBar: AppBar(
        title: const Text(
          'لوحة معلومات الامتحانات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[800],
        elevation: 2,
        // أيقونة الإعدادات في الزاوية اليسرى
        actions: [
          IconButton(
            icon: const Icon(Icons.settings, color: Colors.white),
            onPressed: () => _navigateToExamSettings(),
            tooltip: 'إعدادات الامتحانات',
          ),
        ],
      ),

      // محتوى الشاشة الرئيسي
      body: RefreshIndicator(
        // إمكانية السحب للتحديث
        onRefresh: () async {
          // إعادة تحميل البيانات عند السحب للأسفل
          ref.invalidate(examStatsProvider);
          ref.invalidate(ongoingExamsStreamProvider);
          ref.invalidate(upcomingExamsStreamProvider);
        },
        child: SingleChildScrollView(
          // تمكين التمرير العمودي
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // قسم البطاقات الإحصائية السريعة
              _buildStatsCards(examStats),

              const SizedBox(height: 24),

              // قسم التنبيهات والإشعارات
              _buildAlertsSection(ongoingExamsAsync, upcomingExamsAsync),

              const SizedBox(height: 24),

              // قسم الإجراءات السريعة
              _buildQuickActionsSection(),

              const SizedBox(height: 24),

              // قسم الامتحانات الجارية
              _buildOngoingExamsSection(ongoingExamsAsync),

              const SizedBox(height: 24),

              // قسم الامتحانات القادمة
              _buildUpcomingExamsSection(upcomingExamsAsync),
            ],
          ),
        ),
      ),

      // زر الإجراء العائم لإنشاء امتحان جديد
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _navigateToCreateExam(),
        backgroundColor: Colors.green[600],
        icon: const Icon(Icons.add, color: Colors.white),
        label: const Text(
          'إنشاء امتحان جديد',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  /// بناء بطاقات الإحصائيات السريعة
  ///
  /// تعرض أربع بطاقات تحتوي على:
  /// - إجمالي الامتحانات
  /// - الامتحانات الجارية
  /// - الامتحانات القادمة
  /// - الامتحانات المكتملة
  Widget _buildStatsCards(ExamStats stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        const Text(
          'الإحصائيات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),

        // شبكة البطاقات الإحصائية
        GridView.count(
          // تعطيل التمرير لأن الشاشة كاملة قابلة للتمرير
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2, // عمودين في كل صف
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.5, // نسبة العرض إلى الارتفاع
          children: [
            // بطاقة إجمالي الامتحانات
            _buildStatCard(
              title: 'إجمالي الامتحانات',
              value: stats.totalExams.toString(),
              icon: Icons.quiz,
              color: Colors.blue,
              subtitle: 'جميع الامتحانات',
            ),

            // بطاقة الامتحانات الجارية
            _buildStatCard(
              title: 'الامتحانات الجارية',
              value: stats.ongoingExams.toString(),
              icon: Icons.play_circle_filled,
              color: Colors.orange,
              subtitle: 'تحتاج متابعة',
            ),

            // بطاقة الامتحانات القادمة
            _buildStatCard(
              title: 'الامتحانات القادمة',
              value: stats.upcomingExams.toString(),
              icon: Icons.schedule,
              color: Colors.green,
              subtitle: 'في الانتظار',
            ),

            // بطاقة الامتحانات المكتملة
            _buildStatCard(
              title: 'الامتحانات المكتملة',
              value: stats.completedExams.toString(),
              icon: Icons.check_circle,
              color: Colors.purple,
              subtitle:
                  '${stats.completionPercentage.toStringAsFixed(1)}% مكتملة',
            ),
          ],
        ),
      ],
    );
  }

  /// بناء بطاقة إحصائية واحدة
  ///
  /// [title] عنوان البطاقة
  /// [value] القيمة الرقمية
  /// [icon] أيقونة البطاقة
  /// [color] لون البطاقة
  /// [subtitle] نص فرعي إضافي
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
    required String subtitle,
  }) {
    return CustomCard(
      // استخدام الويدجت المخصص للبطاقات
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          // تدرج لوني جميل للخلفية
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // الأيقونة في الأعلى
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),

            // القيمة الرقمية الكبيرة
            Text(
              value,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),

            // عنوان البطاقة
            Text(
              title,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 2),

            // النص الفرعي
            Text(
              subtitle,
              style: TextStyle(fontSize: 10, color: Colors.grey[600]),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم التنبيهات والإشعارات
  ///
  /// يعرض التنبيهات المهمة مثل:
  /// - امتحانات تحتاج متابعة عاجلة
  /// - درجات لم تُدخل بعد
  /// - تعارض في الجداول
  Widget _buildAlertsSection(
    AsyncValue<List<ExamModel>> ongoingExams,
    AsyncValue<List<ExamModel>> upcomingExams,
  ) {
    // حساب عدد التنبيهات
    int alertCount = 0;
    List<Widget> alerts = [];

    // التحقق من الامتحانات الجارية التي تحتاج متابعة
    ongoingExams.whenData((exams) {
      if (exams.isNotEmpty) {
        alertCount++;
        alerts.add(
          _buildAlertItem(
            icon: Icons.warning,
            color: Colors.orange,
            title: 'امتحانات جارية تحتاج متابعة',
            subtitle: '${exams.length} امتحان جاري حالياً',
            onTap: () => _navigateToOngoingExams(),
          ),
        );
      }
    });

    // التحقق من الامتحانات القادمة قريباً
    upcomingExams.whenData((exams) {
      final soonExams = exams.where((exam) => exam.daysRemaining <= 3).toList();
      if (soonExams.isNotEmpty) {
        alertCount++;
        alerts.add(
          _buildAlertItem(
            icon: Icons.schedule,
            color: Colors.blue,
            title: 'امتحانات قادمة قريباً',
            subtitle: '${soonExams.length} امتحان خلال 3 أيام',
            onTap: () => _navigateToUpcomingExams(),
          ),
        );
      }
    });

    // إذا لم توجد تنبيهات، عرض رسالة إيجابية
    if (alertCount == 0) {
      alerts.add(
        _buildAlertItem(
          icon: Icons.check_circle,
          color: Colors.green,
          title: 'كل شيء على ما يرام',
          subtitle: 'لا توجد تنبيهات في الوقت الحالي',
          onTap: null,
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع عدد التنبيهات
        Row(
          children: [
            const Text(
              'التنبيهات والإشعارات',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            if (alertCount > 0) ...[
              const SizedBox(width: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  alertCount.toString(),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 12),

        // قائمة التنبيهات
        ...alerts,
      ],
    );
  }

  /// بناء عنصر تنبيه واحد
  Widget _buildAlertItem({
    required IconData icon,
    required Color color,
    required String title,
    required String subtitle,
    VoidCallback? onTap,
  }) {
    return CustomCard(
      child: ListTile(
        // الأيقونة على اليمين
        leading: CircleAvatar(
          backgroundColor: color.withValues(alpha: 0.1),
          child: Icon(icon, color: color),
        ),

        // العنوان والوصف
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
        ),
        subtitle: Text(
          subtitle,
          style: TextStyle(color: Colors.grey[600], fontSize: 12),
        ),

        // سهم للانتقال (إذا كان هناك إجراء)
        trailing:
            onTap != null
                ? Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[400],
                )
                : null,

        // الإجراء عند النقر
        onTap: onTap,
      ),
    );
  }

  /// بناء قسم الإجراءات السريعة
  ///
  /// يحتوي على أزرار للإجراءات الشائعة مثل:
  /// - إنشاء امتحان جديد
  /// - عرض التقارير
  /// - إدارة الجداول
  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم
        const Text(
          'الإجراءات السريعة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 12),

        // شبكة الأزرار
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 3, // ثلاثة أعمدة
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.2,
          children: [
            // زر إنشاء امتحان جديد
            _buildQuickActionButton(
              icon: Icons.add_circle,
              label: 'إنشاء امتحان',
              color: Colors.green,
              onTap: () => _navigateToCreateExam(),
            ),

            // زر عرض التقارير
            _buildQuickActionButton(
              icon: Icons.analytics,
              label: 'التقارير',
              color: Colors.blue,
              onTap: () => _navigateToReports(),
            ),

            // زر إدارة الجداول
            _buildQuickActionButton(
              icon: Icons.calendar_today,
              label: 'إدارة الجداول',
              color: Colors.purple,
              onTap: () => _navigateToScheduleManagement(),
            ),
          ],
        ),
      ],
    );
  }

  /// بناء زر إجراء سريع
  Widget _buildQuickActionButton({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return CustomCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // الأيقونة
              Icon(icon, size: 28, color: color),
              const SizedBox(height: 8),

              // النص
              Text(
                label,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء قسم الامتحانات الجارية
  ///
  /// يعرض قائمة بالامتحانات التي تجري حالياً
  /// مع معلومات مهمة مثل الوقت المتبقي وعدد الطلاب
  Widget _buildOngoingExamsSection(
    AsyncValue<List<ExamModel>> ongoingExamsAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع زر "عرض الكل"
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الامتحانات الجارية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToOngoingExams(),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // محتوى القسم حسب حالة البيانات
        ongoingExamsAsync.when(
          // حالة التحميل
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: LoadingIndicator(),
                ),
              ),

          // حالة الخطأ
          error:
              (error, stack) => CustomCard(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red[300],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'خطأ في تحميل الامتحانات الجارية',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        error.toString(),
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

          // حالة نجاح تحميل البيانات
          data: (exams) {
            // إذا لم توجد امتحانات جارية
            if (exams.isEmpty) {
              return CustomCard(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 48,
                        color: Colors.green[300],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لا توجد امتحانات جارية حالياً',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'جميع الامتحانات مكتملة أو لم تبدأ بعد',
                        style: TextStyle(color: Colors.grey[500], fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            }

            // عرض قائمة الامتحانات الجارية (أول 3 فقط)
            final displayExams = exams.take(3).toList();
            return Column(
              children:
                  displayExams
                      .map(
                        (exam) =>
                            _buildExamListItem(exam: exam, isOngoing: true),
                      )
                      .toList(),
            );
          },
        ),
      ],
    );
  }

  /// بناء قسم الامتحانات القادمة
  ///
  /// يعرض قائمة بالامتحانات المجدولة قريباً
  /// مع العد التنازلي والمعلومات المهمة
  Widget _buildUpcomingExamsSection(
    AsyncValue<List<ExamModel>> upcomingExamsAsync,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // عنوان القسم مع زر "عرض الكل"
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'الامتحانات القادمة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
            ),
            TextButton(
              onPressed: () => _navigateToUpcomingExams(),
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 12),

        // محتوى القسم حسب حالة البيانات
        upcomingExamsAsync.when(
          // حالة التحميل
          loading:
              () => const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: LoadingIndicator(),
                ),
              ),

          // حالة الخطأ
          error:
              (error, stack) => CustomCard(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: Colors.red[300],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'خطأ في تحميل الامتحانات القادمة',
                        style: TextStyle(
                          color: Colors.red[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ),

          // حالة نجاح تحميل البيانات
          data: (exams) {
            // إذا لم توجد امتحانات قادمة
            if (exams.isEmpty) {
              return CustomCard(
                child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: Column(
                    children: [
                      Icon(
                        Icons.event_available,
                        size: 48,
                        color: Colors.blue[300],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'لا توجد امتحانات مجدولة',
                        style: TextStyle(
                          color: Colors.grey[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'يمكنك إنشاء امتحان جديد من الزر أدناه',
                        style: TextStyle(color: Colors.grey[500], fontSize: 12),
                      ),
                    ],
                  ),
                ),
              );
            }

            // عرض قائمة الامتحانات القادمة (أول 3 فقط)
            final displayExams = exams.take(3).toList();
            return Column(
              children:
                  displayExams
                      .map(
                        (exam) =>
                            _buildExamListItem(exam: exam, isOngoing: false),
                      )
                      .toList(),
            );
          },
        ),
      ],
    );
  }

  /// بناء عنصر امتحان في القائمة
  ///
  /// يعرض معلومات الامتحان في شكل بطاقة أنيقة
  /// [exam] بيانات الامتحان
  /// [isOngoing] هل الامتحان جاري حالياً أم قادم
  Widget _buildExamListItem({
    required ExamModel exam,
    required bool isOngoing,
  }) {
    return CustomCard(
      child: ListTile(
        // أيقونة حالة الامتحان
        leading: CircleAvatar(
          backgroundColor:
              isOngoing
                  ? Colors.orange.withValues(alpha: 0.1)
                  : Colors.blue.withValues(alpha: 0.1),
          child: Icon(
            isOngoing ? Icons.play_circle_filled : Icons.schedule,
            color: isOngoing ? Colors.orange : Colors.blue,
          ),
        ),

        // اسم الامتحان ونوعه
        title: Text(
          exam.name,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
        ),

        // معلومات إضافية
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 4),
            Text(
              '${exam.type.arabicName} • ${exam.semester}',
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            ),
            const SizedBox(height: 2),
            Text(
              isOngoing
                  ? 'جاري حالياً • ${exam.classIds.length} صف'
                  : 'خلال ${exam.daysRemaining} يوم • ${exam.classIds.length} صف',
              style: TextStyle(
                color: isOngoing ? Colors.orange[700] : Colors.blue[700],
                fontSize: 11,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),

        // سهم للانتقال لتفاصيل الامتحان
        trailing: Icon(
          Icons.arrow_forward_ios,
          size: 16,
          color: Colors.grey[400],
        ),

        // الانتقال لتفاصيل الامتحان عند النقر
        onTap: () => _navigateToExamDetails(exam.id),
      ),
    );
  }

  // ===================================================================
  // دوال التنقل والإجراءات
  // ===================================================================

  /// الانتقال لشاشة إعدادات الامتحانات
  void _navigateToExamSettings() {
    Navigator.pushNamed(context, '/admin/exam-settings');
  }

  /// الانتقال لشاشة إنشاء امتحان جديد
  void _navigateToCreateExam() {
    Navigator.pushNamed(context, '/admin/create-exam');
  }

  /// الانتقال لشاشة التقارير والتحليلات
  void _navigateToReports() {
    Navigator.pushNamed(context, '/admin/exam-analytics');
  }

  /// الانتقال لشاشة إدارة جداول الامتحانات
  void _navigateToScheduleManagement() {
    Navigator.pushNamed(context, '/admin/exam-schedules');
  }

  /// الانتقال لشاشة الامتحانات الجارية
  void _navigateToOngoingExams() {
    Navigator.pushNamed(context, '/admin/ongoing-exams');
  }

  /// الانتقال لشاشة الامتحانات القادمة
  void _navigateToUpcomingExams() {
    Navigator.pushNamed(context, '/admin/upcoming-exams');
  }

  /// الانتقال لتفاصيل امتحان محدد
  /// [examId] معرف الامتحان
  void _navigateToExamDetails(String examId) {
    Navigator.pushNamed(context, '/admin/exam-details', arguments: examId);
  }
}
