import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// ويدجت متخصص لعرض وإدارة الأنشطة والفعاليات في لوحة التحكم.
class ActivitiesManagementTab extends ConsumerWidget {
  final Function(ActivityModel) onEdit;
  final Function(ActivityModel) onDelete;

  const ActivitiesManagementTab({
    super.key,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final activitiesAsyncValue = ref.watch(activitiesStreamProvider);

    return activitiesAsyncValue.when(
      loading: () => const LoadingIndicator(),
      error: (err, stack) =>
          ErrorMessage(message: 'حدث خطأ أثناء جلب الأنشطة: $err'),
      data: (snapshot) {
        final activities = snapshot.docs;
        if (activities.isEmpty) {
          return const Center(
              child: Text('لا توجد أنشطة حالياً. قم بإضافة نشاط جديد.'));
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount: activities.length,
          itemBuilder: (context, index) {
            final doc = activities[index];
            final activity =
                ActivityModel.fromMap(doc.data() as Map<String, dynamic>, doc.id);
            final formattedDate =
                DateFormat('yyyy/MM/dd').format(activity.date);

            return CustomCard(
              child: ListTile(
                leading: activity.imageUrls.isNotEmpty
                    ? ClipRRect(
                        borderRadius: BorderRadius.circular(8.0),
                        child: Image.network(
                          activity.imageUrls.first, // عرض أول صورة كصورة مصغرة
                          width: 50,
                          height: 50,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              const Icon(Icons.image_not_supported, size: 40),
                        ),
                      )
                    : const Icon(Icons.event, size: 40),
                title: Text(activity.title,
                    style: const TextStyle(fontWeight: FontWeight.bold)),
                subtitle:
                    Text('${activity.description}\nالتاريخ: $formattedDate'),
                isThreeLine: true,
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit, color: Colors.blue),
                      tooltip: 'تعديل',
                      onPressed: () => onEdit(activity),
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete, color: Colors.red),
                      tooltip: 'حذف',
                      onPressed: () => onDelete(activity),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }
}
