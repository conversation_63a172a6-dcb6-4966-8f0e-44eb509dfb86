
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// صفحة الملف الشخصي للطالب
class StudentProfileScreen extends ConsumerWidget {
  final String studentId;
  const StudentProfileScreen({super.key, required this.studentId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentAsyncValue = ref.watch(currentStudentProvider(studentId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('الملف الشخصي'),
      ),
      body: studentAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error: (err, stack) => ErrorMessage(message: 'خطأ: $err'),
        data: (student) {
          if (student == null) {
            return const ErrorMessage(message: 'لم يتم العثور على بيانات الطالب.');
          }
          return _buildProfileView(context, student);
        },
      ),
    );
  }

  Widget _buildProfileView(BuildContext context, StudentModel student) {
    return ListView(
      padding: const EdgeInsets.all(16.0),
      children: [
        Center(
          child: CircleAvatar(
            radius: 50,
            backgroundImage: student.profileImageUrl != null
                ? NetworkImage(student.profileImageUrl!)
                : null,
            child: student.profileImageUrl == null
                ? const Icon(Icons.person, size: 50)
                : null,
          ),
        ),
        const SizedBox(height: 16),
        Text(
          student.name,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.headlineSmall,
        ),
        const SizedBox(height: 24),
        _buildInfoTile(Icons.person, 'الاسم الكامل', student.name),
        _buildInfoTile(Icons.email, 'البريد الإلكتروني', student.email),
        _buildInfoTile(Icons.phone, 'رقم الهاتف', student.phoneNumber ?? 'غير متوفر'),
        _buildInfoTile(Icons.cake, 'تاريخ الميلاد', student.dateOfBirth?.toString() ?? 'غير متوفر'),
        _buildInfoTile(Icons.location_on, 'العنوان', student.address ?? 'غير متوفر'),
      ],
    );
  }

  Widget _buildInfoTile(IconData icon, String title, String subtitle) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 8.0),
      child: ListTile(
        leading: Icon(icon),
        title: Text(title),
        subtitle: Text(subtitle),
      ),
    );
  }
}
