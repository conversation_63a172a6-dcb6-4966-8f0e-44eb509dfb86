                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\baha\Baha\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\baha\Baha\Flutter\New\school_management_system\build\app\intermediates\cxx\Debug\3b1n5fd0\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\baha\Baha\Flutter\New\school_management_system\build\app\intermediates\cxx\Debug\3b1n5fd0\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=Debug
-BF:\baha\Baha\Flutter\New\school_management_system\android\app\.cxx\Debug\3b1n5fd0\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2