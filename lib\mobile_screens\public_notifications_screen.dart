import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';

/// شاشة لعرض الإعلانات العامة من المدرسة باستخدام Riverpod
class PublicNotificationsScreen extends ConsumerWidget {
  const PublicNotificationsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مشاهدة (watch) الـ provider لجلب بيانات الإعلانات
    final announcementsAsyncValue = ref.watch(publicAnnouncementsStreamProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('الإعلانات والفعاليات'),
        centerTitle: true,
      ),
      body: announcementsAsyncValue.when(
        // في حالة تحميل البيانات
        loading: () => const LoadingIndicator(),
        // في حالة حدوث خطأ
        error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
        // في حالة نجاح جلب البيانات
        data: (snapshot) {
          final announcements = snapshot.docs;
          if (announcements.isEmpty) {
            return const Center(
              child: Text(
                'لا توجد إعلانات حالياً',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            );
          }

          // عرض قائمة الإعلانات بتصميم محسن
          return ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 20.0),
            itemCount: announcements.length,
            itemBuilder: (context, index) {
              final data = announcements[index].data() as Map<String, dynamic>;
              
              // تنسيق التاريخ والوقت
              String formattedDate = 'التاريخ غير متوفر';
              if (data['createdAt'] != null) {
                final timestamp = data['createdAt'] as Timestamp;
                final dateTime = timestamp.toDate();
                // استخدام تنسيق مألوف باللغة العربية
                formattedDate = DateFormat.yMMMMd('ar').add_jm().format(dateTime);
              }

              return Card(
                elevation: 3,
                margin: const EdgeInsets.only(bottom: 16.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(15),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data['title'] ?? 'بلا عنوان',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColorDark,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Text(
                        data['content'] ?? '',
                        style: TextStyle(
                          fontSize: 15,
                          color: Colors.grey.shade800,
                          height: 1.5,
                        ),
                      ),
                      const Divider(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          Icon(Icons.access_time, size: 14, color: Colors.grey.shade600),
                          const SizedBox(width: 6),
                          Text(
                            formattedDate,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
