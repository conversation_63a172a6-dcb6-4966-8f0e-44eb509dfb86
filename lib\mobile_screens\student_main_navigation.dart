import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:school_management_system/mobile_screens/student_assignments_screen.dart';
import 'package:school_management_system/mobile_screens/student_attendance_screen.dart';
import 'package:school_management_system/mobile_screens/student_fees_screen.dart';
import 'package:school_management_system/mobile_screens/student_grades_screen.dart';
import 'package:school_management_system/mobile_screens/student_home_page.dart';
import 'package:school_management_system/mobile_screens/student_notes_screen.dart';
import 'package:school_management_system/mobile_screens/student_profile_screen.dart';
import 'package:school_management_system/mobile_screens/student_timetable_screen.dart';
import 'package:school_management_system/widgets/shared_app_drawer.dart';
// استيراد الشاشات الجديدة للطلاب
import 'package:school_management_system/student_screens/exam_preparation_screen.dart';
import 'package:school_management_system/student_screens/student_results_screen.dart';
import 'package:school_management_system/student_screens/student_exam_schedule_screen.dart';

/// الواجهة الرئيسية لتطبيق الطالب بعد تسجيل الدخول
/// تحتوي على شريط تنقل سفلي للتنقل بين الشاشات الرئيسية
class StudentMainNavigation extends StatefulWidget {
  const StudentMainNavigation({super.key});

  @override
  State<StudentMainNavigation> createState() => _StudentMainNavigationState();
}

class _StudentMainNavigationState extends State<StudentMainNavigation> {
  int _selectedIndex = 0;
  late final String _studentId;
  late final List<Widget> _pages;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  void initState() {
    super.initState();
    // جلب معرّف الطالب الحالي عند بناء الواجهة
    _studentId = FirebaseAuth.instance.currentUser!.uid;

    // بناء قائمة الصفحات مع تمرير معرّف الطالب
    _pages = <Widget>[
      StudentHomeScreen(studentId: _studentId),
      StudentAssignmentsScreen(studentId: _studentId),
      StudentAttendanceScreen(studentId: _studentId),
      StudentFeesScreen(studentId: _studentId),
      StudentGradesScreen(studentId: _studentId),
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      appBar: AppBar(
        title: Text(
          [
            'الرئيسية',
            'الواجبات',
            'الحضور والغياب',
            'الرسوم الدراسية',
            'الدرجات',
          ][_selectedIndex],
        ),
        leading: IconButton(
          icon: const Icon(Icons.menu),
          onPressed: () => _scaffoldKey.currentState?.openDrawer(),
        ),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            DrawerHeader(
              decoration: BoxDecoration(color: Theme.of(context).primaryColor),
              child: const Text(
                'القائمة',
                style: TextStyle(color: Colors.white, fontSize: 24),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.person_outline),
              title: const Text('الملف الشخصي'),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentProfileScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.schedule_outlined),
              title: const Text('الجدول الدراسي'),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentTimetableScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.note_alt_outlined),
              title: const Text('الملاحظات'),
              onTap: () {
                Navigator.pop(context); // Close the drawer
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) => StudentNotesScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            const Divider(),
            // الشاشات الجديدة للطلاب
            ListTile(
              leading: const Icon(Icons.quiz_outlined),
              title: const Text('الاستعداد للامتحان'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            ExamPreparationScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.assessment_outlined),
              title: const Text('نتائجي'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentResultsScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.calendar_today_outlined),
              title: const Text('جدول امتحاناتي'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentExamScheduleScreen(studentId: _studentId),
                  ),
                );
              },
            ),
            const Divider(),
            ListTile(
              leading: const Icon(Icons.logout),
              title: const Text('تسجيل الخروج'),
              onTap: () {
                FirebaseAuth.instance.signOut();
                // You might want to navigate to the login screen after logout
              },
            ),
          ],
        ),
      ),
      body: IndexedStack(index: _selectedIndex, children: _pages),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.home_outlined),
            activeIcon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.assignment_outlined),
            activeIcon: Icon(Icons.assignment),
            label: 'الواجبات',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.check_circle_outline),
            activeIcon: Icon(Icons.check_circle),
            label: 'الحضور',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.monetization_on_outlined),
            activeIcon: Icon(Icons.monetization_on),
            label: 'الرسوم',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.grade_outlined),
            activeIcon: Icon(Icons.grade),
            label: 'الدرجات',
          ),
        ],
        currentIndex: _selectedIndex,
        onTap: _onItemTapped,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey.shade600,
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
