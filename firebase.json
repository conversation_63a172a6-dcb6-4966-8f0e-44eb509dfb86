{"flutter": {"platforms": {"android": {"default": {"projectId": "school-management-system-412fe", "appId": "1:361093905515:android:494d6b9902aa9ded1a052e", "fileOutput": "android/app/google-services.json"}}, "dart": {"lib/firebase_options.dart": {"projectId": "school-management-system-412fe", "configurations": {"android": "1:361093905515:android:494d6b9902aa9ded1a052e", "ios": "1:361093905515:ios:cc0cd9c81e1486661a052e", "macos": "1:361093905515:ios:cc0cd9c81e1486661a052e", "web": "1:361093905515:web:13d875ef9641f4991a052e", "windows": "1:361093905515:web:665952879902ef541a052e"}}}}}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "storage": {"rules": "storage.rules"}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}}