import 'package:cloud_firestore/cloud_firestore.dart';

/// يمثل هذا الكائن معلماً بخصائصه الأساسية.
class TeacherModel {
  final String id;
  final String name;
  final String email;
  // يمكن إضافة حقول أخرى مثل المواد التي يدرسها، رقم الهاتف، إلخ.

  TeacherModel({
    required this.id,
    required this.name,
    required this.email,
  });

  /// دالة مصنعية لإنشاء كائن TeacherModel من مستند Firestore.
  factory TeacherModel.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>? ?? {};
    return TeacherModel(
      id: doc.id,
      name: data['name'] as String? ?? 'اسم غير متوفر',
      email: data['email'] as String? ?? 'بريد إلكتروني غير متوفر',
    );
  }
}
