import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة أولياء الأمور من Firestore.
final guardiansStreamProvider = StreamProvider.autoDispose<List<GuardianModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // سنفترض أن دالة `getGuardiansStream` ستتم إضافتها إلى FirebaseService
  return firebaseService.getGuardiansStream();
});

/// Provider لتخزين نص البحث عن أولياء الأمور.
final guardianSearchQueryProvider = StateProvider.autoDispose<String>((ref) => '');

/// Provider لفلترة قائمة أولياء الأمور بناءً على نص البحث.
final filteredGuardiansProvider = Provider.autoDispose<List<GuardianModel>>((ref) {
  final guardiansAsyncValue = ref.watch(guardiansStreamProvider);
  final searchQuery = ref.watch(guardianSearchQueryProvider).toLowerCase();

  return guardiansAsyncValue.when(
    data: (guardians) {
      if (searchQuery.isEmpty) {
        return guardians;
      }
      return guardians.where((guardian) {
        final nameMatches = guardian.name.toLowerCase().contains(searchQuery);
        final emailMatches = guardian.email.toLowerCase().contains(searchQuery);
        return nameMatches || emailMatches;
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// Provider لجلب قائمة جميع الطلاب (لديالوج ربط الطلاب).
final allStudentsProvider = StreamProvider.autoDispose<List<StudentModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // سنفترض أن دالة `getAllStudentsStream` ستتم إضافتها إلى FirebaseService
  return firebaseService.getAllStudentsStream();
});


//======================================================================
// Providers for Guardian Home Page
//======================================================================

/// مزود Future لجلب بيانات جميع الأبناء المرتبطين بولي أمر معين.
/// يستخدم `family` لتمرير معرف ولي الأمر (`guardianId`).
final guardianChildrenProvider =
    FutureProvider.autoDispose.family<List<Map<String, dynamic>>, String>((ref, guardianId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getGuardianChildrenData(guardianId);
});
