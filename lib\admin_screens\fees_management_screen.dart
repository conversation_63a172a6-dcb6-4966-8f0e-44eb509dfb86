import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/student_fee_card.dart';
import 'package:school_management_system/providers/fees_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة إدارة الرسوم الدراسية للطلاب (معاد هيكلتها)
class FeesManagementScreen extends ConsumerWidget {
  const FeesManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentsAsyncValue = ref.watch(studentsForFeesProvider);
    final filteredStudents = ref.watch(filteredStudentsForFeesProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الرسوم الدراسية'),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              onChanged: (value) {
                ref.read(feeSearchQueryProvider.notifier).state = value;
              },
              decoration: const InputDecoration(
                labelText: 'ابحث عن طالب بالاسم أو الرقم الأكاديمي...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
          ),
          Expanded(
            child: studentsAsyncValue.when(
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: 'خطأ: $err'),
              data: (students) {
                if (students.isEmpty) {
                  return const Center(child: Text('لا يوجد طلاب لعرضهم.'));
                }
                if (filteredStudents.isEmpty) {
                  return const Center(child: Text('لم يتم العثور على نتائج.'));
                }
                return ListView.builder(
                  itemCount: filteredStudents.length,
                  itemBuilder: (context, index) {
                    return StudentFeeCard(student: filteredStudents[index]);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}
