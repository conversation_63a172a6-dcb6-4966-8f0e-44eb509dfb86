import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/conversation_model.dart';
import 'package:school_management_system/models/message_model.dart';
import 'package:school_management_system/services/messaging_service.dart';

/// مزودات التواصل المتقدمة
/// 
/// تدير حالة التطبيق المتعلقة بالتواصل والرسائل
/// تشمل المحادثات، الرسائل، الفلاتر، والبحث
/// 
/// المزودات المتوفرة:
/// - مزود خدمة التواصل
/// - مزودات المحادثات والرسائل
/// - مزودات البحث والفلترة
/// - مزودات الإحصائيات
/// - مزودات حالة التطبيق

// ===================================================================
// مزودات الخدمات الأساسية
// ===================================================================

/// مزود خدمة التواصل
final messagingServiceProvider = Provider<MessagingService>((ref) {
  return MessagingService();
});

// ===================================================================
// مزودات المحادثات
// ===================================================================

/// مزود محادثات المستخدم
final userConversationsProvider = StreamProvider.family<List<ConversationModel>, String>(
  (ref, userId) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.getUserConversations(userId);
  },
);

/// مزود محادثة محددة
final conversationProvider = FutureProvider.family<ConversationModel?, String>(
  (ref, conversationId) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.getConversation(conversationId);
  },
);

/// مزود البحث في المحادثات
final conversationSearchProvider = StreamProvider.family<List<ConversationModel>, ConversationSearchParams>(
  (ref, params) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.searchConversations(params.userId, params.query);
  },
);

// ===================================================================
// مزودات الرسائل
// ===================================================================

/// مزود رسائل المحادثة
final conversationMessagesProvider = StreamProvider.family<List<MessageModel>, ConversationMessagesParams>(
  (ref, params) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.getConversationMessages(
      params.conversationId,
      limit: params.limit,
    );
  },
);

/// مزود البحث في الرسائل
final messageSearchProvider = StreamProvider.family<List<MessageModel>, MessageSearchParams>(
  (ref, params) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.searchMessages(params.conversationId, params.query);
  },
);

// ===================================================================
// مزودات الحالة والفلاتر
// ===================================================================

/// مزود نص البحث في المحادثات
final conversationSearchQueryProvider = StateProvider<String>((ref) => '');

/// مزود نص البحث في الرسائل
final messageSearchQueryProvider = StateProvider<String>((ref) => '');

/// مزود فلتر نوع المحادثة
final conversationTypeFilterProvider = StateProvider<ConversationType?>((ref) => null);

/// مزود فلتر أولوية المحادثة
final conversationPriorityFilterProvider = StateProvider<ConversationPriority?>((ref) => null);

/// مزود فلتر حالة المحادثة
final conversationStatusFilterProvider = StateProvider<ConversationStatus?>((ref) => null);

/// مزود المحادثة المحددة حالياً
final selectedConversationProvider = StateProvider<String?>((ref) => null);

/// مزود حالة تحميل الرسائل
final messagesLoadingProvider = StateProvider<bool>((ref) => false);

/// مزود حالة إرسال الرسالة
final sendingMessageProvider = StateProvider<bool>((ref) => false);

// ===================================================================
// مزودات المحادثات المفلترة
// ===================================================================

/// مزود المحادثات المفلترة حسب النوع والأولوية والحالة
final filteredConversationsProvider = Provider.family<AsyncValue<List<ConversationModel>>, String>(
  (ref, userId) {
    final conversationsAsync = ref.watch(userConversationsProvider(userId));
    final typeFilter = ref.watch(conversationTypeFilterProvider);
    final priorityFilter = ref.watch(conversationPriorityFilterProvider);
    final statusFilter = ref.watch(conversationStatusFilterProvider);
    final searchQuery = ref.watch(conversationSearchQueryProvider);

    return conversationsAsync.when(
      data: (conversations) {
        var filtered = conversations;

        // فلترة حسب النوع
        if (typeFilter != null) {
          filtered = filtered.where((conv) => conv.type == typeFilter).toList();
        }

        // فلترة حسب الأولوية
        if (priorityFilter != null) {
          filtered = filtered.where((conv) => conv.priority == priorityFilter).toList();
        }

        // فلترة حسب الحالة
        if (statusFilter != null) {
          filtered = filtered.where((conv) => conv.status == statusFilter).toList();
        }

        // فلترة حسب البحث
        if (searchQuery.isNotEmpty) {
          filtered = filtered.where((conv) =>
              conv.subject.toLowerCase().contains(searchQuery.toLowerCase()) ||
              conv.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
              conv.lastMessageText?.toLowerCase().contains(searchQuery.toLowerCase()) == true).toList();
        }

        return AsyncValue.data(filtered);
      },
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    );
  },
);

/// مزود المحادثات غير المقروءة
final unreadConversationsProvider = Provider.family<AsyncValue<List<ConversationModel>>, String>(
  (ref, userId) {
    final conversationsAsync = ref.watch(userConversationsProvider(userId));

    return conversationsAsync.when(
      data: (conversations) {
        final unread = conversations.where((conv) => 
            (conv.unreadCounts[userId] ?? 0) > 0).toList();
        return AsyncValue.data(unread);
      },
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    );
  },
);

/// مزود المحادثات المهمة
final importantConversationsProvider = Provider.family<AsyncValue<List<ConversationModel>>, String>(
  (ref, userId) {
    final conversationsAsync = ref.watch(userConversationsProvider(userId));

    return conversationsAsync.when(
      data: (conversations) {
        final important = conversations.where((conv) => 
            conv.priority == ConversationPriority.high || 
            conv.priority == ConversationPriority.urgent ||
            conv.priority == ConversationPriority.critical).toList();
        return AsyncValue.data(important);
      },
      loading: () => const AsyncValue.loading(),
      error: (error, stack) => AsyncValue.error(error, stack),
    );
  },
);

// ===================================================================
// مزودات الإحصائيات
// ===================================================================

/// مزود إحصائيات التواصل
final communicationStatsProvider = FutureProvider.family<Map<String, dynamic>, String>(
  (ref, userId) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.getCommunicationStats(userId);
  },
);

/// مزود عدد الرسائل غير المقروءة
final unreadMessageCountProvider = FutureProvider.family<int, String>(
  (ref, userId) {
    final messagingService = ref.watch(messagingServiceProvider);
    return messagingService.getUnreadMessageCount(userId);
  },
);

// ===================================================================
// مزودات متحكمات النماذج
// ===================================================================

/// مزود متحكم إنشاء محادثة جديدة
final newConversationControllerProvider = StateNotifierProvider<NewConversationController, NewConversationState>(
  (ref) => NewConversationController(ref.read(messagingServiceProvider)),
);

/// مزود متحكم إرسال رسالة
final sendMessageControllerProvider = StateNotifierProvider<SendMessageController, SendMessageState>(
  (ref) => SendMessageController(ref.read(messagingServiceProvider)),
);

// ===================================================================
// فئات المعاملات
// ===================================================================

/// معاملات البحث في المحادثات
class ConversationSearchParams {
  final String userId;
  final String query;

  const ConversationSearchParams({
    required this.userId,
    required this.query,
  });
}

/// معاملات رسائل المحادثة
class ConversationMessagesParams {
  final String conversationId;
  final int limit;

  const ConversationMessagesParams({
    required this.conversationId,
    this.limit = 50,
  });
}

/// معاملات البحث في الرسائل
class MessageSearchParams {
  final String conversationId;
  final String query;

  const MessageSearchParams({
    required this.conversationId,
    required this.query,
  });
}

// ===================================================================
// متحكمات الحالة
// ===================================================================

/// حالة إنشاء محادثة جديدة
class NewConversationState {
  final bool isLoading;
  final String? error;
  final String subject;
  final String description;
  final ConversationType type;
  final ConversationPriority priority;
  final List<String> participantIds;
  final String? studentId;

  const NewConversationState({
    this.isLoading = false,
    this.error,
    this.subject = '',
    this.description = '',
    this.type = ConversationType.inquiry,
    this.priority = ConversationPriority.normal,
    this.participantIds = const [],
    this.studentId,
  });

  NewConversationState copyWith({
    bool? isLoading,
    String? error,
    String? subject,
    String? description,
    ConversationType? type,
    ConversationPriority? priority,
    List<String>? participantIds,
    String? studentId,
  }) {
    return NewConversationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      subject: subject ?? this.subject,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      participantIds: participantIds ?? this.participantIds,
      studentId: studentId ?? this.studentId,
    );
  }
}

/// متحكم إنشاء محادثة جديدة
class NewConversationController extends StateNotifier<NewConversationState> {
  final MessagingService _messagingService;

  NewConversationController(this._messagingService) : super(const NewConversationState());

  void updateSubject(String subject) {
    state = state.copyWith(subject: subject);
  }

  void updateDescription(String description) {
    state = state.copyWith(description: description);
  }

  void updateType(ConversationType type) {
    state = state.copyWith(type: type);
  }

  void updatePriority(ConversationPriority priority) {
    state = state.copyWith(priority: priority);
  }

  void updateParticipants(List<String> participantIds) {
    state = state.copyWith(participantIds: participantIds);
  }

  void updateStudent(String? studentId) {
    state = state.copyWith(studentId: studentId);
  }

  Future<String?> createConversation(
    Map<String, ParticipantInfo> participants,
    String createdBy,
    String? studentName,
  ) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final conversationId = await _messagingService.createConversation(
        subject: state.subject,
        description: state.description,
        type: state.type,
        priority: state.priority,
        participantIds: state.participantIds,
        participants: participants,
        createdBy: createdBy,
        studentId: state.studentId,
        studentName: studentName,
      );

      state = state.copyWith(isLoading: false);
      return conversationId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  void reset() {
    state = const NewConversationState();
  }
}

/// حالة إرسال رسالة
class SendMessageState {
  final bool isLoading;
  final String? error;
  final String content;
  final MessageType type;
  final MessagePriority priority;

  const SendMessageState({
    this.isLoading = false,
    this.error,
    this.content = '',
    this.type = MessageType.text,
    this.priority = MessagePriority.normal,
  });

  SendMessageState copyWith({
    bool? isLoading,
    String? error,
    String? content,
    MessageType? type,
    MessagePriority? priority,
  }) {
    return SendMessageState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      content: content ?? this.content,
      type: type ?? this.type,
      priority: priority ?? this.priority,
    );
  }
}

/// متحكم إرسال رسالة
class SendMessageController extends StateNotifier<SendMessageState> {
  final MessagingService _messagingService;

  SendMessageController(this._messagingService) : super(const SendMessageState());

  void updateContent(String content) {
    state = state.copyWith(content: content);
  }

  void updateType(MessageType type) {
    state = state.copyWith(type: type);
  }

  void updatePriority(MessagePriority priority) {
    state = state.copyWith(priority: priority);
  }

  Future<String?> sendMessage({
    required String conversationId,
    required String senderId,
    required String senderName,
    required ParticipantRole senderRole,
    String? replyToMessageId,
    List<MessageAttachment> attachments = const [],
  }) async {
    if (state.content.trim().isEmpty && attachments.isEmpty) {
      state = state.copyWith(error: 'لا يمكن إرسال رسالة فارغة');
      return null;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final messageId = await _messagingService.sendMessage(
        conversationId: conversationId,
        senderId: senderId,
        senderName: senderName,
        senderRole: senderRole,
        content: state.content,
        type: state.type,
        replyToMessageId: replyToMessageId,
        attachments: attachments,
        priority: state.priority,
      );

      state = state.copyWith(isLoading: false, content: '');
      return messageId;
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
      return null;
    }
  }

  void reset() {
    state = const SendMessageState();
  }
}
