import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/models/grade_model.dart';
import 'package:school_management_system/providers/exam_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة إدخال الدرجات للمعلمين
///
/// هذه الشاشة تسمح للمعلمين بإدخال وإدارة درجات الطلاب
/// في الامتحانات المختلفة مع ميزات متقدمة للتحقق والحفظ
///
/// الوظائف الرئيسية:
/// - اختيار الامتحان المراد إدخال درجاته
/// - عرض قائمة الطلاب المسجلين في الامتحان
/// - إدخال الدرجات مع التحقق من الصحة
/// - حفظ تلقائي للدرجات أثناء الإدخال
/// - معاينة الإحصائيات والتوزيع
/// - تصدير النتائج وطباعتها
/// - إرسال إشعارات للطلاب والأولياء
///
/// تدفق العمل:
/// 1. اختيار الامتحان من القائمة المتاحة
/// 2. تحميل قائمة الطلاب المسجلين
/// 3. إدخال الدرجات لكل طالب
/// 4. مراجعة ومعاينة النتائج
/// 5. حفظ ونشر الدرجات
/// 6. إرسال الإشعارات
class GradeEntryScreen extends ConsumerStatefulWidget {
  const GradeEntryScreen({super.key});

  @override
  ConsumerState<GradeEntryScreen> createState() => _GradeEntryScreenState();
}

class _GradeEntryScreenState extends ConsumerState<GradeEntryScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والحالة
  // ===================================================================

  /// متحكم التبويبات الرئيسية
  /// يدير التنقل بين: إدخال الدرجات، المراجعة، الإحصائيات
  late TabController _tabController;

  /// متحكمات النماذج لإدخال البيانات
  final _formKey = GlobalKey<FormState>();
  final _searchController = TextEditingController();

  /// خريطة متحكمات إدخال الدرجات لكل طالب
  /// المفتاح هو معرف الطالب والقيمة هي متحكم النص
  final Map<String, TextEditingController> _gradeControllers = {};

  /// خريطة متحكمات الملاحظات لكل طالب
  final Map<String, TextEditingController> _notesControllers = {};

  // ===================================================================
  // متغيرات الحالة الرئيسية
  // ===================================================================

  /// الامتحان المحدد حالياً لإدخال الدرجات
  ExamModel? _selectedExam;

  /// قائمة الطلاب المسجلين في الامتحان المحدد
  List<StudentModel> _students = [];

  /// خريطة الدرجات المدخلة لكل طالب
  /// المفتاح هو معرف الطالب والقيمة هي الدرجة
  Map<String, double> _enteredGrades = {};

  /// خريطة الملاحظات لكل طالب
  Map<String, String> _studentNotes = {};

  /// خريطة حالة الحفظ لكل طالب
  /// تتبع ما إذا كانت درجة الطالب محفوظة أم لا
  Map<String, bool> _savedStates = {};

  /// حالة تحميل البيانات
  bool _isLoading = false;

  /// حالة حفظ البيانات
  bool _isSaving = false;

  /// حالة نشر النتائج
  bool _isPublishing = false;

  // ===================================================================
  // إعدادات إدخال الدرجات
  // ===================================================================

  /// الدرجة العظمى للامتحان
  double _maxGrade = 100.0;

  /// الدرجة الصغرى المسموحة
  double _minGrade = 0.0;

  /// هل يُسمح بالدرجات العشرية؟
  bool _allowDecimalGrades = true;

  /// عدد المنازل العشرية المسموحة
  int _decimalPlaces = 1;

  /// هل تم نشر النتائج؟
  bool _areResultsPublished = false;

  /// تاريخ آخر حفظ
  DateTime? _lastSaved;

  /// عدد الدرجات المحفوظة
  int _savedGradesCount = 0;

  /// عدد الدرجات المعلقة (غير محفوظة)
  int _pendingGradesCount = 0;

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم التبويبات مع 3 تبويبات رئيسية
    _tabController = TabController(length: 3, vsync: this);

    // تحميل البيانات الأولية
    _loadInitialData();

    // إضافة مستمع لتغييرات التبويبات
    _tabController.addListener(_onTabChanged);
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _tabController.dispose();
    _searchController.dispose();

    // تنظيف متحكمات الدرجات والملاحظات
    for (final controller in _gradeControllers.values) {
      controller.dispose();
    }
    for (final controller in _notesControllers.values) {
      controller.dispose();
    }

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'إدخال الدرجات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.indigo[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.edit), text: 'إدخال الدرجات'),
            Tab(icon: Icon(Icons.preview), text: 'مراجعة النتائج'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر البحث
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () => _showSearchDialog(),
            tooltip: 'البحث عن طالب',
          ),

          // زر الحفظ السريع
          if (_pendingGradesCount > 0)
            IconButton(
              icon:
                  _isSaving
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : const Icon(Icons.save, color: Colors.white),
              onPressed: _isSaving ? null : () => _saveAllGrades(),
              tooltip: 'حفظ جميع الدرجات',
            ),

          // زر النشر
          if (_selectedExam != null && _savedGradesCount > 0)
            IconButton(
              icon:
                  _isPublishing
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : Icon(
                        _areResultsPublished
                            ? Icons.publish
                            : Icons.publish_outlined,
                        color: Colors.white,
                      ),
              onPressed: _isPublishing ? null : () => _publishResults(),
              tooltip: _areResultsPublished ? 'إلغاء النشر' : 'نشر النتائج',
            ),
        ],
      ),

      // محتوى التبويبات
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب إدخال الدرجات
          _buildGradeEntryTab(),

          // تبويب مراجعة النتائج
          _buildReviewTab(),

          // تبويب الإحصائيات
          _buildStatisticsTab(),
        ],
      ),

      // شريط المعلومات السفلي
      bottomNavigationBar: _buildBottomInfoBar(),
    );
  }

  /// بناء تبويب إدخال الدرجات
  ///
  /// هذا التبويب يحتوي على:
  /// - اختيار الامتحان
  /// - قائمة الطلاب مع حقول إدخال الدرجات
  /// - أزرار الحفظ والإجراءات
  Widget _buildGradeEntryTab() {
    return Column(
      children: [
        // قسم اختيار الامتحان
        _buildExamSelectionSection(),

        // قائمة الطلاب والدرجات (تظهر فقط بعد اختيار الامتحان)
        if (_selectedExam != null) Expanded(child: _buildStudentsGradesList()),
      ],
    );
  }

  /// بناء قسم اختيار الامتحان
  ///
  /// يعرض قائمة بالامتحانات المتاحة للمعلم لإدخال درجاتها
  Widget _buildExamSelectionSection() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      color: Colors.grey[50],
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Row(
            children: [
              Icon(Icons.quiz, color: Colors.indigo[600], size: 24),
              const SizedBox(width: 12),
              const Text(
                'اختيار الامتحان',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'اختر الامتحان الذي تريد إدخال درجاته',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 16),

          // قائمة الامتحانات المتاحة
          Consumer(
            builder: (context, ref, child) {
              final upcomingExamsAsync = ref.watch(upcomingExamsStreamProvider);

              return upcomingExamsAsync.when(
                loading:
                    () => const Center(
                      child: Padding(
                        padding: EdgeInsets.all(20.0),
                        child: LoadingIndicator(),
                      ),
                    ),
                error:
                    (error, stack) => Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.error_outline,
                            size: 48,
                            color: Colors.red[300],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'خطأ في تحميل الامتحانات',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.red[700],
                            ),
                          ),
                        ],
                      ),
                    ),
                data: (exams) {
                  if (exams.isEmpty) {
                    return Center(
                      child: Column(
                        children: [
                          Icon(
                            Icons.quiz_outlined,
                            size: 64,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'لا توجد امتحانات متاحة',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    );
                  }

                  return SizedBox(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: exams.length,
                      itemBuilder: (context, index) {
                        final exam = exams[index];
                        return _buildExamCard(exam);
                      },
                    ),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة امتحان واحد للاختيار
  ///
  /// تعرض معلومات الامتحان مع إمكانية الاختيار
  Widget _buildExamCard(ExamModel exam) {
    final isSelected = _selectedExam?.id == exam.id;

    return Container(
      width: 280,
      margin: const EdgeInsets.only(right: 12),
      child: InkWell(
        onTap: () => _selectExam(exam),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? Colors.indigo[600]! : Colors.grey[300]!,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(12),
            color: isSelected ? Colors.indigo[50] : Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // رأس البطاقة
              Row(
                children: [
                  // أيقونة الامتحان
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: isSelected ? Colors.indigo[600] : Colors.grey[200],
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.quiz,
                      color: isSelected ? Colors.white : Colors.grey[600],
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),

                  // اسم الامتحان
                  Expanded(
                    child: Text(
                      exam.name,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                        color: isSelected ? Colors.indigo[800] : Colors.black87,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),

                  // مؤشر الاختيار
                  if (isSelected)
                    Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.indigo[600],
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 12,
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 12),

              // تفاصيل الامتحان
              Row(
                children: [
                  Icon(Icons.category, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    exam.type.arabicName,
                    style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                  ),
                  const SizedBox(width: 12),
                  Icon(Icons.calendar_today, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${exam.startDate.day}/${exam.startDate.month}',
                    style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // عدد الصفوف
              Row(
                children: [
                  Icon(Icons.class_, size: 12, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    '${exam.classIds.length} صف',
                    style: TextStyle(fontSize: 11, color: Colors.grey[600]),
                  ),
                  const Spacer(),

                  // حالة إدخال الدرجات
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _getGradeStatusColor(exam),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getGradeStatusText(exam),
                      style: const TextStyle(
                        fontSize: 10,
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال الإجراءات والتفاعل
  // ===================================================================

  /// تحميل البيانات الأولية عند فتح الشاشة
  Future<void> _loadInitialData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل البيانات من قاعدة البيانات
      await Future.delayed(const Duration(milliseconds: 500)); // محاكاة التحميل
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// معالج تغيير التبويبات
  void _onTabChanged() {
    if (!mounted) return;

    // تحديث البيانات حسب التبويب المحدد
    switch (_tabController.index) {
      case 0: // تبويب الإدخال
        break;
      case 1: // تبويب المراجعة
        _updateStatistics();
        break;
      case 2: // تبويب الإحصائيات
        _updateStatistics();
        break;
    }
  }

  /// اختيار امتحان لإدخال درجاته
  void _selectExam(ExamModel exam) {
    setState(() {
      _selectedExam = exam;
      _maxGrade = 100.0; // TODO: جلب من إعدادات الامتحان
    });

    // تحميل قائمة الطلاب للامتحان المحدد
    _loadStudentsForExam(exam);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم اختيار امتحان: ${exam.name}'),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// تحميل قائمة الطلاب للامتحان المحدد
  Future<void> _loadStudentsForExam(ExamModel exam) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // TODO: تحميل الطلاب من قاعدة البيانات
      // محاكاة قائمة طلاب
      _students = [
        StudentModel(
          id: '1',
          name: 'أحمد محمد علي',
          email: '<EMAIL>',
          studentNumber: 'STU001',
          studentClass: 'الصف الأول',
          classId: 'class1',
          createdAt: Timestamp.now(),
          dateOfBirth: DateTime(2010, 1, 1),
          address: 'الرياض',
          isActive: true,
        ),
        StudentModel(
          id: '2',
          name: 'فاطمة أحمد سالم',
          email: '<EMAIL>',
          studentNumber: 'STU002',
          studentClass: 'الصف الأول',
          classId: 'class1',
          createdAt: Timestamp.now(),
          dateOfBirth: DateTime(2010, 3, 15),
          address: 'جدة',
          isActive: true,
        ),
        // يمكن إضافة المزيد من الطلاب هنا
      ];

      // إنشاء متحكمات الإدخال لكل طالب
      _initializeControllers();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطلاب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// إنشاء متحكمات الإدخال لكل طالب
  void _initializeControllers() {
    // تنظيف المتحكمات السابقة
    for (final controller in _gradeControllers.values) {
      controller.dispose();
    }
    for (final controller in _notesControllers.values) {
      controller.dispose();
    }

    _gradeControllers.clear();
    _notesControllers.clear();
    _enteredGrades.clear();
    _studentNotes.clear();
    _savedStates.clear();

    // إنشاء متحكمات جديدة لكل طالب
    for (final student in _students) {
      _gradeControllers[student.id] = TextEditingController();
      _notesControllers[student.id] = TextEditingController();
      _savedStates[student.id] = false;
    }
  }

  /// الحصول على لون حالة إدخال الدرجات
  Color _getGradeStatusColor(ExamModel exam) {
    // TODO: تحديد الحالة الفعلية من قاعدة البيانات
    return Colors.orange; // افتراضي: في انتظار الإدخال
  }

  /// الحصول على نص حالة إدخال الدرجات
  String _getGradeStatusText(ExamModel exam) {
    // TODO: تحديد الحالة الفعلية من قاعدة البيانات
    return 'في الانتظار'; // افتراضي
  }

  /// عرض حوار البحث عن طالب
  void _showSearchDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('البحث عن طالب'),
            content: TextField(
              controller: _searchController,
              decoration: const InputDecoration(
                hintText: 'ابحث عن طالب...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _performSearch(_searchController.text);
                },
                child: const Text('بحث'),
              ),
            ],
          ),
    );
  }

  /// تنفيذ البحث عن طالب
  void _performSearch(String query) {
    // TODO: تطبيق منطق البحث
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('البحث عن: $query'), backgroundColor: Colors.blue),
    );
  }

  /// حفظ جميع الدرجات
  Future<void> _saveAllGrades() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // TODO: حفظ الدرجات في قاعدة البيانات
      await Future.delayed(const Duration(seconds: 2)); // محاكاة الحفظ

      setState(() {
        _savedGradesCount = _enteredGrades.length;
        _pendingGradesCount = 0;
        _lastSaved = DateTime.now();

        // تحديث حالة الحفظ لجميع الطلاب
        for (final studentId in _enteredGrades.keys) {
          _savedStates[studentId] = true;
        }
      });

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ جميع الدرجات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ الدرجات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  /// نشر النتائج
  Future<void> _publishResults() async {
    setState(() {
      _isPublishing = true;
    });

    try {
      // TODO: نشر النتائج وإرسال الإشعارات
      await Future.delayed(const Duration(seconds: 2)); // محاكاة النشر

      setState(() {
        _areResultsPublished = !_areResultsPublished;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            _areResultsPublished
                ? 'تم نشر النتائج بنجاح'
                : 'تم إلغاء نشر النتائج',
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في نشر النتائج: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() {
        _isPublishing = false;
      });
    }
  }

  /// تحديث الإحصائيات
  void _updateStatistics() {
    // TODO: حساب الإحصائيات
  }

  /// بناء قائمة الطلاب والدرجات
  Widget _buildStudentsGradesList() {
    return const Center(
      child: Text(
        'قائمة الطلاب\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب المراجعة
  Widget _buildReviewTab() {
    return const Center(
      child: Text(
        'مراجعة النتائج\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return const Center(
      child: Text(
        'الإحصائيات\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 18, color: Colors.grey),
      ),
    );
  }

  /// بناء شريط المعلومات السفلي
  Widget _buildBottomInfoBar() {
    if (_selectedExam == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(top: BorderSide(color: Colors.grey[300]!)),
      ),
      child: Row(
        children: [
          // معلومات الحفظ
          Icon(Icons.save, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 4),
          Text(
            'محفوظ: $_savedGradesCount',
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
          ),
          const SizedBox(width: 16),

          // معلومات المعلق
          Icon(Icons.pending, size: 16, color: Colors.orange[600]),
          const SizedBox(width: 4),
          Text(
            'معلق: $_pendingGradesCount',
            style: TextStyle(fontSize: 12, color: Colors.orange[600]),
          ),
          const Spacer(),

          // آخر حفظ
          if (_lastSaved != null) ...[
            Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
            const SizedBox(width: 4),
            Text(
              'آخر حفظ: ${_lastSaved!.hour}:${_lastSaved!.minute.toString().padLeft(2, '0')}',
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ],
      ),
    );
  }
}
