import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// تعداد لأولوية الواجبات المدرسية
/// يوفر طريقة منظمة للتعامل مع مستويات الأولوية المختلفة
enum AssignmentPriority {
  low('عادي'),
  medium('مهم'),
  high('عاجل'),
  critical('حرج');

  const AssignmentPriority(this.arabicName);
  final String arabicName;

  /// الحصول على لون الأولوية للعرض البصري
  Color get color {
    switch (this) {
      case AssignmentPriority.low:
        return const Color(0xFF38A169); // أخضر
      case AssignmentPriority.medium:
        return const Color(0xFFD69E2E); // برتقالي
      case AssignmentPriority.high:
        return const Color(0xFFE53E3E); // أحمر
      case AssignmentPriority.critical:
        return const Color(0xFF9F1239); // أحمر داكن
    }
  }

  /// الحصول على أيقونة الأولوية
  IconData get icon {
    switch (this) {
      case AssignmentPriority.low:
        return Icons.assignment;
      case AssignmentPriority.medium:
        return Icons.warning_amber;
      case AssignmentPriority.high:
        return Icons.priority_high;
      case AssignmentPriority.critical:
        return Icons.error;
    }
  }

  /// تحويل النص العربي إلى enum
  static AssignmentPriority fromArabic(String arabicName) {
    for (AssignmentPriority priority in AssignmentPriority.values) {
      if (priority.arabicName == arabicName) {
        return priority;
      }
    }
    return AssignmentPriority.low;
  }
}

/// تعداد لحالة الواجب
/// يوضح الحالة الحالية للواجب بالنسبة للطالب
enum AssignmentStatus {
  pending('مطلوب'),
  submitted('تم التسليم'),
  overdue('متأخر'),
  graded('تم التصحيح'),
  returned('مُعاد');

  const AssignmentStatus(this.arabicName);
  final String arabicName;

  /// الحصول على لون الحالة
  Color get color {
    switch (this) {
      case AssignmentStatus.pending:
        return const Color(0xFFD69E2E); // برتقالي
      case AssignmentStatus.submitted:
        return const Color(0xFF38A169); // أخضر
      case AssignmentStatus.overdue:
        return const Color(0xFFE53E3E); // أحمر
      case AssignmentStatus.graded:
        return const Color(0xFF3182CE); // أزرق
      case AssignmentStatus.returned:
        return const Color(0xFF805AD5); // بنفسجي
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get icon {
    switch (this) {
      case AssignmentStatus.pending:
        return Icons.pending_actions;
      case AssignmentStatus.submitted:
        return Icons.check_circle;
      case AssignmentStatus.overdue:
        return Icons.warning;
      case AssignmentStatus.graded:
        return Icons.grade;
      case AssignmentStatus.returned:
        return Icons.assignment_return;
    }
  }
}

/// تعداد لنوع المرفق
/// يساعد في تحديد كيفية عرض المرفقات المختلفة
enum AttachmentType {
  image('صورة'),
  pdf('ملف PDF'),
  document('مستند'),
  video('فيديو'),
  audio('صوت'),
  link('رابط'),
  other('أخرى');

  const AttachmentType(this.arabicName);
  final String arabicName;

  /// الحصول على أيقونة نوع المرفق
  IconData get icon {
    switch (this) {
      case AttachmentType.image:
        return Icons.image;
      case AttachmentType.pdf:
        return Icons.picture_as_pdf;
      case AttachmentType.document:
        return Icons.description;
      case AttachmentType.video:
        return Icons.video_file;
      case AttachmentType.audio:
        return Icons.audio_file;
      case AttachmentType.link:
        return Icons.link;
      case AttachmentType.other:
        return Icons.attach_file;
    }
  }

  /// تحديد نوع المرفق من امتداد الملف
  static AttachmentType fromExtension(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
      case 'webp':
        return AttachmentType.image;
      case 'pdf':
        return AttachmentType.pdf;
      case 'doc':
      case 'docx':
      case 'txt':
      case 'rtf':
        return AttachmentType.document;
      case 'mp4':
      case 'avi':
      case 'mov':
      case 'wmv':
        return AttachmentType.video;
      case 'mp3':
      case 'wav':
      case 'aac':
        return AttachmentType.audio;
      default:
        return AttachmentType.other;
    }
  }
}

/// نموذج بيانات للمرفق
/// يحتوي على معلومات تفصيلية عن كل مرفق
class AssignmentAttachment {
  final String name; // اسم الملف
  final String url; // رابط الملف
  final AttachmentType type; // نوع المرفق
  final int? size; // حجم الملف بالبايت
  final DateTime? uploadedAt; // تاريخ الرفع

  const AssignmentAttachment({
    required this.name,
    required this.url,
    required this.type,
    this.size,
    this.uploadedAt,
  });

  /// إنشاء مرفق من Map
  factory AssignmentAttachment.fromMap(Map<String, dynamic> data) {
    return AssignmentAttachment(
      name: data['name'] as String? ?? 'ملف غير معروف',
      url: data['url'] as String? ?? '',
      type: AttachmentType.fromExtension(data['name'] as String? ?? ''),
      size: data['size'] as int?,
      uploadedAt:
          data['uploadedAt'] != null
              ? (data['uploadedAt'] as Timestamp).toDate()
              : null,
    );
  }

  /// تحويل المرفق إلى Map
  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'url': url,
      'type': type.arabicName,
      'size': size,
      'uploadedAt': uploadedAt != null ? Timestamp.fromDate(uploadedAt!) : null,
    };
  }

  /// تنسيق حجم الملف للعرض
  String get formattedSize {
    if (size == null) return 'غير معروف';

    if (size! < 1024) {
      return '${size!} بايت';
    } else if (size! < 1024 * 1024) {
      return '${(size! / 1024).toStringAsFixed(1)} كيلوبايت';
    } else {
      return '${(size! / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
    }
  }
}

/// يمثل هذا الكائن واجبًا دراسيًا بجميع خصائصه المحسنة.
/// استخدام كائن موديل مثل هذا يجعل الكود أكثر أمانًا وقابلية للقراءة
/// بدلاً من التعامل مع كائنات Map<String, dynamic> بشكل مباشر.
class AssignmentModel {
  final String id; // معرف الواجب الفريد
  final String title; // عنوان الواجب
  final String subjectName; // اسم المادة
  final String description; // وصف تفصيلي للواجب
  final DateTime dueDate; // تاريخ التسليم المطلوب
  final Timestamp createdAt; // تاريخ إنشاء الواجب

  // خصائص محسنة لتحسين عرض الواجبات
  final List<AssignmentAttachment> attachments; // قائمة المرفقات المحسنة
  final String? classId; // معرف الفصل المخصص له الواجب
  final String? teacherId; // معرف المعلم الذي أنشأ الواجب
  final String? teacherName; // اسم المعلم الذي أنشأ الواجب
  final int maxGrade; // الدرجة العظمى للواجب
  final AssignmentPriority priority; // أولوية الواجب المحسنة
  final String? instructions; // تعليمات إضافية للطلاب
  final bool allowLateSubmission; // السماح بالتسليم المتأخر
  final DateTime? lateSubmissionDeadline; // الموعد النهائي للتسليم المتأخر
  final double? latePenalty; // نسبة خصم التأخير (0.0 - 1.0)
  final List<String> tags; // علامات تصنيفية للواجب
  final Map<String, dynamic>? rubric; // معايير التقييم
  final bool isActive; // هل الواجب نشط أم لا

  const AssignmentModel({
    required this.id,
    required this.title,
    required this.subjectName,
    required this.description,
    required this.dueDate,
    required this.createdAt,
    this.attachments = const [],
    this.classId,
    this.teacherId,
    this.teacherName,
    this.maxGrade = 100,
    this.priority = AssignmentPriority.low,
    this.instructions,
    this.allowLateSubmission = false,
    this.lateSubmissionDeadline,
    this.latePenalty,
    this.tags = const [],
    this.rubric,
    this.isActive = true,
  });

  /// دالة مصنعية (Factory Constructor) لإنشاء كائن AssignmentModel
  /// من مستند Firestore (DocumentSnapshot).
  /// تقوم هذه الدالة بمعالجة البيانات القادمة من قاعدة البيانات بشكل آمن.
  factory AssignmentModel.fromFirestore(DocumentSnapshot doc) {
    // تحويل البيانات إلى Map
    final data = doc.data() as Map<String, dynamic>? ?? {};

    // معالجة المرفقات المحسنة
    List<AssignmentAttachment> attachments = [];
    if (data['attachments'] is List) {
      attachments =
          (data['attachments'] as List<dynamic>)
              .map(
                (item) =>
                    AssignmentAttachment.fromMap(item as Map<String, dynamic>),
              )
              .toList();
    } else {
      // التوافق مع البيانات القديمة
      final urls =
          (data['attachmentUrls'] as List<dynamic>?)?.cast<String>() ?? [];
      final names =
          (data['attachmentNames'] as List<dynamic>?)?.cast<String>() ?? [];

      for (int i = 0; i < urls.length; i++) {
        final name = i < names.length ? names[i] : 'ملف ${i + 1}';
        attachments.add(
          AssignmentAttachment(
            name: name,
            url: urls[i],
            type: AttachmentType.fromExtension(name),
          ),
        );
      }
    }

    return AssignmentModel(
      id: doc.id,
      title: data['title'] as String? ?? 'بلا عنوان',
      subjectName: data['subjectName'] as String? ?? 'مادة غير محددة',
      description: data['description'] as String? ?? '',
      // التعامل مع التواريخ بشكل آمن
      dueDate: (data['dueDate'] as Timestamp? ?? Timestamp.now()).toDate(),
      createdAt: data['createdAt'] as Timestamp? ?? Timestamp.now(),
      // معالجة الخصائص المحسنة بشكل آمن
      attachments: attachments,
      classId: data['classId'] as String?,
      teacherId: data['teacherId'] as String?,
      teacherName: data['teacherName'] as String?,
      maxGrade: data['maxGrade'] as int? ?? 100,
      priority: AssignmentPriority.fromArabic(
        data['priority'] as String? ?? 'عادي',
      ),
      instructions: data['instructions'] as String?,
      allowLateSubmission: data['allowLateSubmission'] as bool? ?? false,
      lateSubmissionDeadline:
          data['lateSubmissionDeadline'] != null
              ? (data['lateSubmissionDeadline'] as Timestamp).toDate()
              : null,
      latePenalty: (data['latePenalty'] as num?)?.toDouble(),
      tags: (data['tags'] as List<dynamic>?)?.cast<String>() ?? [],
      rubric: data['rubric'] as Map<String, dynamic>?,
      isActive: data['isActive'] as bool? ?? true,
    );
  }

  /// دالة لتحويل كائن الموديل إلى Map لإرساله إلى Firestore.
  /// مفيدة عند إنشاء أو تحديث الواجبات.
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'subjectName': subjectName,
      'description': description,
      'dueDate': Timestamp.fromDate(dueDate),
      'createdAt': createdAt,
      'attachments':
          attachments.map((attachment) => attachment.toMap()).toList(),
      'classId': classId,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'maxGrade': maxGrade,
      'priority': priority.arabicName,
      'instructions': instructions,
      'allowLateSubmission': allowLateSubmission,
      'lateSubmissionDeadline':
          lateSubmissionDeadline != null
              ? Timestamp.fromDate(lateSubmissionDeadline!)
              : null,
      'latePenalty': latePenalty,
      'tags': tags,
      'rubric': rubric,
      'isActive': isActive,
    };
  }

  /// إنشاء نسخة محدثة من الواجب
  AssignmentModel copyWith({
    String? id,
    String? title,
    String? subjectName,
    String? description,
    DateTime? dueDate,
    Timestamp? createdAt,
    List<AssignmentAttachment>? attachments,
    String? classId,
    String? teacherId,
    String? teacherName,
    int? maxGrade,
    AssignmentPriority? priority,
    String? instructions,
    bool? allowLateSubmission,
    DateTime? lateSubmissionDeadline,
    double? latePenalty,
    List<String>? tags,
    Map<String, dynamic>? rubric,
    bool? isActive,
  }) {
    return AssignmentModel(
      id: id ?? this.id,
      title: title ?? this.title,
      subjectName: subjectName ?? this.subjectName,
      description: description ?? this.description,
      dueDate: dueDate ?? this.dueDate,
      createdAt: createdAt ?? this.createdAt,
      attachments: attachments ?? this.attachments,
      classId: classId ?? this.classId,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      maxGrade: maxGrade ?? this.maxGrade,
      priority: priority ?? this.priority,
      instructions: instructions ?? this.instructions,
      allowLateSubmission: allowLateSubmission ?? this.allowLateSubmission,
      lateSubmissionDeadline:
          lateSubmissionDeadline ?? this.lateSubmissionDeadline,
      latePenalty: latePenalty ?? this.latePenalty,
      tags: tags ?? this.tags,
      rubric: rubric ?? this.rubric,
      isActive: isActive ?? this.isActive,
    );
  }

  /// دوال مساعدة للوصول السريع للخصائص المحسنة

  /// الحصول على لون الأولوية
  Color get priorityColor => priority.color;

  /// الحصول على أيقونة الأولوية
  IconData get priorityIcon => priority.icon;

  /// التحقق من انتهاء موعد التسليم
  bool get isOverdue => DateTime.now().isAfter(dueDate);

  /// التحقق من إمكانية التسليم المتأخر
  bool get canSubmitLate {
    if (!allowLateSubmission) return false;
    if (lateSubmissionDeadline == null) return true;
    return DateTime.now().isBefore(lateSubmissionDeadline!);
  }

  /// حساب الوقت المتبقي للتسليم
  Duration get timeRemaining {
    final now = DateTime.now();
    if (now.isBefore(dueDate)) {
      return dueDate.difference(now);
    } else if (canSubmitLate && lateSubmissionDeadline != null) {
      return lateSubmissionDeadline!.difference(now);
    }
    return Duration.zero;
  }

  /// تحديد حالة الواجب بناءً على التاريخ الحالي
  AssignmentStatus get currentStatus {
    final now = DateTime.now();

    if (now.isBefore(dueDate)) {
      return AssignmentStatus.pending;
    } else if (canSubmitLate) {
      return AssignmentStatus.overdue;
    } else {
      return AssignmentStatus.overdue;
    }
  }

  /// الحصول على نص وصفي للوقت المتبقي
  String get timeRemainingText {
    final remaining = timeRemaining;

    if (remaining == Duration.zero) {
      return 'انتهى الموعد';
    }

    if (remaining.inDays > 0) {
      return 'متبقي ${remaining.inDays} يوم';
    } else if (remaining.inHours > 0) {
      return 'متبقي ${remaining.inHours} ساعة';
    } else if (remaining.inMinutes > 0) {
      return 'متبقي ${remaining.inMinutes} دقيقة';
    } else {
      return 'أقل من دقيقة';
    }
  }

  /// التحقق من وجود مرفقات
  bool get hasAttachments => attachments.isNotEmpty;

  /// عدد المرفقات
  int get attachmentCount => attachments.length;

  /// الحصول على المرفقات حسب النوع
  List<AssignmentAttachment> getAttachmentsByType(AttachmentType type) {
    return attachments.where((attachment) => attachment.type == type).toList();
  }

  /// التحقق من وجود تعليمات إضافية
  bool get hasInstructions => instructions != null && instructions!.isNotEmpty;

  /// التحقق من وجود معايير تقييم
  bool get hasRubric => rubric != null && rubric!.isNotEmpty;

  /// حساب نسبة خصم التأخير
  double get lateSubmissionPenalty {
    if (!isOverdue || latePenalty == null) return 0.0;
    return latePenalty!;
  }

  /// تحديد مستوى الإلحاح بناءً على الوقت المتبقي والأولوية
  int get urgencyLevel {
    final remaining = timeRemaining;
    int baseUrgency = priority.index;

    if (remaining.inDays <= 1) {
      baseUrgency += 2;
    } else if (remaining.inDays <= 3) {
      baseUrgency += 1;
    }

    return baseUrgency.clamp(0, 5);
  }

  /// الحصول على لون الإلحاح
  Color get urgencyColor {
    switch (urgencyLevel) {
      case 0:
      case 1:
        return Colors.green;
      case 2:
        return Colors.yellow.shade700;
      case 3:
        return Colors.orange;
      case 4:
        return Colors.red;
      case 5:
      default:
        return Colors.red.shade800;
    }
  }

  @override
  String toString() {
    return 'AssignmentModel(id: $id, title: $title, subject: $subjectName, dueDate: $dueDate)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AssignmentModel &&
        other.id == id &&
        other.title == title &&
        other.dueDate == dueDate;
  }

  @override
  int get hashCode {
    return id.hashCode ^ title.hashCode ^ dueDate.hashCode;
  }
}

/// نموذج بيانات لإحصائيات الواجبات
/// يوفر تحليلات مفيدة عن واجبات الطالب
class AssignmentStats {
  final List<AssignmentModel> assignments;

  const AssignmentStats({required this.assignments});

  /// إجمالي عدد الواجبات
  int get totalAssignments => assignments.length;

  /// عدد الواجبات المطلوبة
  int get pendingAssignments => assignments.where((a) => !a.isOverdue).length;

  /// عدد الواجبات المتأخرة
  int get overdueAssignments => assignments.where((a) => a.isOverdue).length;

  /// عدد الواجبات العاجلة (أقل من يوم)
  int get urgentAssignments =>
      assignments
          .where((a) => !a.isOverdue && a.timeRemaining.inDays < 1)
          .length;

  /// عدد الواجبات حسب المادة
  Map<String, int> get assignmentsBySubject {
    final Map<String, int> result = {};
    for (final assignment in assignments) {
      result[assignment.subjectName] =
          (result[assignment.subjectName] ?? 0) + 1;
    }
    return result;
  }

  /// عدد الواجبات حسب الأولوية
  Map<AssignmentPriority, int> get assignmentsByPriority {
    final Map<AssignmentPriority, int> result = {};
    for (final assignment in assignments) {
      result[assignment.priority] = (result[assignment.priority] ?? 0) + 1;
    }
    return result;
  }

  /// متوسط الوقت المتبقي للواجبات المطلوبة
  Duration get averageTimeRemaining {
    final pending = assignments.where((a) => !a.isOverdue).toList();
    if (pending.isEmpty) return Duration.zero;

    final totalMinutes = pending
        .map((a) => a.timeRemaining.inMinutes)
        .reduce((a, b) => a + b);

    return Duration(minutes: (totalMinutes / pending.length).round());
  }

  /// الواجب الأكثر إلحاحاً
  AssignmentModel? get mostUrgentAssignment {
    if (assignments.isEmpty) return null;

    final pending = assignments.where((a) => !a.isOverdue).toList();
    if (pending.isEmpty) return null;

    return pending.reduce((a, b) => a.urgencyLevel > b.urgencyLevel ? a : b);
  }

  /// المادة الأكثر واجبات
  String? get subjectWithMostAssignments {
    final subjects = assignmentsBySubject;
    if (subjects.isEmpty) return null;

    return subjects.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }
}
