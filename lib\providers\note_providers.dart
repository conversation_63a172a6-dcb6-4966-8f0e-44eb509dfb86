import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/note_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب قائمة الملاحظات الخاصة بطالب معين.
final notesStreamProvider =
    StreamProvider.autoDispose.family<List<NoteModel>, String>((ref, studentId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getNotesStream(studentId: studentId);
});

/// Provider لإدارة حالة إرسال الملاحظات.
final noteControllerProvider =
    StateNotifierProvider.autoDispose<NoteController, bool>((ref) {
  return NoteController(ref);
});

class NoteController extends StateNotifier<bool> {
  NoteController(this._ref) : super(false);

  final Ref _ref;

  Future<void> sendNote(String content) async {
    state = true; // جاري الإرسال
    try {
      await _ref.read(firebaseServiceProvider).sendNote(content);
    } catch (e) {
      // يمكنك هنا التعامل مع الخطأ، مثلاً عبر provider آخر لعرض الأخطاء
      rethrow;
    } finally {
      state = false; // اكتمل الإرسال
    }
  }
}
