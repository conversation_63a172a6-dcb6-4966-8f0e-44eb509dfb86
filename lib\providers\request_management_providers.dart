import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/request_management_model.dart';
import 'package:school_management_system/services/request_management_service.dart';

/// مزودات إدارة الطلبات
///
/// تحتوي على جميع مزودات الحالة المطلوبة لإدارة طلبات أولياء الأمور والطلاب
/// في النظام المدرسي مع دعم كامل للتفاعل مع الخدمات والواجهات
///
/// الميزات المتقدمة:
/// - إدارة حالة الطلبات في الوقت الفعلي
/// - نظام فلترة وبحث متقدم
/// - إدارة نماذج إنشاء وتعديل الطلبات
/// - تتبع حالة التحميل والأخطاء
/// - إدارة المرفقات والوثائق
/// - نظام الموافقات والتعليقات
/// - إحصائيات وتقارير شاملة
/// - إشعارات وتذكيرات تلقائية

// ===================================================================
// مزودات الخدمات الأساسية
// ===================================================================

/// مزود خدمة إدارة الطلبات
///
/// يوفر مثيل واحد من خدمة إدارة الطلبات لجميع أجزاء التطبيق
/// مع ضمان الاستخدام الأمثل للموارد وتجنب إنشاء مثيلات متعددة
final requestManagementServiceProvider = Provider<RequestManagementService>((
  ref,
) {
  return RequestManagementService();
});

// ===================================================================
// مزودات البيانات الأساسية
// ===================================================================

/// مزود طلبات المستخدم
///
/// يوفر قائمة طلبات المستخدم في الوقت الفعلي مع إمكانية الفلترة
/// ويتم تحديث البيانات تلقائياً عند حدوث أي تغييرات في قاعدة البيانات
///
/// [userId] معرف المستخدم المطلوب جلب طلباته
final userRequestsProvider =
    StreamProvider.family<List<RequestManagementModel>, String>((ref, userId) {
      final service = ref.watch(requestManagementServiceProvider);
      return service.getUserRequests(userId);
    });

/// مزود طلبات المستخدم مع فلاتر
///
/// يوفر قائمة طلبات المستخدم مع إمكانية تطبيق فلاتر متقدمة
/// مثل التاريخ، الحالة، النوع، والفئة
///
/// [params] معاملات الفلترة والبحث
final filteredUserRequestsProvider =
    StreamProvider.family<List<RequestManagementModel>, UserRequestsParams>((
      ref,
      params,
    ) {
      final service = ref.watch(requestManagementServiceProvider);
      return service.getUserRequests(
        params.userId,
        startDate: params.startDate,
        endDate: params.endDate,
        status: params.status,
        requestType: params.requestType,
        category: params.category,
        limit: params.limit,
      );
    });

/// مزود تفاصيل طلب محدد
///
/// يوفر تفاصيل طلب محدد بناءً على معرف الطلب
/// مع تحديث تلقائي عند حدوث تغييرات
///
/// [requestId] معرف الطلب المطلوب جلب تفاصيله
final requestDetailsProvider =
    FutureProvider.family<RequestManagementModel?, String>((ref, requestId) {
      final service = ref.watch(requestManagementServiceProvider);
      return service.getRequest(requestId);
    });

/// مزود الطلبات المعلقة للموافقة
///
/// يوفر قائمة الطلبات التي تحتاج موافقة من مسؤول محدد
/// مع تحديث فوري عند إضافة طلبات جديدة أو تغيير حالة الطلبات الموجودة
///
/// [approverId] معرف المسؤول عن الموافقة
final pendingApprovalRequestsProvider =
    StreamProvider.family<List<RequestManagementModel>, String>((
      ref,
      approverId,
    ) {
      final service = ref.watch(requestManagementServiceProvider);
      return service.getPendingApprovalRequests(approverId);
    });

/// مزود إحصائيات الطلبات
///
/// يوفر إحصائيات شاملة للطلبات مع إمكانية تحديد فترة زمنية محددة
/// يشمل عدد الطلبات، معدل الإنجاز، الوقت المتوسط، وتوزيع الطلبات حسب النوع والحالة
///
/// [params] معاملات الإحصائيات (التاريخ من والى)
final requestStatisticsProvider =
    FutureProvider.family<Map<String, dynamic>, StatisticsParams>((
      ref,
      params,
    ) {
      final service = ref.watch(requestManagementServiceProvider);
      return service.getRequestStatistics(
        startDate: params.startDate,
        endDate: params.endDate,
      );
    });

// ===================================================================
// مزودات إدارة النماذج
// ===================================================================

/// مزود حالة نموذج إنشاء طلب جديد
///
/// يدير حالة نموذج إنشاء طلب جديد مع جميع الحقول والتحقق من صحة البيانات
/// ويوفر دوال لتحديث الحقول وإرسال الطلب وإدارة المرفقات
final createRequestFormProvider =
    StateNotifierProvider<CreateRequestFormNotifier, CreateRequestFormState>(
      (ref) => CreateRequestFormNotifier(ref),
    );

/// مزود حالة فلاتر البحث
///
/// يدير حالة فلاتر البحث والفلترة للطلبات
/// مع إمكانية حفظ واستعادة إعدادات الفلترة المفضلة للمستخدم
final requestFiltersProvider =
    StateNotifierProvider<RequestFiltersNotifier, RequestFiltersState>(
      (ref) => RequestFiltersNotifier(),
    );

/// مزود حالة الموافقات
///
/// يدير حالة عمليات الموافقة والرفض للطلبات
/// مع تتبع حالة التحميل وإدارة الأخطاء
final approvalProcessProvider =
    StateNotifierProvider<ApprovalProcessNotifier, ApprovalProcessState>(
      (ref) => ApprovalProcessNotifier(ref),
    );

// ===================================================================
// فئات المعاملات المساعدة
// ===================================================================

/// معاملات طلبات المستخدم مع الفلاتر
///
/// تحتوي على جميع المعاملات المطلوبة لفلترة وجلب طلبات المستخدم
/// مع دعم للبحث المتقدم والترتيب والتصفح
class UserRequestsParams {
  /// معرف المستخدم
  final String userId;

  /// تاريخ البداية للفلترة
  final DateTime? startDate;

  /// تاريخ النهاية للفلترة
  final DateTime? endDate;

  /// حالة الطلب للفلترة
  final RequestStatus? status;

  /// نوع الطلب للفلترة
  final RequestType? requestType;

  /// فئة الطلب للفلترة
  final RequestCategory? category;

  /// عدد الطلبات المطلوب إرجاعها
  final int limit;

  const UserRequestsParams({
    required this.userId,
    this.startDate,
    this.endDate,
    this.status,
    this.requestType,
    this.category,
    this.limit = 50,
  });

  /// إنشاء نسخة محدثة من المعاملات
  UserRequestsParams copyWith({
    String? userId,
    DateTime? startDate,
    DateTime? endDate,
    RequestStatus? status,
    RequestType? requestType,
    RequestCategory? category,
    int? limit,
  }) {
    return UserRequestsParams(
      userId: userId ?? this.userId,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      requestType: requestType ?? this.requestType,
      category: category ?? this.category,
      limit: limit ?? this.limit,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserRequestsParams &&
        other.userId == userId &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.status == status &&
        other.requestType == requestType &&
        other.category == category &&
        other.limit == limit;
  }

  @override
  int get hashCode {
    return userId.hashCode ^
        startDate.hashCode ^
        endDate.hashCode ^
        status.hashCode ^
        requestType.hashCode ^
        category.hashCode ^
        limit.hashCode;
  }
}

/// معاملات الإحصائيات
///
/// تحتوي على معاملات حساب إحصائيات الطلبات
/// مع إمكانية تحديد فترة زمنية محددة للتحليل
class StatisticsParams {
  /// تاريخ البداية للإحصائيات
  final DateTime? startDate;

  /// تاريخ النهاية للإحصائيات
  final DateTime? endDate;

  const StatisticsParams({this.startDate, this.endDate});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StatisticsParams &&
        other.startDate == startDate &&
        other.endDate == endDate;
  }

  @override
  int get hashCode {
    return startDate.hashCode ^ endDate.hashCode;
  }
}

// ===================================================================
// حالات النماذج والعمليات
// ===================================================================

/// حالة نموذج إنشاء طلب جديد
///
/// تحتوي على جميع البيانات والحالات المطلوبة لإنشاء طلب جديد
/// مع تتبع حالة التحميل والأخطاء والتحقق من صحة البيانات
class CreateRequestFormState {
  /// معرف مقدم الطلب
  final String requesterId;

  /// اسم مقدم الطلب
  final String requesterName;

  /// نوع مقدم الطلب
  final RequesterType requesterType;

  /// معرف الطالب المرتبط بالطلب
  final String? studentId;

  /// اسم الطالب المرتبط بالطلب
  final String? studentName;

  /// معرف الصف الدراسي
  final String? classId;

  /// اسم الصف الدراسي
  final String? className;

  /// نوع الطلب
  final RequestType requestType;

  /// فئة الطلب
  final RequestCategory category;

  /// أولوية الطلب
  final RequestPriority priority;

  /// عنوان الطلب
  final String title;

  /// وصف الطلب
  final String description;

  /// سبب الطلب
  final String reason;

  /// تفاصيل إضافية
  final String? additionalDetails;

  /// التاريخ المطلوب للإنجاز
  final DateTime? requestedCompletionDate;

  /// الوثائق المطلوبة
  final List<RequiredDocument> requiredDocuments;

  /// المرفقات المرفوعة
  final List<RequestAttachment> attachments;

  /// هل الطلب عاجل؟
  final bool isUrgent;

  /// هل يحتاج متابعة خاصة؟
  final bool requiresSpecialAttention;

  /// العلامات والتصنيفات
  final List<String> tags;

  /// مستوى الخصوصية
  final PrivacyLevel privacyLevel;

  /// معلومات إضافية
  final Map<String, dynamic> metadata;

  /// هل النموذج صحيح؟
  final bool isValid;

  /// هل يتم الإرسال حالياً؟
  final bool isSubmitting;

  /// رسالة الخطأ
  final String? errorMessage;

  const CreateRequestFormState({
    this.requesterId = '',
    this.requesterName = '',
    this.requesterType = RequesterType.parent,
    this.studentId,
    this.studentName,
    this.classId,
    this.className,
    this.requestType = RequestType.certificate,
    this.category = RequestCategory.academic,
    this.priority = RequestPriority.normal,
    this.title = '',
    this.description = '',
    this.reason = '',
    this.additionalDetails,
    this.requestedCompletionDate,
    this.requiredDocuments = const [],
    this.attachments = const [],
    this.isUrgent = false,
    this.requiresSpecialAttention = false,
    this.tags = const [],
    this.privacyLevel = PrivacyLevel.normal,
    this.metadata = const {},
    this.isValid = false,
    this.isSubmitting = false,
    this.errorMessage,
  });

  /// إنشاء نسخة محدثة من الحالة
  CreateRequestFormState copyWith({
    String? requesterId,
    String? requesterName,
    RequesterType? requesterType,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    RequestType? requestType,
    RequestCategory? category,
    RequestPriority? priority,
    String? title,
    String? description,
    String? reason,
    String? additionalDetails,
    DateTime? requestedCompletionDate,
    List<RequiredDocument>? requiredDocuments,
    List<RequestAttachment>? attachments,
    bool? isUrgent,
    bool? requiresSpecialAttention,
    List<String>? tags,
    PrivacyLevel? privacyLevel,
    Map<String, dynamic>? metadata,
    bool? isValid,
    bool? isSubmitting,
    String? errorMessage,
  }) {
    return CreateRequestFormState(
      requesterId: requesterId ?? this.requesterId,
      requesterName: requesterName ?? this.requesterName,
      requesterType: requesterType ?? this.requesterType,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      classId: classId ?? this.classId,
      className: className ?? this.className,
      requestType: requestType ?? this.requestType,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      title: title ?? this.title,
      description: description ?? this.description,
      reason: reason ?? this.reason,
      additionalDetails: additionalDetails ?? this.additionalDetails,
      requestedCompletionDate:
          requestedCompletionDate ?? this.requestedCompletionDate,
      requiredDocuments: requiredDocuments ?? this.requiredDocuments,
      attachments: attachments ?? this.attachments,
      isUrgent: isUrgent ?? this.isUrgent,
      requiresSpecialAttention:
          requiresSpecialAttention ?? this.requiresSpecialAttention,
      tags: tags ?? this.tags,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      metadata: metadata ?? this.metadata,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      errorMessage: errorMessage,
    );
  }

  /// التحقق من صحة النموذج
  bool get _isFormValid {
    return requesterId.isNotEmpty &&
        requesterName.isNotEmpty &&
        title.trim().isNotEmpty &&
        description.trim().isNotEmpty &&
        reason.trim().isNotEmpty;
  }
}

/// مدير حالة نموذج إنشاء الطلب
///
/// يدير جميع العمليات المتعلقة بإنشاء طلب جديد
/// مع التحقق من صحة البيانات وإدارة المرفقات والإرسال
class CreateRequestFormNotifier extends StateNotifier<CreateRequestFormState> {
  /// مرجع للمزودات الأخرى
  final Ref _ref;

  CreateRequestFormNotifier(this._ref) : super(const CreateRequestFormState());

  /// تحديث معرف مقدم الطلب
  void updateRequesterId(String requesterId) {
    state = state.copyWith(
      requesterId: requesterId,
      isValid: state._isFormValid,
    );
  }

  /// تحديث اسم مقدم الطلب
  void updateRequesterName(String requesterName) {
    state = state.copyWith(
      requesterName: requesterName,
      isValid: state._isFormValid,
    );
  }

  /// تحديث نوع مقدم الطلب
  void updateRequesterType(RequesterType requesterType) {
    state = state.copyWith(
      requesterType: requesterType,
      isValid: state._isFormValid,
    );
  }

  /// تحديث معلومات الطالب
  void updateStudentInfo({
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
  }) {
    state = state.copyWith(
      studentId: studentId,
      studentName: studentName,
      classId: classId,
      className: className,
      isValid: state._isFormValid,
    );
  }

  /// تحديث نوع الطلب
  void updateRequestType(RequestType requestType) {
    state = state.copyWith(
      requestType: requestType,
      isValid: state._isFormValid,
    );
  }

  /// تحديث فئة الطلب
  void updateCategory(RequestCategory category) {
    state = state.copyWith(category: category, isValid: state._isFormValid);
  }

  /// تحديث أولوية الطلب
  void updatePriority(RequestPriority priority) {
    state = state.copyWith(priority: priority, isValid: state._isFormValid);
  }

  /// تحديث عنوان الطلب
  void updateTitle(String title) {
    state = state.copyWith(title: title, isValid: state._isFormValid);
  }

  /// تحديث وصف الطلب
  void updateDescription(String description) {
    state = state.copyWith(
      description: description,
      isValid: state._isFormValid,
    );
  }

  /// تحديث سبب الطلب
  void updateReason(String reason) {
    state = state.copyWith(reason: reason, isValid: state._isFormValid);
  }

  /// تحديث التفاصيل الإضافية
  void updateAdditionalDetails(String? additionalDetails) {
    state = state.copyWith(
      additionalDetails: additionalDetails,
      isValid: state._isFormValid,
    );
  }

  /// تحديث التاريخ المطلوب للإنجاز
  void updateRequestedCompletionDate(DateTime? date) {
    state = state.copyWith(
      requestedCompletionDate: date,
      isValid: state._isFormValid,
    );
  }

  /// تحديث الوثائق المطلوبة
  void updateRequiredDocuments(List<RequiredDocument> documents) {
    state = state.copyWith(
      requiredDocuments: documents,
      isValid: state._isFormValid,
    );
  }

  /// إضافة مرفق
  void addAttachment(RequestAttachment attachment) {
    final updatedAttachments = List<RequestAttachment>.from(state.attachments)
      ..add(attachment);

    state = state.copyWith(
      attachments: updatedAttachments,
      isValid: state._isFormValid,
    );
  }

  /// حذف مرفق
  void removeAttachment(String attachmentId) {
    final updatedAttachments =
        state.attachments
            .where((attachment) => attachment.id != attachmentId)
            .toList();

    state = state.copyWith(
      attachments: updatedAttachments,
      isValid: state._isFormValid,
    );
  }

  /// تحديث إعدادات الطلب
  void updateSettings({
    bool? isUrgent,
    bool? requiresSpecialAttention,
    List<String>? tags,
    PrivacyLevel? privacyLevel,
    Map<String, dynamic>? metadata,
  }) {
    state = state.copyWith(
      isUrgent: isUrgent ?? state.isUrgent,
      requiresSpecialAttention:
          requiresSpecialAttention ?? state.requiresSpecialAttention,
      tags: tags ?? state.tags,
      privacyLevel: privacyLevel ?? state.privacyLevel,
      metadata: metadata ?? state.metadata,
      isValid: state._isFormValid,
    );
  }

  /// إرسال الطلب
  Future<String?> submitRequest() async {
    if (!state.isValid) {
      state = state.copyWith(
        errorMessage: 'يرجى التأكد من صحة جميع البيانات المطلوبة',
      );
      return null;
    }

    state = state.copyWith(isSubmitting: true, errorMessage: null);

    try {
      final service = _ref.read(requestManagementServiceProvider);

      final requestId = await service.createRequest(
        requesterId: state.requesterId,
        requesterName: state.requesterName,
        requesterType: state.requesterType,
        studentId: state.studentId,
        studentName: state.studentName,
        classId: state.classId,
        className: state.className,
        requestType: state.requestType,
        category: state.category,
        priority: state.priority,
        title: state.title,
        description: state.description,
        reason: state.reason,
        additionalDetails: state.additionalDetails,
        requestedCompletionDate: state.requestedCompletionDate,
        requiredDocuments: state.requiredDocuments,
        isUrgent: state.isUrgent,
        requiresSpecialAttention: state.requiresSpecialAttention,
        tags: state.tags,
        privacyLevel: state.privacyLevel,
        metadata: state.metadata,
      );

      // إعادة تعيين النموذج بعد الإرسال الناجح
      state = const CreateRequestFormState();

      return requestId;
    } catch (e) {
      state = state.copyWith(
        isSubmitting: false,
        errorMessage: 'فشل في إرسال الطلب: ${e.toString()}',
      );
      return null;
    }
  }

  /// إعادة تعيين النموذج
  void resetForm() {
    state = const CreateRequestFormState();
  }

  /// تحديث رسالة الخطأ
  void updateErrorMessage(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }
}

/// حالة فلاتر البحث
///
/// تحتوي على جميع إعدادات الفلترة والبحث للطلبات
/// مع إمكانية حفظ واستعادة الإعدادات المفضلة
class RequestFiltersState {
  /// نص البحث
  final String searchText;

  /// تاريخ البداية
  final DateTime? startDate;

  /// تاريخ النهاية
  final DateTime? endDate;

  /// حالة الطلب
  final RequestStatus? status;

  /// نوع الطلب
  final RequestType? requestType;

  /// فئة الطلب
  final RequestCategory? category;

  /// أولوية الطلب
  final RequestPriority? priority;

  /// هل عرض الطلبات العاجلة فقط؟
  final bool urgentOnly;

  /// ترتيب النتائج
  final SortOrder sortOrder;

  /// حقل الترتيب
  final SortField sortField;

  const RequestFiltersState({
    this.searchText = '',
    this.startDate,
    this.endDate,
    this.status,
    this.requestType,
    this.category,
    this.priority,
    this.urgentOnly = false,
    this.sortOrder = SortOrder.descending,
    this.sortField = SortField.submittedAt,
  });

  /// إنشاء نسخة محدثة من الحالة
  RequestFiltersState copyWith({
    String? searchText,
    DateTime? startDate,
    DateTime? endDate,
    RequestStatus? status,
    RequestType? requestType,
    RequestCategory? category,
    RequestPriority? priority,
    bool? urgentOnly,
    SortOrder? sortOrder,
    SortField? sortField,
  }) {
    return RequestFiltersState(
      searchText: searchText ?? this.searchText,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      status: status ?? this.status,
      requestType: requestType ?? this.requestType,
      category: category ?? this.category,
      priority: priority ?? this.priority,
      urgentOnly: urgentOnly ?? this.urgentOnly,
      sortOrder: sortOrder ?? this.sortOrder,
      sortField: sortField ?? this.sortField,
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return searchText.isNotEmpty ||
        startDate != null ||
        endDate != null ||
        status != null ||
        requestType != null ||
        category != null ||
        priority != null ||
        urgentOnly;
  }

  /// إعادة تعيين جميع الفلاتر
  RequestFiltersState clearAll() {
    return const RequestFiltersState();
  }
}

/// مدير حالة فلاتر البحث
///
/// يدير جميع عمليات الفلترة والبحث للطلبات
/// مع إمكانية حفظ واستعادة الإعدادات المفضلة
class RequestFiltersNotifier extends StateNotifier<RequestFiltersState> {
  RequestFiltersNotifier() : super(const RequestFiltersState());

  /// تحديث نص البحث
  void updateSearchText(String searchText) {
    state = state.copyWith(searchText: searchText);
  }

  /// تحديث تاريخ البداية
  void updateStartDate(DateTime? startDate) {
    state = state.copyWith(startDate: startDate);
  }

  /// تحديث تاريخ النهاية
  void updateEndDate(DateTime? endDate) {
    state = state.copyWith(endDate: endDate);
  }

  /// تحديث حالة الطلب
  void updateStatus(RequestStatus? status) {
    state = state.copyWith(status: status);
  }

  /// تحديث نوع الطلب
  void updateRequestType(RequestType? requestType) {
    state = state.copyWith(requestType: requestType);
  }

  /// تحديث فئة الطلب
  void updateCategory(RequestCategory? category) {
    state = state.copyWith(category: category);
  }

  /// تحديث أولوية الطلب
  void updatePriority(RequestPriority? priority) {
    state = state.copyWith(priority: priority);
  }

  /// تحديث فلتر الطلبات العاجلة
  void updateUrgentOnly(bool urgentOnly) {
    state = state.copyWith(urgentOnly: urgentOnly);
  }

  /// تحديث ترتيب النتائج
  void updateSortOrder(SortOrder sortOrder) {
    state = state.copyWith(sortOrder: sortOrder);
  }

  /// تحديث حقل الترتيب
  void updateSortField(SortField sortField) {
    state = state.copyWith(sortField: sortField);
  }

  /// إعادة تعيين جميع الفلاتر
  void clearAllFilters() {
    state = const RequestFiltersState();
  }

  /// تطبيق فلاتر سريعة
  void applyQuickFilter(QuickFilterType filterType) {
    switch (filterType) {
      case QuickFilterType.today:
        final today = DateTime.now();
        state = state.copyWith(
          startDate: DateTime(today.year, today.month, today.day),
          endDate: DateTime(today.year, today.month, today.day, 23, 59, 59),
        );
        break;
      case QuickFilterType.thisWeek:
        final now = DateTime.now();
        final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
        state = state.copyWith(
          startDate: DateTime(
            startOfWeek.year,
            startOfWeek.month,
            startOfWeek.day,
          ),
          endDate: now,
        );
        break;
      case QuickFilterType.thisMonth:
        final now = DateTime.now();
        state = state.copyWith(
          startDate: DateTime(now.year, now.month, 1),
          endDate: now,
        );
        break;
      case QuickFilterType.pending:
        state = state.copyWith(status: RequestStatus.submitted);
        break;
      case QuickFilterType.urgent:
        state = state.copyWith(urgentOnly: true);
        break;
    }
  }
}

/// حالة عملية الموافقة
///
/// تحتوي على حالة عمليات الموافقة والرفض للطلبات
/// مع تتبع حالة التحميل والأخطاء
class ApprovalProcessState {
  /// هل يتم معالجة الموافقة حالياً؟
  final bool isProcessing;

  /// رسالة الخطأ
  final String? errorMessage;

  /// رسالة النجاح
  final String? successMessage;

  const ApprovalProcessState({
    this.isProcessing = false,
    this.errorMessage,
    this.successMessage,
  });

  /// إنشاء نسخة محدثة من الحالة
  ApprovalProcessState copyWith({
    bool? isProcessing,
    String? errorMessage,
    String? successMessage,
  }) {
    return ApprovalProcessState(
      isProcessing: isProcessing ?? this.isProcessing,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }
}

/// مدير حالة عملية الموافقة
///
/// يدير جميع عمليات الموافقة والرفض للطلبات
/// مع تتبع حالة التحميل وإدارة الأخطاء
class ApprovalProcessNotifier extends StateNotifier<ApprovalProcessState> {
  /// مرجع للمزودات الأخرى
  final Ref _ref;

  ApprovalProcessNotifier(this._ref) : super(const ApprovalProcessState());

  /// معالجة الموافقة أو الرفض
  Future<bool> processApproval(
    String requestId,
    String approverId,
    String approverName,
    bool isApproved, {
    String? notes,
  }) async {
    state = state.copyWith(
      isProcessing: true,
      errorMessage: null,
      successMessage: null,
    );

    try {
      final service = _ref.read(requestManagementServiceProvider);

      await service.processApproval(
        requestId,
        approverId,
        approverName,
        isApproved,
        notes: notes,
      );

      state = state.copyWith(
        isProcessing: false,
        successMessage:
            isApproved ? 'تمت الموافقة على الطلب بنجاح' : 'تم رفض الطلب بنجاح',
      );

      return true;
    } catch (e) {
      state = state.copyWith(
        isProcessing: false,
        errorMessage: 'فشل في معالجة الموافقة: ${e.toString()}',
      );
      return false;
    }
  }

  /// إعادة تعيين الحالة
  void resetState() {
    state = const ApprovalProcessState();
  }

  /// تحديث رسالة الخطأ
  void updateErrorMessage(String? errorMessage) {
    state = state.copyWith(errorMessage: errorMessage);
  }

  /// تحديث رسالة النجاح
  void updateSuccessMessage(String? successMessage) {
    state = state.copyWith(successMessage: successMessage);
  }
}

// ===================================================================
// التعدادات المساعدة
// ===================================================================

/// ترتيب النتائج
enum SortOrder {
  /// تصاعدي
  ascending,

  /// تنازلي
  descending,
}

/// حقل الترتيب
enum SortField {
  /// تاريخ التقديم
  submittedAt,

  /// تاريخ آخر تحديث
  lastUpdated,

  /// عنوان الطلب
  title,

  /// حالة الطلب
  status,

  /// أولوية الطلب
  priority,

  /// نوع الطلب
  requestType,

  /// التاريخ المطلوب للإنجاز
  requestedCompletionDate,
}

/// نوع الفلتر السريع
enum QuickFilterType {
  /// اليوم
  today,

  /// هذا الأسبوع
  thisWeek,

  /// هذا الشهر
  thisMonth,

  /// الطلبات المعلقة
  pending,

  /// الطلبات العاجلة
  urgent,
}
