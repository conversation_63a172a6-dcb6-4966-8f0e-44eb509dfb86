import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/request_management_model.dart';
import 'package:school_management_system/providers/request_management_providers.dart';

/// شاشة إنشاء طلب جديد لأولياء الأمور
///
/// توفر واجهة سهلة ومبسطة لأولياء الأمور لإنشاء طلبات جديدة
/// مع إرشادات واضحة وتحقق من صحة البيانات
///
/// الميزات الرئيسية:
/// - نموذج مبسط وسهل الاستخدام
/// - اختيار نوع الطلب من قائمة محددة مسبقاً
/// - إضافة المرفقات المطلوبة
/// - تحديد الأولوية والتاريخ المطلوب
/// - معاينة الطلب قبل الإرسال
/// - تتبع حالة الإرسال مع رسائل واضحة
/// - دعم كامل للغة العربية
/// - واجهة متجاوبة لجميع الأجهزة
class CreateRequestScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر
  final String parentId;

  /// اسم ولي الأمر
  final String parentName;

  /// معرف الطالب (اختياري)
  final String? studentId;

  /// اسم الطالب (اختياري)
  final String? studentName;

  const CreateRequestScreen({
    super.key,
    required this.parentId,
    required this.parentName,
    this.studentId,
    this.studentName,
  });

  @override
  ConsumerState<CreateRequestScreen> createState() =>
      _CreateRequestScreenState();
}

class _CreateRequestScreenState extends ConsumerState<CreateRequestScreen> {
  /// مفتاح النموذج للتحقق من صحة البيانات
  final _formKey = GlobalKey<FormState>();

  /// تحكم في حقول النص
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _reasonController = TextEditingController();
  final _additionalDetailsController = TextEditingController();

  /// المتغيرات المحلية للنموذج
  RequestType _selectedRequestType = RequestType.certificate;
  RequestCategory _selectedCategory = RequestCategory.academic;
  RequestPriority _selectedPriority = RequestPriority.normal;
  DateTime? _requestedCompletionDate;
  bool _isUrgent = false;

  @override
  void initState() {
    super.initState();

    /// تهيئة النموذج بمعلومات ولي الأمر
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final notifier = ref.read(createRequestFormProvider.notifier);
      notifier.updateRequesterId(widget.parentId);
      notifier.updateRequesterName(widget.parentName);
      notifier.updateRequesterType(RequesterType.parent);

      if (widget.studentId != null && widget.studentName != null) {
        notifier.updateStudentInfo(
          studentId: widget.studentId,
          studentName: widget.studentName,
        );
      }
    });
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _reasonController.dispose();
    _additionalDetailsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(createRequestFormProvider);

    return Scaffold(
      /// شريط التطبيق
      appBar: AppBar(
        title: const Text(
          'طلب جديد',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,

        /// زر المساعدة
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelp,
            tooltip: 'المساعدة',
          ),
        ],
      ),

      /// محتوى الشاشة
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// رسالة ترحيبية
              _buildWelcomeMessage(),

              const SizedBox(height: 24),

              /// قسم نوع الطلب
              _buildRequestTypeSection(),

              const SizedBox(height: 24),

              /// قسم تفاصيل الطلب
              _buildRequestDetailsSection(),

              const SizedBox(height: 24),

              /// قسم الإعدادات الإضافية
              _buildAdditionalSettingsSection(),

              const SizedBox(height: 32),

              /// أزرار الإجراءات
              _buildActionButtons(formState),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء رسالة الترحيب
  Widget _buildWelcomeMessage() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue[700], size: 24),
              const SizedBox(width: 8),
              Text(
                'مرحباً ${widget.parentName}',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'يمكنك من خلال هذا النموذج تقديم طلب جديد للمدرسة. '
            'يرجى ملء جميع الحقول المطلوبة بدقة لضمان معالجة طلبك بسرعة.',
            style: TextStyle(
              fontSize: 14,
              color: Colors.blue[600],
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء قسم نوع الطلب
  Widget _buildRequestTypeSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.category,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'نوع الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            /// اختيار نوع الطلب
            DropdownButtonFormField<RequestType>(
              value: _selectedRequestType,
              decoration: const InputDecoration(
                labelText: 'نوع الطلب *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.request_page),
              ),
              items:
                  RequestType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getRequestTypeLabel(type)),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedRequestType = value;
                  });
                  ref
                      .read(createRequestFormProvider.notifier)
                      .updateRequestType(value);

                  // تحديث الفئة تلقائياً حسب النوع
                  _updateCategoryBasedOnType(value);
                }
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار نوع الطلب';
                }
                return null;
              },
            ),

            const SizedBox(height: 16),

            /// اختيار فئة الطلب
            DropdownButtonFormField<RequestCategory>(
              value: _selectedCategory,
              decoration: const InputDecoration(
                labelText: 'فئة الطلب *',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.folder),
              ),
              items:
                  RequestCategory.values.map((category) {
                    return DropdownMenuItem(
                      value: category,
                      child: Text(_getCategoryLabel(category)),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedCategory = value;
                  });
                  ref
                      .read(createRequestFormProvider.notifier)
                      .updateCategory(value);
                }
              },
              validator: (value) {
                if (value == null) {
                  return 'يرجى اختيار فئة الطلب';
                }
                return null;
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإعدادات الإضافية
  Widget _buildAdditionalSettingsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'إعدادات إضافية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            /// أولوية الطلب
            DropdownButtonFormField<RequestPriority>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'أولوية الطلب',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.priority_high),
              ),
              items:
                  RequestPriority.values.map((priority) {
                    return DropdownMenuItem(
                      value: priority,
                      child: Text(_getPriorityLabel(priority)),
                    );
                  }).toList(),
              onChanged: (value) {
                if (value != null) {
                  setState(() {
                    _selectedPriority = value;
                  });
                  ref
                      .read(createRequestFormProvider.notifier)
                      .updatePriority(value);
                }
              },
            ),

            const SizedBox(height: 16),

            /// التاريخ المطلوب للإنجاز
            InkWell(
              onTap: _selectCompletionDate,
              child: InputDecorator(
                decoration: const InputDecoration(
                  labelText: 'التاريخ المطلوب للإنجاز (اختياري)',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.calendar_today),
                ),
                child: Text(
                  _requestedCompletionDate != null
                      ? _formatDate(_requestedCompletionDate!)
                      : 'اختر التاريخ',
                  style: TextStyle(
                    color:
                        _requestedCompletionDate != null
                            ? Colors.black87
                            : Colors.grey[600],
                  ),
                ),
              ),
            ),

            const SizedBox(height: 16),

            /// مفتاح الطلب العاجل
            SwitchListTile(
              title: const Text('طلب عاجل'),
              subtitle: const Text('يحتاج معالجة سريعة'),
              value: _isUrgent,
              onChanged: (value) {
                setState(() {
                  _isUrgent = value;
                });
                ref
                    .read(createRequestFormProvider.notifier)
                    .updateSettings(isUrgent: value);
              },
              activeColor: Theme.of(context).primaryColor,
              contentPadding: EdgeInsets.zero,
            ),

            const SizedBox(height: 16),

            /// تفاصيل إضافية
            TextFormField(
              controller: _additionalDetailsController,
              decoration: const InputDecoration(
                labelText: 'تفاصيل إضافية (اختياري)',
                hintText: 'أي معلومات إضافية تريد إضافتها...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.note_add),
              ),
              maxLines: 3,
              maxLength: 200,
              onChanged: (value) {
                ref
                    .read(createRequestFormProvider.notifier)
                    .updateAdditionalDetails(value.isEmpty ? null : value);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار الإجراءات
  Widget _buildActionButtons(CreateRequestFormState formState) {
    return Column(
      children: [
        /// زر الإرسال
        SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton.icon(
            onPressed: formState.isSubmitting ? null : _submitRequest,
            icon:
                formState.isSubmitting
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.send),
            label: Text(
              formState.isSubmitting ? 'جاري الإرسال...' : 'إرسال الطلب',
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        const SizedBox(height: 12),

        /// زر المعاينة
        SizedBox(
          width: double.infinity,
          height: 50,
          child: OutlinedButton.icon(
            onPressed: _previewRequest,
            icon: const Icon(Icons.preview),
            label: const Text(
              'معاينة الطلب',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).primaryColor,
              side: BorderSide(color: Theme.of(context).primaryColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ),

        /// رسالة الخطأ
        if (formState.errorMessage != null) ...[
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red[50],
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.red[300]!),
            ),
            child: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.red[700]),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    formState.errorMessage!,
                    style: TextStyle(
                      color: Colors.red[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  // ===================================================================
  // دوال المعالجة والإجراءات
  // ===================================================================

  /// إرسال الطلب
  Future<void> _submitRequest() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final result =
        await ref.read(createRequestFormProvider.notifier).submitRequest();

    if (result != null && mounted) {
      // نجح الإرسال
      showDialog(
        context: context,
        barrierDismissible: false,
        builder:
            (context) => AlertDialog(
              icon: const Icon(
                Icons.check_circle,
                color: Colors.green,
                size: 64,
              ),
              title: const Text('تم إرسال الطلب بنجاح'),
              content: Text(
                'تم إرسال طلبك بنجاح. رقم الطلب: $result\n'
                'ستتلقى إشعاراً عند تحديث حالة الطلب.',
              ),
              actions: [
                TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(); // إغلاق الحوار
                    Navigator.of(context).pop(); // العودة للشاشة السابقة
                  },
                  child: const Text('حسناً'),
                ),
              ],
            ),
      );
    }
  }

  /// معاينة الطلب
  void _previewRequest() {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('معاينة الطلب'),
            content: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildPreviewItem(
                    'نوع الطلب',
                    _getRequestTypeLabel(_selectedRequestType),
                  ),
                  _buildPreviewItem(
                    'فئة الطلب',
                    _getCategoryLabel(_selectedCategory),
                  ),
                  _buildPreviewItem('عنوان الطلب', _titleController.text),
                  _buildPreviewItem('وصف الطلب', _descriptionController.text),
                  _buildPreviewItem('سبب الطلب', _reasonController.text),
                  _buildPreviewItem(
                    'الأولوية',
                    _getPriorityLabel(_selectedPriority),
                  ),
                  if (_requestedCompletionDate != null)
                    _buildPreviewItem(
                      'التاريخ المطلوب',
                      _formatDate(_requestedCompletionDate!),
                    ),
                  if (_isUrgent) _buildPreviewItem('طلب عاجل', 'نعم'),
                  if (_additionalDetailsController.text.isNotEmpty)
                    _buildPreviewItem(
                      'تفاصيل إضافية',
                      _additionalDetailsController.text,
                    ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _submitRequest();
                },
                child: const Text('إرسال الطلب'),
              ),
            ],
          ),
    );
  }

  /// بناء عنصر المعاينة
  Widget _buildPreviewItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
          ),
          const SizedBox(height: 4),
          Text(value, style: const TextStyle(fontSize: 14)),
          const Divider(),
        ],
      ),
    );
  }

  /// اختيار تاريخ الإنجاز المطلوب
  Future<void> _selectCompletionDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate:
          _requestedCompletionDate ??
          DateTime.now().add(const Duration(days: 7)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      helpText: 'اختر التاريخ المطلوب للإنجاز',
      cancelText: 'إلغاء',
      confirmText: 'تأكيد',
    );

    if (picked != null && picked != _requestedCompletionDate) {
      setState(() {
        _requestedCompletionDate = picked;
      });
      ref
          .read(createRequestFormProvider.notifier)
          .updateRequestedCompletionDate(picked);
    }
  }

  /// عرض المساعدة
  void _showHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('المساعدة'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'كيفية تقديم طلب جديد:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 12),
                  Text('1. اختر نوع الطلب المناسب من القائمة'),
                  SizedBox(height: 6),
                  Text('2. املأ عنوان ووصف الطلب بوضوح'),
                  SizedBox(height: 6),
                  Text('3. اذكر السبب وراء تقديم الطلب'),
                  SizedBox(height: 6),
                  Text('4. حدد الأولوية والتاريخ المطلوب (اختياري)'),
                  SizedBox(height: 6),
                  Text('5. راجع الطلب قبل الإرسال'),
                  SizedBox(height: 12),
                  Text(
                    'ملاحظة: ستتلقى إشعاراً عند تحديث حالة طلبك',
                    style: TextStyle(
                      fontStyle: FontStyle.italic,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  /// تحديث الفئة تلقائياً حسب نوع الطلب
  void _updateCategoryBasedOnType(RequestType type) {
    RequestCategory newCategory;

    switch (type) {
      case RequestType.certificate:
      case RequestType.transcript:
      case RequestType.enrollment:
      case RequestType.withdrawal:
        newCategory = RequestCategory.academic;
        break;
      case RequestType.meeting:
      case RequestType.complaint:
      case RequestType.suggestion:
        newCategory = RequestCategory.administrative;
        break;
      case RequestType.leave:
        newCategory = RequestCategory.medical;
        break;
      case RequestType.transfer:
        newCategory = RequestCategory.legal;
        break;
      default:
        newCategory = RequestCategory.other;
    }

    setState(() {
      _selectedCategory = newCategory;
    });
    ref.read(createRequestFormProvider.notifier).updateCategory(newCategory);
  }

  // ===================================================================
  // دوال التسميات والتنسيق
  // ===================================================================

  /// الحصول على تسمية نوع الطلب
  String _getRequestTypeLabel(RequestType type) {
    switch (type) {
      case RequestType.certificate:
        return 'شهادة';
      case RequestType.document:
        return 'وثيقة';
      case RequestType.meeting:
        return 'اجتماع';
      case RequestType.complaint:
        return 'شكوى';
      case RequestType.suggestion:
        return 'اقتراح';
      case RequestType.leave:
        return 'إجازة';
      case RequestType.transfer:
        return 'نقل';
      case RequestType.enrollment:
        return 'تسجيل';
      case RequestType.withdrawal:
        return 'انسحاب';
      case RequestType.transcript:
        return 'كشف درجات';
      case RequestType.recommendation:
        return 'خطاب توصية';
      case RequestType.verification:
        return 'تصديق';
      case RequestType.other:
        return 'أخرى';
    }
  }

  /// الحصول على تسمية فئة الطلب
  String _getCategoryLabel(RequestCategory category) {
    switch (category) {
      case RequestCategory.academic:
        return 'أكاديمي';
      case RequestCategory.administrative:
        return 'إداري';
      case RequestCategory.financial:
        return 'مالي';
      case RequestCategory.medical:
        return 'طبي';
      case RequestCategory.disciplinary:
        return 'تأديبي';
      case RequestCategory.technical:
        return 'تقني';
      case RequestCategory.social:
        return 'اجتماعي';
      case RequestCategory.legal:
        return 'قانوني';
      case RequestCategory.other:
        return 'أخرى';
    }
  }

  /// الحصول على تسمية أولوية الطلب
  String _getPriorityLabel(RequestPriority priority) {
    switch (priority) {
      case RequestPriority.low:
        return 'منخفضة';
      case RequestPriority.normal:
        return 'عادية';
      case RequestPriority.high:
        return 'عالية';
      case RequestPriority.urgent:
        return 'عاجلة';
      case RequestPriority.critical:
        return 'حرجة';
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// بناء قسم تفاصيل الطلب
  Widget _buildRequestDetailsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            /// عنوان القسم
            Row(
              children: [
                Icon(
                  Icons.edit_document,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  'تفاصيل الطلب',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
              ],
            ),

            const SizedBox(height: 16),

            /// عنوان الطلب
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'عنوان الطلب *',
                hintText: 'مثال: طلب شهادة حسن سير وسلوك',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.title),
              ),
              maxLength: 100,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال عنوان الطلب';
                }
                if (value.trim().length < 5) {
                  return 'عنوان الطلب قصير جداً';
                }
                return null;
              },
              onChanged: (value) {
                ref.read(createRequestFormProvider.notifier).updateTitle(value);
              },
            ),

            const SizedBox(height: 16),

            /// وصف الطلب
            TextFormField(
              controller: _descriptionController,
              decoration: const InputDecoration(
                labelText: 'وصف الطلب *',
                hintText: 'اشرح تفاصيل طلبك بوضوح...',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.description),
              ),
              maxLines: 4,
              maxLength: 500,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال وصف الطلب';
                }
                if (value.trim().length < 10) {
                  return 'وصف الطلب قصير جداً';
                }
                return null;
              },
              onChanged: (value) {
                ref
                    .read(createRequestFormProvider.notifier)
                    .updateDescription(value);
              },
            ),

            const SizedBox(height: 16),

            /// سبب الطلب
            TextFormField(
              controller: _reasonController,
              decoration: const InputDecoration(
                labelText: 'سبب الطلب *',
                hintText: 'ما هو السبب وراء هذا الطلب؟',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.help_outline),
              ),
              maxLines: 3,
              maxLength: 300,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'يرجى إدخال سبب الطلب';
                }
                return null;
              },
              onChanged: (value) {
                ref
                    .read(createRequestFormProvider.notifier)
                    .updateReason(value);
              },
            ),
          ],
        ),
      ),
    );
  }
}
