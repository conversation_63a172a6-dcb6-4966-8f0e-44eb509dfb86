/// ملف تجميع شاشات الأولياء
///
/// هذا الملف يجمع شاشات الأولياء المتكاملة في مكان واحد
/// لسهولة الاستيراد والاستخدام في أجزاء أخرى من التطبيق
///
/// الشاشات المتضمنة:
/// - شاشة إنشاء طلب جديد (متكاملة مع Firebase)
///
/// ملاحظة: تم استبدال الشاشات الوهمية بشاشات متكاملة في mobile_screens:
/// - guardian_home_page.dart (بدلاً من parent_dashboard_screen.dart)
/// - child_dashboard_screen.dart (بدلاً من children_performance_screen.dart)
/// - guardian_communication_screen.dart (بدلاً من school_communication_screen.dart)
///
/// الاستخدام:
/// ```dart
/// import 'package:school_management_system/parent_screens/parent_screens_exports.dart';
///
/// // شاشة إنشاء طلب جديد
/// CreateRequestScreen(parentId: parentId, parentName: parentName)
/// ```

// تصدير شاشة إنشاء الطلب (متكاملة 100%)
export 'create_request_screen.dart';
