import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/teacher_model.dart';
import 'package:school_management_system/services/teacher_service.dart';

// Provider لخدمة المعلمين
final teacherServiceProvider = Provider<TeacherService>((ref) {
  return TeacherService();
});

// StreamProvider لجلب قائمة المعلمين
final teachersStreamProvider = StreamProvider.autoDispose<List<TeacherModel>>((ref) {
  return ref.watch(teacherServiceProvider).getTeachers();
});
