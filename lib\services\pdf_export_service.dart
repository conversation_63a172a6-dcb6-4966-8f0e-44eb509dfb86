import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:school_management_system/models/student_model.dart';

/// خدمة لإنشاء وتصدير ملفات PDF.
class PdfExportService {
  /// دالة عامة لتصدير أي مستند PDF.
  static Future<void> exportPdf(BuildContext context, pw.Document pdf, String fileName) async {
    try {
      final pdfBytes = await pdf.save();
      if (kIsWeb) {
        await Printing.sharePdf(bytes: pdfBytes, filename: fileName);
      } else {
        await FileSaver.instance.saveFile(
          name: fileName,
          bytes: pdfBytes,
          ext: 'pdf',
          mimeType: MimeType.pdf,
        );
      }
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('تم تصدير الملف "$fileName" بنجاح.')),
      );
    } catch (e) {
      print('Error exporting PDF: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('حدث خطأ أثناء تصدير الملف: $e')),
      );
    }
  }

  /// دالة خاصة لإنشاء محتوى PDF لتقرير طالب فردي.
  Future<pw.Document> _generatePdf(
    StudentModel student, {
    required Map<String, dynamic> financialData,
    required List<Map<String, dynamic>> grades,
    required List<Map<String, dynamic>> attendance,
  }) async {
    final pdf = pw.Document();
    final fontData = await rootBundle.load("assets/fonts/Amiri-Regular.ttf");
    final ttf = pw.Font.ttf(fontData);

    final assignedFees = (financialData['assignedFees'] as List).cast<Map<String, dynamic>>();

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(base: ttf, bold: ttf),
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) => [
          pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('التقرير الشامل للطالب', style: pw.TextStyle(font: ttf, fontSize: 24)),
                    pw.Text('تاريخ: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}'),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Text('اسم الطالب: ${student.name}'),
                pw.Text('الرقم الأكاديمي: ${student.studentNumber}'),
                pw.Divider(),
                pw.SizedBox(height: 10),

                // --- القسم المالي ---
                pw.Text('التقرير المالي', style: pw.TextStyle(font: ttf, fontSize: 18, fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(height: 10),
                pw.Table.fromTextArray(
                  context: context,
                  headerStyle: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
                  cellStyle: pw.TextStyle(font: ttf),
                  headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  headers: <String>['المتبقي', 'المدفوع', 'الإجمالي', 'نوع الرسوم'],
                  data: assignedFees.map((fee) {
                    final total = (fee['amount_total'] as num).toDouble();
                    final paid = (fee['amount_paid'] as num).toDouble();
                    return <String>[
                      (total - paid).toStringAsFixed(2),
                      paid.toStringAsFixed(2),
                      total.toStringAsFixed(2),
                      fee['fee_type_name'] ?? '',
                    ];
                  }).toList(),
                ),
                pw.SizedBox(height: 10),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'الإجمالي المتبقي: ${financialData['totalRemaining']?.toStringAsFixed(2) ?? '0.00'}',
                      style: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
                    ),
                  ],
                ),
                pw.Divider(),
                pw.SizedBox(height: 10),

                // --- قسم الدرجات ---
                pw.Text('تقرير الدرجات', style: pw.TextStyle(font: ttf, fontSize: 18, fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(height: 10),
                grades.isEmpty
                    ? pw.Text('لا توجد درجات مسجلة.')
                    : pw.Table.fromTextArray(
                        context: context,
                        headerStyle: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
                        cellStyle: pw.TextStyle(font: ttf),
                        headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
                        headers: <String>['الدرجة', 'المادة'],
                        data: grades.map((grade) {
                          return <String>[
                            grade['grade']?.toString() ?? 'N/A',
                            grade['subjectName'] ?? 'غير محدد',
                          ];
                        }).toList(),
                      ),
                pw.Divider(),
                pw.SizedBox(height: 10),

                // --- قسم الحضور ---
                pw.Text('تقرير الحضور والغياب', style: pw.TextStyle(font: ttf, fontSize: 18, fontWeight: pw.FontWeight.bold)),
                pw.SizedBox(height: 10),
                attendance.isEmpty
                    ? pw.Text('لا يوجد سجل حضور.')
                    : pw.Table.fromTextArray(
                        context: context,
                        headerStyle: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
                        cellStyle: pw.TextStyle(font: ttf),
                        headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
                        headers: <String>['الحالة', 'التاريخ'],
                        data: attendance.map((att) {
                           final date = (att['date'] as Timestamp).toDate();
                           final formattedDate = DateFormat('yyyy/MM/dd').format(date);
                          return <String>[
                            att['status'] ?? 'غير محدد',
                            formattedDate,
                          ];
                        }).toList(),
                      ),
              ],
            ),
          ),
        ],
      ),
    );
    return pdf;
  }

  /// دالة لإنشاء وطباعة تقرير PDF فردي.
  Future<void> generateAndPrintPdf(
    StudentModel student, {
    required Map<String, dynamic> financialData,
    required List<Map<String, dynamic>> grades,
    required List<Map<String, dynamic>> attendance,
  }) async {
    final pdf = await _generatePdf(student, financialData: financialData, grades: grades, attendance: attendance);
    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  /// دالة لإنشاء وتحميل تقرير PDF فردي.
  Future<void> generateAndDownloadPdf(
    BuildContext context,
    StudentModel student, {
    required Map<String, dynamic> financialData,
    required List<Map<String, dynamic>> grades,
    required List<Map<String, dynamic>> attendance,
  }) async {
    final pdf = await _generatePdf(student, financialData: financialData, grades: grades, attendance: attendance);
    final fileName = 'report_${student.studentNumber}_${DateTime.now().toIso8601String().substring(0, 10)}.pdf';
    await exportPdf(context, pdf, fileName);
  }

  /// دالة لإنشاء وتنزيل تقرير PDF مجمع لصف دراسي كامل.
  Future<void> generateClassComprehensivePdf(BuildContext context, String className, List<Map<String, dynamic>> reportData) async {
    final pdf = pw.Document();
    final fontData = await rootBundle.load("assets/fonts/Amiri-Regular.ttf");
    final ttf = pw.Font.ttf(fontData);

    // --- حساب الإجماليات للصف ---
    double classTotalRemaining = 0;
    double classTotalGrades = 0;
    int classTotalStudentsWithGrades = 0;
    int classTotalPresent = 0;
    int classTotalAbsent = 0;

    for (var data in reportData) {
      final financials = data['financials'] as Map<String, dynamic>;
      final grades = (data['grades'] as List).cast<Map<String, dynamic>>();
      final attendance = (data['attendance'] as List).cast<Map<String, dynamic>>();

      classTotalRemaining += (financials['totalRemaining'] as num?) ?? 0.0;
      
      if (grades.isNotEmpty) {
        double studentTotalGrades = 0;
        grades.forEach((grade) {
          studentTotalGrades += (grade['grade'] as num?) ?? 0.0;
        });
        classTotalGrades += studentTotalGrades / grades.length;
        classTotalStudentsWithGrades++;
      }

      classTotalPresent += attendance.where((a) => a['status'] == 'حاضر').length;
      classTotalAbsent += attendance.where((a) => a['status'] == 'غائب').length;
    }

    final classAverageGrade = classTotalStudentsWithGrades > 0 ? classTotalGrades / classTotalStudentsWithGrades : 0.0;

    pdf.addPage(
      pw.MultiPage(
        theme: pw.ThemeData.withFont(base: ttf, bold: ttf),
        pageFormat: PdfPageFormat.a4,
        header: (pw.Context context) {
          return pw.Header(
            level: 0,
            child: pw.Directionality(
              textDirection: pw.TextDirection.rtl,
              child: pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Text('تقرير مجمع لـ: $className', style: pw.TextStyle(font: ttf, fontSize: 20)),
                  pw.Text('تاريخ: ${DateFormat('yyyy/MM/dd').format(DateTime.now())}'),
                ],
              ),
            ),
          );
        },
        build: (pw.Context context) {
          List<pw.Widget> studentWidgets = [];
          for (var data in reportData) {
            final studentInfo = data['studentInfo'] as Map<String, dynamic>;
            final financials = data['financials'] as Map<String, dynamic>;
            final grades = (data['grades'] as List).cast<Map<String, dynamic>>();
            final attendance = (data['attendance'] as List).cast<Map<String, dynamic>>();
            
            double studentAverageGrade = 0.0;
            if (grades.isNotEmpty) {
              double totalGrades = 0;
              grades.forEach((grade) {
                totalGrades += (grade['grade'] as num?) ?? 0.0;
              });
              studentAverageGrade = totalGrades / grades.length;
            }

            final presentCount = attendance.where((a) => a['status'] == 'حاضر').length;
            final absentCount = attendance.where((a) => a['status'] == 'غائب').length;

            studentWidgets.add(
              pw.Directionality(
                textDirection: pw.TextDirection.rtl,
                child: pw.Container(
                  margin: const pw.EdgeInsets.only(bottom: 15),
                  padding: const pw.EdgeInsets.all(10),
                  decoration: pw.BoxDecoration(
                    border: pw.Border.all(color: PdfColors.grey),
                    borderRadius: const pw.BorderRadius.all(pw.Radius.circular(5)),
                  ),
                  child: pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'الطالب: ${studentInfo['name'] ?? ''} - الرقم الأكاديمي: ${studentInfo['student_number'] ?? ''}',
                        style: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold, fontSize: 16),
                      ),
                      pw.Divider(height: 10),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text('المتبقي المالي: ${financials['totalRemaining']?.toStringAsFixed(2) ?? '0.00'}'),
                          pw.Text('متوسط الدرجات: ${studentAverageGrade.toStringAsFixed(2)}'),
                        ],
                      ),
                      pw.SizedBox(height: 5),
                      pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                        children: [
                          pw.Text('أيام الحضور: $presentCount'),
                          pw.Text('أيام الغياب: $absentCount'),
                        ],
                      ),
                    ],
                  ),
                ),
              )
            );
          }
          return studentWidgets;
        },
        footer: (pw.Context context) {
          return pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Container(
              alignment: pw.Alignment.center,
              margin: const pw.EdgeInsets.only(top: 10.0),
              padding: const pw.EdgeInsets.all(10),
              decoration: const pw.BoxDecoration(
                border: pw.Border(top: pw.BorderSide(color: PdfColors.grey, width: 2)),
              ),
              child: pw.Column(
                children: [
                  pw.Text('ملخص الصف', style: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold, fontSize: 18)),
                  pw.SizedBox(height: 10),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Text('إجمالي المتبقي: ${classTotalRemaining.toStringAsFixed(2)}'),
                      pw.Text('متوسط درجات الصف: ${classAverageGrade.toStringAsFixed(2)}'),
                    ],
                  ),
                  pw.SizedBox(height: 5),
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                    children: [
                      pw.Text('إجمالي الحضور: $classTotalPresent'),
                      pw.Text('إجمالي الغياب: $classTotalAbsent'),
                    ],
                  ),
                ]
              )
            ),
          );
        },
      ),
    );

    final fileName = 'class_report_${className.replaceAll(' ', '_')}_${DateTime.now().toIso8601String().substring(0, 10)}.pdf';
    await exportPdf(context, pdf, fileName);
  }

  /// دالة لإنشاء وتصدير جدول دراسي إلى PDF.
  Future<void> exportTimetableToPdf({
    required String className,
    required Map<String, List<String>> timetableData,
    required List<String> days,
    required int periods,
  }) async {
    final pdf = pw.Document();
    final fontData = await rootBundle.load("assets/fonts/Amiri-Regular.ttf");
    final ttf = pw.Font.ttf(fontData);

    // بناء رأس الجدول
    final List<String> headers = ['اليوم'];
    for (int i = 1; i <= periods; i++) {
      headers.add('الحصة $i');
    }

    // بناء بيانات الجدول
    final List<List<String>> tableData = [];
    for (var day in days) {
      final row = [day];
      row.addAll(timetableData[day] ?? List.filled(periods, '-'));
      tableData.add(row);
    }

    pdf.addPage(
      pw.Page(
        theme: pw.ThemeData.withFont(base: ttf, bold: ttf),
        pageFormat: PdfPageFormat.a4.landscape,
        build: (pw.Context context) {
          return pw.Directionality(
            textDirection: pw.TextDirection.rtl,
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.center,
              children: [
                pw.Text('الجدول الدراسي - فصل: $className', style: pw.TextStyle(font: ttf, fontSize: 24)),
                pw.SizedBox(height: 20),
                pw.Table.fromTextArray(
                  context: context,
                  headerStyle: pw.TextStyle(font: ttf, fontWeight: pw.FontWeight.bold),
                  cellStyle: pw.TextStyle(font: ttf, fontSize: 10),
                  headerDecoration: const pw.BoxDecoration(color: PdfColors.grey300),
                  cellAlignment: pw.Alignment.center,
                  headers: headers.reversed.toList(), // عكس الرؤوس لتناسب RTL
                  data: tableData.map((row) => row.reversed.toList()).toList(), // عكس كل صف
                ),
              ],
            ),
          );
        },
      ),
    );

    final fileName = 'timetable_${className.replaceAll(' ', '_')}_${DateTime.now().toIso8601String().substring(0, 10)}.pdf';
    final pdfBytes = await pdf.save();

    if (kIsWeb) {
      await Printing.sharePdf(bytes: pdfBytes, filename: fileName);
    } else {
      await FileSaver.instance.saveFile(
        name: fileName,
        bytes: pdfBytes,
        ext: 'pdf',
        mimeType: MimeType.pdf,
      );
    }
  }
}
