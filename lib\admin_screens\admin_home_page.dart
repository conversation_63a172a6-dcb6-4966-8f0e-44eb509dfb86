import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/dashboard_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';

/// The main homepage for the Admin Dashboard.
///
/// Displays a summary of important information using statistical cards.
/// It now uses Riverpod to fetch data, making it decoupled and testable.
class AdminHomePage extends ConsumerWidget {
  const AdminHomePage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // List of statistical cards. Some are fetched via providers, others are static for now.
    final List<Widget> summaryCards = [
      _buildStatsCard(
        ref: ref,
        title: 'إجمالي الطلاب',
        icon: Icons.people_alt_rounded,
        color: Colors.blue,
        provider: studentsCountProvider,
      ),
      _buildStatsCard(
        ref: ref,
        title: 'إجمالي الموظفين',
        icon: Icons.badge_rounded,
        color: Colors.green,
        provider: staffCountProvider,
      ),
      // Fixed cards for demonstration
      _buildFixedStatsCard(
        context: context,
        title: 'الرسائل الجديدة',
        value: '12',
        icon: Icons.email_rounded,
        color: Colors.orange,
      ),
      _buildFixedStatsCard(
        context: context,
        title: 'الفعاليات القادمة',
        value: '3',
        icon: Icons.event_rounded,
        color: Colors.purple,
      ),
    ];

    return LayoutBuilder(builder: (context, constraints) {
      int crossAxisCount = 4;
      if (constraints.maxWidth < 1200) crossAxisCount = 3;
      if (constraints.maxWidth < 800) crossAxisCount = 2;
      if (constraints.maxWidth < 500) crossAxisCount = 1;

      return Padding(
        padding: const EdgeInsets.all(16.0),
        child: GridView.builder(
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: crossAxisCount,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.8,
          ),
          itemCount: summaryCards.length,
          itemBuilder: (context, index) {
            return summaryCards[index];
          },
        ),
      );
    });
  }

  /// A helper widget to build a statistics card that fetches its value from a FutureProvider.
  Widget _buildStatsCard({
    required WidgetRef ref,
    required String title,
    required IconData icon,
    required Color color,
    required ProviderBase<AsyncValue<int>> provider,
  }) {
    final asyncValue = ref.watch(provider);

    return asyncValue.when(
      data: (count) => _buildFixedStatsCard(
        context: ref.context,
        title: title,
        value: count.toString(),
        icon: icon,
        color: color,
      ),
      loading: () => _buildFixedStatsCard(
        context: ref.context,
        title: title,
        value: '...', // Loading indicator
        icon: icon,
        color: color,
      ),
      error: (err, stack) => _buildFixedStatsCard(
        context: ref.context,
        title: title,
        value: 'خطأ', // Error indicator
        icon: icon,
        color: Colors.red,
      ),
    );
  }

  /// A helper widget to build a statistics card with a fixed value.
  Widget _buildFixedStatsCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return CustomCard(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 40, color: color),
          const SizedBox(height: 12),
          Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  color: color,
                  fontWeight: FontWeight.bold,
                ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
