import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج المحادثة المتقدم
/// 
/// يمثل محادثة كاملة بين ولي الأمر والمدرسة (معلم أو إدارة)
/// يدعم المحادثات متعددة الأطراف والرسائل المتسلسلة
/// 
/// الميزات المتقدمة:
/// - دعم المحادثات الجماعية
/// - تصنيف المحادثات حسب النوع والأولوية
/// - تتبع حالة القراءة لكل مشارك
/// - ربط المحادثة بطالب محدد
/// - دعم الملفات المرفقة
/// - أرشفة المحادثات المكتملة
class ConversationModel {
  /// معرف المحادثة الفريد
  final String id;
  
  /// عنوان المحادثة
  final String subject;
  
  /// وصف مختصر للمحادثة
  final String description;
  
  /// نوع المحادثة (استفسار، شكوى، اقتراح، طلب، طارئ)
  final ConversationType type;
  
  /// أولوية المحادثة (منخفضة، عادية، مهمة، عاجلة)
  final ConversationPriority priority;
  
  /// حالة المحادثة (نشطة، مغلقة، مؤرشفة، معلقة)
  final ConversationStatus status;
  
  /// قائمة معرفات المشاركين في المحادثة
  /// يشمل: ولي الأمر، المعلمين، الإدارة
  final List<String> participantIds;
  
  /// تفاصيل المشاركين (الأسماء والأدوار)
  final Map<String, ParticipantInfo> participants;
  
  /// معرف الطالب المرتبط بالمحادثة (إن وجد)
  final String? studentId;
  
  /// اسم الطالب المرتبط بالمحادثة
  final String? studentName;
  
  /// معرف منشئ المحادثة (عادة ولي الأمر)
  final String createdBy;
  
  /// تاريخ إنشاء المحادثة
  final DateTime createdAt;
  
  /// تاريخ آخر تحديث للمحادثة
  final DateTime updatedAt;
  
  /// معرف آخر رسالة في المحادثة
  final String? lastMessageId;
  
  /// نص آخر رسالة (للعرض السريع)
  final String? lastMessageText;
  
  /// تاريخ آخر رسالة
  final DateTime? lastMessageTime;
  
  /// معرف مرسل آخر رسالة
  final String? lastMessageSenderId;
  
  /// عدد الرسائل الإجمالي في المحادثة
  final int messageCount;
  
  /// عدد الرسائل غير المقروءة لكل مشارك
  final Map<String, int> unreadCounts;
  
  /// العلامات والتصنيفات الإضافية
  final List<String> tags;
  
  /// هل المحادثة مؤرشفة؟
  final bool isArchived;
  
  /// هل المحادثة مثبتة؟
  final bool isPinned;
  
  /// تاريخ الأرشفة (إن وجد)
  final DateTime? archivedAt;
  
  /// معرف من قام بالأرشفة
  final String? archivedBy;
  
  /// ملاحظات إضافية على المحادثة
  final String? notes;

  const ConversationModel({
    required this.id,
    required this.subject,
    required this.description,
    required this.type,
    required this.priority,
    required this.status,
    required this.participantIds,
    required this.participants,
    this.studentId,
    this.studentName,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    this.lastMessageId,
    this.lastMessageText,
    this.lastMessageTime,
    this.lastMessageSenderId,
    this.messageCount = 0,
    this.unreadCounts = const {},
    this.tags = const [],
    this.isArchived = false,
    this.isPinned = false,
    this.archivedAt,
    this.archivedBy,
    this.notes,
  });

  /// إنشاء نموذج من وثيقة Firestore
  factory ConversationModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ConversationModel(
      id: doc.id,
      subject: data['subject'] ?? '',
      description: data['description'] ?? '',
      type: ConversationType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => ConversationType.inquiry,
      ),
      priority: ConversationPriority.values.firstWhere(
        (e) => e.toString() == data['priority'],
        orElse: () => ConversationPriority.normal,
      ),
      status: ConversationStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => ConversationStatus.active,
      ),
      participantIds: List<String>.from(data['participantIds'] ?? []),
      participants: (data['participants'] as Map<String, dynamic>? ?? {})
          .map((key, value) => MapEntry(
                key,
                ParticipantInfo.fromMap(value as Map<String, dynamic>),
              )),
      studentId: data['studentId'],
      studentName: data['studentName'],
      createdBy: data['createdBy'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessageId: data['lastMessageId'],
      lastMessageText: data['lastMessageText'],
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate(),
      lastMessageSenderId: data['lastMessageSenderId'],
      messageCount: data['messageCount'] ?? 0,
      unreadCounts: Map<String, int>.from(data['unreadCounts'] ?? {}),
      tags: List<String>.from(data['tags'] ?? []),
      isArchived: data['isArchived'] ?? false,
      isPinned: data['isPinned'] ?? false,
      archivedAt: (data['archivedAt'] as Timestamp?)?.toDate(),
      archivedBy: data['archivedBy'],
      notes: data['notes'],
    );
  }

  /// تحويل النموذج إلى خريطة للحفظ في Firestore
  Map<String, dynamic> toMap() {
    return {
      'subject': subject,
      'description': description,
      'type': type.toString(),
      'priority': priority.toString(),
      'status': status.toString(),
      'participantIds': participantIds,
      'participants': participants.map((key, value) => MapEntry(key, value.toMap())),
      'studentId': studentId,
      'studentName': studentName,
      'createdBy': createdBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastMessageId': lastMessageId,
      'lastMessageText': lastMessageText,
      'lastMessageTime': lastMessageTime != null 
          ? Timestamp.fromDate(lastMessageTime!) 
          : null,
      'lastMessageSenderId': lastMessageSenderId,
      'messageCount': messageCount,
      'unreadCounts': unreadCounts,
      'tags': tags,
      'isArchived': isArchived,
      'isPinned': isPinned,
      'archivedAt': archivedAt != null ? Timestamp.fromDate(archivedAt!) : null,
      'archivedBy': archivedBy,
      'notes': notes,
    };
  }

  /// إنشاء نسخة محدثة من المحادثة
  ConversationModel copyWith({
    String? subject,
    String? description,
    ConversationType? type,
    ConversationPriority? priority,
    ConversationStatus? status,
    List<String>? participantIds,
    Map<String, ParticipantInfo>? participants,
    String? studentId,
    String? studentName,
    DateTime? updatedAt,
    String? lastMessageId,
    String? lastMessageText,
    DateTime? lastMessageTime,
    String? lastMessageSenderId,
    int? messageCount,
    Map<String, int>? unreadCounts,
    List<String>? tags,
    bool? isArchived,
    bool? isPinned,
    DateTime? archivedAt,
    String? archivedBy,
    String? notes,
  }) {
    return ConversationModel(
      id: id,
      subject: subject ?? this.subject,
      description: description ?? this.description,
      type: type ?? this.type,
      priority: priority ?? this.priority,
      status: status ?? this.status,
      participantIds: participantIds ?? this.participantIds,
      participants: participants ?? this.participants,
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      createdBy: createdBy,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      lastMessageId: lastMessageId ?? this.lastMessageId,
      lastMessageText: lastMessageText ?? this.lastMessageText,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageSenderId: lastMessageSenderId ?? this.lastMessageSenderId,
      messageCount: messageCount ?? this.messageCount,
      unreadCounts: unreadCounts ?? this.unreadCounts,
      tags: tags ?? this.tags,
      isArchived: isArchived ?? this.isArchived,
      isPinned: isPinned ?? this.isPinned,
      archivedAt: archivedAt ?? this.archivedAt,
      archivedBy: archivedBy ?? this.archivedBy,
      notes: notes ?? this.notes,
    );
  }
}

/// معلومات المشارك في المحادثة
class ParticipantInfo {
  /// معرف المشارك
  final String id;
  
  /// اسم المشارك
  final String name;
  
  /// دور المشارك (ولي أمر، معلم، إدارة)
  final ParticipantRole role;
  
  /// القسم أو المادة (للمعلمين)
  final String? department;
  
  /// هل المشارك متصل حالياً؟
  final bool isOnline;
  
  /// آخر ظهور للمشارك
  final DateTime? lastSeen;
  
  /// صورة المشارك الشخصية
  final String? avatarUrl;

  const ParticipantInfo({
    required this.id,
    required this.name,
    required this.role,
    this.department,
    this.isOnline = false,
    this.lastSeen,
    this.avatarUrl,
  });

  factory ParticipantInfo.fromMap(Map<String, dynamic> map) {
    return ParticipantInfo(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      role: ParticipantRole.values.firstWhere(
        (e) => e.toString() == map['role'],
        orElse: () => ParticipantRole.parent,
      ),
      department: map['department'],
      isOnline: map['isOnline'] ?? false,
      lastSeen: (map['lastSeen'] as Timestamp?)?.toDate(),
      avatarUrl: map['avatarUrl'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'role': role.toString(),
      'department': department,
      'isOnline': isOnline,
      'lastSeen': lastSeen != null ? Timestamp.fromDate(lastSeen!) : null,
      'avatarUrl': avatarUrl,
    };
  }
}

/// أنواع المحادثات
enum ConversationType {
  inquiry,     // استفسار
  complaint,   // شكوى
  suggestion,  // اقتراح
  request,     // طلب
  emergency,   // طارئ
  general,     // عام
  academic,    // أكاديمي
  behavioral,  // سلوكي
  financial,   // مالي
  medical,     // طبي
}

/// أولويات المحادثات
enum ConversationPriority {
  low,      // منخفضة
  normal,   // عادية
  high,     // مهمة
  urgent,   // عاجلة
  critical, // حرجة
}

/// حالات المحادثات
enum ConversationStatus {
  active,    // نشطة
  closed,    // مغلقة
  archived,  // مؤرشفة
  pending,   // معلقة
  resolved,  // محلولة
}

/// أدوار المشاركين
enum ParticipantRole {
  parent,      // ولي أمر
  teacher,     // معلم
  admin,       // إدارة
  principal,   // مدير
  counselor,   // مرشد
  nurse,       // ممرضة
  staff,       // موظف
}
