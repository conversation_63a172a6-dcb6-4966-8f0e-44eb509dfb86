import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/providers/about_school_providers.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class AboutSchoolScreen extends ConsumerStatefulWidget {
  const AboutSchoolScreen({super.key});

  @override
  ConsumerState<AboutSchoolScreen> createState() => _AboutSchoolScreenState();
}

class _AboutSchoolScreenState extends ConsumerState<AboutSchoolScreen> {
  // Controllers for text fields
  late final TextEditingController _visionController;
  late final TextEditingController _missionController;
  late final TextEditingController _contactInfoController;
  late final TextEditingController _historyController;
  late final TextEditingController _valuesController;
  late final TextEditingController _principalMessageController;
  late QuillController _descriptionController;

  @override
  void initState() {
    super.initState();
    final initialState = ref.read(aboutSchoolControllerProvider);
    _visionController = TextEditingController(text: initialState.vision);
    _missionController = TextEditingController(text: initialState.mission);
    _contactInfoController = TextEditingController(
      text: initialState.contactInfo,
    );
    _historyController = TextEditingController(text: initialState.history);
    _valuesController = TextEditingController(text: initialState.values);
    _principalMessageController = TextEditingController(
      text: initialState.principalMessage,
    );
    _descriptionController = QuillController(
      document:
          initialState.description.isEmpty()
              ? Document()
              : Document.fromJson(
                jsonDecode(
                  jsonEncode(initialState.description.toDelta().toJson()),
                ),
              ),
      selection: const TextSelection.collapsed(offset: 0),
    );
  }

  @override
  void dispose() {
    _visionController.dispose();
    _missionController.dispose();
    _contactInfoController.dispose();
    _historyController.dispose();
    _valuesController.dispose();
    _principalMessageController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(aboutSchoolControllerProvider);
    final controller = ref.read(aboutSchoolControllerProvider.notifier);

    ref.listen<AboutSchoolState>(aboutSchoolControllerProvider, (
      previous,
      next,
    ) {
      if (previous?.isSaving == true && next.isSaving == false) {
        showSuccessSnackBar(context, 'تم حفظ التغييرات بنجاح.');
      }
    });

    if (state.isLoading) {
      return const LoadingIndicator();
    }

    return Scaffold(
      body: AbsorbPointer(
        absorbing: state.isSaving,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: CustomCard(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Text(
                    'تعديل معلومات المدرسة',
                    style: Theme.of(context).textTheme.headlineMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 32),
                  _buildLogoSection(context, state, controller),
                  const SizedBox(height: 24),
                  const Divider(),
                  const SizedBox(height: 24),
                  TextField(
                    controller: _visionController,
                    decoration: const InputDecoration(
                      labelText: 'رؤيتنا',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _missionController,
                    decoration: const InputDecoration(
                      labelText: 'رسالتنا',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _historyController,
                    decoration: const InputDecoration(
                      labelText: 'تاريخ المدرسة',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 5,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _valuesController,
                    decoration: const InputDecoration(
                      labelText: 'قيمنا',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 5,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _principalMessageController,
                    decoration: const InputDecoration(
                      labelText: 'كلمة المدير',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 5,
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _contactInfoController,
                    decoration: const InputDecoration(
                      labelText: 'معلومات التواصل',
                      border: OutlineInputBorder(),
                    ),
                    maxLines: 3,
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'وصف عام',
                    style: Theme.of(context).textTheme.titleMedium,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey.shade300),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        QuillSimpleToolbar(
                          controller: _descriptionController,
                          config: const QuillSimpleToolbarConfig(
                            showAlignmentButtons: true,
                          ),
                        ),
                        const Divider(height: 1),
                        SizedBox(
                          height: 250,
                          child: QuillEditor.basic(
                            controller: _descriptionController,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  const Divider(),
                  const SizedBox(height: 24),
                  _buildGallerySection(context, state, controller),
                  const SizedBox(height: 32),
                  state.isSaving
                      ? const Center(child: LoadingIndicator())
                      : ElevatedButton.icon(
                        onPressed: () {
                          controller.saveContent(
                            vision: _visionController.text,
                            mission: _missionController.text,
                            contactInfo: _contactInfoController.text,
                            history: _historyController.text,
                            values: _valuesController.text,
                            principalMessage: _principalMessageController.text,
                            description: _descriptionController.document,
                          );
                        },
                        icon: const Icon(Icons.save),
                        label: const Text('حفظ كل التغييرات'),
                        style: ElevatedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                        ),
                      ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLogoSection(
    BuildContext context,
    AboutSchoolState state,
    AboutSchoolController controller,
  ) {
    return Column(
      children: [
        Text('شعار المدرسة', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade400),
            borderRadius: BorderRadius.circular(8),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child:
                state.newLogoFile != null
                    ? (kIsWeb
                        ? Image.memory(
                          File(state.newLogoFile!.path).readAsBytesSync(),
                          fit: BoxFit.cover,
                        )
                        : Image.file(
                          File(state.newLogoFile!.path),
                          fit: BoxFit.cover,
                        ))
                    : (state.logoUrl != null && state.logoUrl!.isNotEmpty
                        ? Image.network(
                          state.logoUrl!,
                          fit: BoxFit.cover,
                          errorBuilder:
                              (context, error, stackTrace) => const Icon(
                                Icons.school,
                                size: 60,
                                color: Colors.grey,
                              ),
                        )
                        : const Icon(
                          Icons.school,
                          size: 60,
                          color: Colors.grey,
                        )),
          ),
        ),
        const SizedBox(height: 16),
        TextButton.icon(
          icon: const Icon(Icons.image),
          label: const Text('تغيير الشعار'),
          onPressed: controller.pickNewLogo,
        ),
      ],
    );
  }

  Widget _buildGallerySection(
    BuildContext context,
    AboutSchoolState state,
    AboutSchoolController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('معرض صور المدرسة', style: Theme.of(context).textTheme.titleLarge),
        const SizedBox(height: 16),
        if (state.galleryImageUrls.isEmpty && state.newGalleryImages.isEmpty)
          const Center(child: Text('لا يوجد صور في المعرض حالياً.')),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
            maxCrossAxisExtent: 150,
            crossAxisSpacing: 8,
            mainAxisSpacing: 8,
          ),
          itemCount: state.galleryImageUrls.length,
          itemBuilder: (context, index) {
            final imageUrl = state.galleryImageUrls[index];
            return Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    image: DecorationImage(
                      image: NetworkImage(imageUrl),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Positioned(
                  top: 4,
                  right: 4,
                  child: CircleAvatar(
                    backgroundColor: Colors.black54,
                    radius: 16,
                    child: IconButton(
                      icon: const Icon(
                        Icons.delete,
                        color: Colors.white,
                        size: 16,
                      ),
                      onPressed: () async {
                        final confirm = await showDialog<bool>(
                          context: context,
                          builder:
                              (context) => AlertDialog(
                                title: const Text('تأكيد الحذف'),
                                content: const Text(
                                  'هل أنت متأكد من رغبتك في حذف هذه الصورة نهائياً؟',
                                ),
                                actions: [
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(false),
                                    child: const Text('إلغاء'),
                                  ),
                                  TextButton(
                                    onPressed:
                                        () => Navigator.of(context).pop(true),
                                    child: const Text('حذف'),
                                  ),
                                ],
                              ),
                        );
                        if (confirm == true) {
                          await controller.deleteGalleryImage(imageUrl);
                          if (context.mounted)
                            showSuccessSnackBar(
                              context,
                              'تم حذف الصورة بنجاح.',
                            );
                        }
                      },
                    ),
                  ),
                ),
              ],
            );
          },
        ),
        const SizedBox(height: 16),
        if (state.newGalleryImages.isNotEmpty) ...[
          const Text(
            'صور جديدة سيتم رفعها:',
            style: TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithMaxCrossAxisExtent(
              maxCrossAxisExtent: 150,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
            ),
            itemCount: state.newGalleryImages.length,
            itemBuilder: (context, index) {
              final image = state.newGalleryImages[index];
              return Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      image: DecorationImage(
                        image:
                            kIsWeb
                                ? MemoryImage(
                                      File(image.path).readAsBytesSync(),
                                    )
                                    as ImageProvider
                                : FileImage(File(image.path)),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  Positioned(
                    top: 4,
                    right: 4,
                    child: CircleAvatar(
                      backgroundColor: Colors.black54,
                      radius: 16,
                      child: IconButton(
                        icon: const Icon(
                          Icons.close,
                          color: Colors.white,
                          size: 16,
                        ),
                        onPressed:
                            () => controller.removeNewGalleryImage(index),
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
          const SizedBox(height: 16),
        ],
        Center(
          child: ElevatedButton.icon(
            onPressed: controller.pickGalleryImages,
            icon: const Icon(Icons.add_a_photo),
            label: const Text('إضافة صور للمعرض'),
          ),
        ),
      ],
    );
  }
}
