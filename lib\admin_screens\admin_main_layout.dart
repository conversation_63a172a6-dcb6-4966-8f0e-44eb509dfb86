import 'package:flutter/material.dart';
import 'package:school_management_system/admin_screens/about_school_screen.dart';
import 'package:school_management_system/admin_screens/admins_management_screen.dart';
import 'package:school_management_system/admin_screens/content_management_screen.dart'
    hide Colors;
import 'package:school_management_system/admin_screens/assignments_management_screen.dart';
import 'package:school_management_system/admin_screens/attendance_management_screen.dart';
import 'package:school_management_system/admin_screens/fees_management_screen.dart';
import 'package:school_management_system/admin_screens/fees_types_management_screen.dart'; // Import the new screen
import 'package:school_management_system/admin_screens/grades_management_screen.dart';
import 'package:school_management_system/admin_screens/guardian_management_screen.dart';
import 'package:school_management_system/admin_screens/admin_home_page.dart';
import 'package:school_management_system/admin_screens/notifications_screen.dart';
import 'package:school_management_system/admin_screens/staff_management_screen.dart';
import 'package:school_management_system/admin_screens/student_feedback_screen.dart';
import 'package:school_management_system/admin_screens/student_management_screen.dart';
import 'package:school_management_system/admin_screens/student_reports_screen.dart';
import 'package:school_management_system/admin_screens/classes_management_screen.dart';
import 'package:school_management_system/admin_screens/subjects_management_screen.dart';
import 'package:school_management_system/admin_screens/timetables_management_screen.dart';
// استيراد شاشات الامتحانات الجديدة
import 'package:school_management_system/admin_screens/exams_dashboard_screen.dart';
import 'package:school_management_system/admin_screens/exam_schedules_management_screen.dart';
import 'package:school_management_system/admin_screens/exam_settings_screen.dart';
// استيراد شاشة التواصل المتقدمة
import 'package:school_management_system/admin_screens/advanced_messaging_screen.dart';
import 'package:school_management_system/models/conversation_model.dart';
// استيراد شاشة إدارة المواعيد
import 'package:school_management_system/admin_screens/appointments_management_screen.dart';
// استيراد شاشة إدارة تقييمات السلوك
import 'package:school_management_system/admin_screens/behavior_evaluation_management_screen.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// التخطيط الرئيسي للوحة تحكم الإدارة
class AdminMainLayout extends StatefulWidget {
  const AdminMainLayout({super.key});

  @override
  State<AdminMainLayout> createState() => _AdminMainLayoutState();
}

class _AdminMainLayoutState extends State<AdminMainLayout> {
  int _selectedIndex = 0;

  // قائمة الصفحات التي يمكن للمسؤول التنقل بينها
  final List<Widget> _pages = [
    const AdminHomePage(),
    const ContentManagementScreen(),
    const AssignmentsManagementScreen(),
    const AttendanceManagementScreen(),
    const FeesManagementScreen(),
    FeesTypesManagementScreen(), // Add the new screen to the pages list
    const GradesManagementScreen(),
    const StudentManagementScreen(),
    const GuardianManagementScreen(),
    const StudentReportsScreen(),
    const StudentFeedbackScreen(),
    const NotificationsScreen(),
    const StaffManagementScreen(),
    const AdminsManagementScreen(),
    const SubjectsManagementScreen(),
    const ClassesManagementScreen(),
    const TimetablesManagementScreen(),
    const AboutSchoolScreen(),
    // شاشات الامتحانات الجديدة
    const ExamsDashboardScreen(), // 18: لوحة معلومات الامتحانات
    const ExamSchedulesManagementScreen(), // 19: إدارة جداول الامتحانات
    const ExamSettingsScreen(), // 20: إعدادات نظام الامتحانات
    // شاشة التواصل المتقدمة
    const AdvancedMessagingScreen(
      adminId: 'admin_001', // TODO: استخدام معرف المدير الفعلي
      adminName: 'مدير النظام', // TODO: استخدام اسم المدير الفعلي
      adminRole: ParticipantRole.admin,
    ), // 21: إدارة التواصل المتقدمة
    // شاشة إدارة المواعيد
    const AppointmentsManagementScreen(
      adminId: 'admin_001', // TODO: استخدام معرف المدير الفعلي
      adminName: 'مدير النظام', // TODO: استخدام اسم المدير الفعلي
      adminRole: 'admin',
    ), // 22: إدارة المواعيد والاجتماعات
    // شاشة إدارة تقييمات السلوك
    const BehaviorEvaluationManagementScreen(
      adminId: 'admin_001', // TODO: استخدام معرف المدير الفعلي
      adminName: 'مدير النظام', // TODO: استخدام اسم المدير الفعلي
      adminRole: 'admin',
    ), // 23: إدارة تقييمات السلوك
  ];

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final isSmallScreen = constraints.maxWidth < 1000;

        return Scaffold(
          appBar: AppBar(
            title: const Text('لوحة تحكم المدرسة'),
            actions: [
              IconButton(
                icon: const Icon(Icons.logout),
                tooltip: 'تسجيل الخروج',
                onPressed: () {
                  FirebaseService().signOut();
                },
              ),
            ],
            leading:
                isSmallScreen
                    ? Builder(
                      builder:
                          (context) => IconButton(
                            icon: const Icon(Icons.menu),
                            onPressed: () => Scaffold.of(context).openDrawer(),
                          ),
                    )
                    : null,
          ),
          drawer: isSmallScreen ? _buildDrawer() : null,
          body: Row(
            children: [
              if (!isSmallScreen)
                SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height,
                    ),
                    child: IntrinsicHeight(
                      child: NavigationRail(
                        selectedIndex: _selectedIndex,
                        onDestinationSelected: (int index) {
                          setState(() {
                            _selectedIndex = index;
                          });
                        },
                        labelType: NavigationRailLabelType.all,
                        destinations: _buildNavDestinations(),
                      ),
                    ),
                  ),
                ),
              if (!isSmallScreen) const VerticalDivider(thickness: 1, width: 1),
              Expanded(child: _pages[_selectedIndex]),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDrawer() {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(color: Theme.of(context).primaryColor),
            child: const Text(
              'القائمة',
              style: TextStyle(color: Colors.white, fontSize: 24),
            ),
          ),
          ...List.generate(_buildNavDestinations().length, (index) {
            final dest = _buildNavDestinations()[index];
            return ListTile(
              leading: _selectedIndex == index ? dest.selectedIcon : dest.icon,
              title: dest.label,
              selected: _selectedIndex == index,
              onTap: () {
                setState(() {
                  _selectedIndex = index;
                });
                Navigator.of(context).pop();
              },
            );
          }),
        ],
      ),
    );
  }

  List<NavigationRailDestination> _buildNavDestinations() {
    return const <NavigationRailDestination>[
      NavigationRailDestination(
        icon: Icon(Icons.dashboard_outlined),
        selectedIcon: Icon(Icons.dashboard),
        label: Text('الرئيسية'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.article_outlined),
        selectedIcon: Icon(Icons.edit_document),
        label: Text('المحتوى'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.assignment_outlined),
        selectedIcon: Icon(Icons.assignment),
        label: Text('الواجبات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.check_circle_outline),
        selectedIcon: Icon(Icons.check_circle),
        label: Text('الحاضر'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.monetization_on_outlined),
        selectedIcon: Icon(Icons.monetization_on),
        label: Text('الرسوم'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.category_outlined), // New Icon
        selectedIcon: Icon(Icons.category), // New Icon
        label: Text('أنواع الرسوم'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.grade_outlined),
        selectedIcon: Icon(Icons.grade),
        label: Text('الدرجات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.school_outlined),
        selectedIcon: Icon(Icons.school),
        label: Text('الطلاب'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.supervisor_account_outlined),
        selectedIcon: Icon(Icons.supervisor_account),
        label: Text('أولياء الأمور'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.bar_chart_outlined),
        selectedIcon: Icon(Icons.bar_chart),
        label: Text('التقارير'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.feedback_outlined),
        selectedIcon: Icon(Icons.feedback),
        label: Text('الملاحظات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.notifications_outlined),
        selectedIcon: Icon(Icons.notifications),
        label: Text('الإشعارات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.people_outline),
        selectedIcon: Icon(Icons.people),
        label: Text('المعلمين'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.admin_panel_settings_outlined),
        selectedIcon: Icon(Icons.admin_panel_settings),
        label: Text('المسؤولين'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.book_outlined),
        selectedIcon: Icon(Icons.book),
        label: Text('المواد'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.class_outlined),
        selectedIcon: Icon(Icons.class_),
        label: Text('الفصول'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.schedule_outlined),
        selectedIcon: Icon(Icons.schedule),
        label: Text('الجداول'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.info_outline),
        selectedIcon: Icon(Icons.info),
        label: Text('عن المدرسة'),
      ),
      // وجهات تنقل الامتحانات الجديدة
      NavigationRailDestination(
        icon: Icon(Icons.quiz_outlined),
        selectedIcon: Icon(Icons.quiz),
        label: Text('الامتحانات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.calendar_today_outlined),
        selectedIcon: Icon(Icons.calendar_today),
        label: Text('جداول الامتحانات'),
      ),
      NavigationRailDestination(
        icon: Icon(Icons.settings_outlined),
        selectedIcon: Icon(Icons.settings),
        label: Text('إعدادات الامتحانات'),
      ),
      // وجهة تنقل التواصل المتقدمة
      NavigationRailDestination(
        icon: Icon(Icons.forum_outlined),
        selectedIcon: Icon(Icons.forum),
        label: Text('التواصل المتقدم'),
      ),
      // وجهة تنقل إدارة المواعيد
      NavigationRailDestination(
        icon: Icon(Icons.event_outlined),
        selectedIcon: Icon(Icons.event),
        label: Text('إدارة المواعيد'),
      ),
      // وجهة تنقل إدارة تقييمات السلوك
      NavigationRailDestination(
        icon: Icon(Icons.psychology_outlined),
        selectedIcon: Icon(Icons.psychology),
        label: Text('تقييم السلوك'),
      ),
    ];
  }
}
