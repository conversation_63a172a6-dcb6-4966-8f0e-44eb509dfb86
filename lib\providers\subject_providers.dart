import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/subject_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provides a stream of all subjects.
final subjectsStreamProvider = StreamProvider.autoDispose<List<Subject>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getSubjectsStream();
});

/// Provides a stream of all classes, used for dropdowns.
final classesForSubjectsProvider = StreamProvider.autoDispose<List<ClassModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getClassesStream();
});
