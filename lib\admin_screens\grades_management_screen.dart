import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:school_management_system/admin_screens/student_grades_details_screen.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/attendance_providers.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/grades_providers.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// A screen for managing student grades, refactored with Riverpod.
class GradesManagementScreen extends ConsumerWidget {
  const GradesManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الدرجات'),
        automaticallyImplyLeading: false,
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const ClassesDropdownForGrades(),
                const SizedBox(height: 16),
                TextField(
                  onChanged: (value) {
                    ref.read(gradesSearchQueryProvider.notifier).state = value;
                  },
                  decoration: const InputDecoration(
                    labelText: 'ابحث عن طالب بالاسم أو الرقم الأكاديمي...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
          ),
          const Expanded(
            child: StudentsListForGrades(),
          ),
        ],
      ),
    );
  }
}

/// A dropdown widget to select a class, specifically for the grades screen.
class ClassesDropdownForGrades extends ConsumerWidget {
  const ClassesDropdownForGrades({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classesAsyncValue = ref.watch(classesStreamProvider);
    final selectedClassId = ref.watch(selectedClassIdProvider);

    return classesAsyncValue.when(
      data: (classes) {
        return DropdownButtonFormField<String>(
          value: selectedClassId,
          hint: Text('اختر صفًا لعرض الطلاب', style: GoogleFonts.cairo()),
          items: classes.map((ClassModel classModel) {
            return DropdownMenuItem<String>(
              value: classModel.id,
              child: Text(classModel.name),
            );
          }).toList(),
          onChanged: (value) {
            ref.read(selectedClassIdProvider.notifier).state = value;
          },
          decoration: const InputDecoration(
            labelText: 'الصف الدراسي',
            border: OutlineInputBorder(),
          ),
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => ErrorMessage(message: err.toString()),
    );
  }
}

/// A list widget to display students for grade management.
class StudentsListForGrades extends ConsumerWidget {
  const StudentsListForGrades({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedClassId = ref.watch(selectedClassIdProvider);
    final filteredStudents = ref.watch(filteredGradesStudentsProvider);
    final classId = ref.watch(selectedClassIdProvider);
    final studentsAsyncValue = ref.watch(classStudentsStreamProvider(classId));

    if (selectedClassId == null) {
      return const Center(child: Text('الرجاء اختيار صف لعرض الطلاب.'));
    }

    return studentsAsyncValue.when(
      data: (students) {
        if (students.isEmpty) {
          return const Center(child: Text('لا يوجد طلاب في هذا الفصل.'));
        }
        if (filteredStudents.isEmpty) {
          return const Center(child: Text('لم يتم العثور على نتائج للبحث.'));
        }
        return ListView.builder(
          itemCount: filteredStudents.length,
          itemBuilder: (context, index) {
            final student = filteredStudents[index];
            return Card(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: ListTile(
                title: Text(student.name, style: GoogleFonts.cairo(fontWeight: FontWeight.bold)),
                subtitle: Text('الرقم الأكاديمي: ${student.studentNumber}'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => StudentGradesDetailsScreen(student: student),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
      loading: () => const LoadingIndicator(),
      error: (err, stack) => ErrorMessage(message: err.toString()),
    );
  }
}
