
import 'package:cloud_firestore/cloud_firestore.dart';

/// نموذج بيانات الملاحظات (من الإدارة للطالب أو العكس)
class NoteModel {
  final String id;
  final String title; // عنوان الملاحظة
  final String content; // محتوى الملاحظة
  final String senderId; // مُعرّف المُرسِل
  final String receiverId; // مُعرّف المُستقبِل ('admin' للإدارة)
  final DateTime timestamp; // تاريخ ووقت الإرسال
  bool isRead; // هل تمت قراءتها
  final String? reply; // نص الرد من الإدارة
  final DateTime? repliedAt; // تاريخ ووقت الرد
  final List<String>? participants; // قائمة بمعرفات المشاركين (المرسل والمستقبل)

  NoteModel({
    required this.id,
    required this.title,
    required this.content,
    required this.senderId,
    required this.receiverId,
    required this.timestamp,
    this.isRead = false,
    this.reply,
    this.repliedAt,
    this.participants,
  });

  factory NoteModel.fromMap(Map<String, dynamic> data, String documentId) {
    return NoteModel(
      id: documentId,
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      senderId: data['senderId'] ?? '',
      receiverId: data['receiverId'] ?? '',
      timestamp: (data['timestamp'] as Timestamp).toDate(),
      isRead: data['isRead'] ?? false,
      reply: data['reply'],
      repliedAt: data['repliedAt'] != null ? (data['repliedAt'] as Timestamp).toDate() : null,
      participants: data['participants'] != null ? List<String>.from(data['participants']) : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'content': content,
      'senderId': senderId,
      'receiverId': receiverId,
      'timestamp': Timestamp.fromDate(timestamp),
      'isRead': isRead,
      'reply': reply,
      'repliedAt': repliedAt != null ? Timestamp.fromDate(repliedAt!) : null,
      'participants': participants,
    };
  }
}
