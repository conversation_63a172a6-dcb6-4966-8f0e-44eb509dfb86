import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/activity_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لجلب عدد الطلاب.
final studentsCountProvider = FutureProvider.autoDispose<int>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getUsersCountByRole('student');
});

/// Provider لجلب عدد الموظفين (معلمين وإداريين).
final staffCountProvider = FutureProvider.autoDispose<int>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getUsersCountByRole(['teacher', 'staff']);
});

/// Provider لجلب إحصائيات لوحة المعلومات العامة.
final publicDashboardStatsProvider = FutureProvider.autoDispose<Map<String, int>>((ref) async {
  final firebaseService = ref.watch(firebaseServiceProvider);
  // جلب الإحصائيات بالتوازي لتحسين الأداء
  final results = await Future.wait([
    firebaseService.getCollection(path: 'lessons', builder: (data, id) => 1).first,
    firebaseService.getCollection(path: 'activities', builder: (data, id) => 1).first,
    firebaseService.getUsersCountByRole(['teacher', 'staff']),
  ]);

  return {
    'lessons': (results[0] as List).length,
    'activities': (results[1] as List).length,
    'staff': results[2] as int,
  };
});

/// Provider لجلب الأنشطة القادمة.
final upcomingActivitiesProvider = StreamProvider.autoDispose<List<ActivityModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getActivitiesStream().map((snapshot) => snapshot.docs
      .map((doc) => ActivityModel.fromMap(doc.data() as Map<String, dynamic>, doc.id))
      .where((activity) => activity.date.isAfter(DateTime.now()))
      .take(3)
      .toList());
});
