import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// لوحة تحكم الأولياء الرئيسية
///
/// هذه الشاشة تعتبر المركز الرئيسي لولي الأمر في النظام حيث تعرض:
/// - ملخص شامل لأداء جميع الأبناء في المدرسة
/// - الإشعارات والتنبيهات المهمة من المدرسة
/// - الأحداث والمواعيد القادمة (امتحانات، اجتماعات، فعاليات)
/// - إحصائيات سريعة عن الحضور والغياب
/// - آخر النتائج والدرجات المنشورة
/// - حالة الرسوم الدراسية لكل طالب
/// - روابط سريعة للوظائف الأكثر استخداماً
///
/// تدفق العمل في الشاشة:
/// 1. تحميل قائمة الأبناء المرتبطين بولي الأمر
/// 2. جلب البيانات الأساسية لكل طالب (الدرجات، الحضور، الرسوم)
/// 3. عرض ملخص تفاعلي لكل طالب في بطاقات منفصلة
/// 4. إظهار الإشعارات والتنبيهات الحديثة
/// 5. توفير وصول سريع للوظائف المهمة
///
/// الألوان والتصميم:
/// - اللون الأساسي: أخضر داكن (يرمز للنمو والتطور)
/// - ألوان فرعية: أخضر فاتح، ذهبي، أزرق فاتح
/// - تصميم عائلي دافئ ومريح للعين
/// - بطاقات مستديرة الزوايا مع ظلال ناعمة
/// - أيقونات واضحة ومفهومة للأولياء
class ParentDashboardScreen extends ConsumerStatefulWidget {
  /// معرف ولي الأمر الذي يستخدم النظام
  /// يستخدم لتحميل بيانات الأبناء المرتبطين به
  final String parentId;

  const ParentDashboardScreen({super.key, required this.parentId});

  @override
  ConsumerState<ParentDashboardScreen> createState() =>
      _ParentDashboardScreenState();
}

class _ParentDashboardScreenState extends ConsumerState<ParentDashboardScreen>
    with TickerProviderStateMixin {
  // ===================================================================
  // متحكمات الواجهة والرسوم المتحركة
  // ===================================================================

  /// متحكم الرسوم المتحركة لظهور البطاقات تدريجياً
  /// يستخدم لإنشاء تأثير جميل عند تحميل الشاشة
  late AnimationController _cardAnimationController;

  /// الرسم المتحرك لشفافية البطاقات
  /// يتحكم في ظهور البطاقات من الشفافية الكاملة إلى الوضوح الكامل
  late Animation<double> _cardFadeAnimation;

  /// الرسم المتحرك لحركة البطاقات من الأسفل
  /// يجعل البطاقات تنزلق من الأسفل إلى موضعها النهائي
  late Animation<Offset> _cardSlideAnimation;

  /// متحكم تحديث البيانات
  /// يستخدم لإظهار مؤشر التحديث عند سحب الشاشة للأسفل
  final GlobalKey<RefreshIndicatorState> _refreshKey =
      GlobalKey<RefreshIndicatorState>();

  // ===================================================================
  // متغيرات البيانات الرئيسية
  // ===================================================================

  /// قائمة الأبناء المرتبطين بولي الأمر
  /// تحتوي على معلومات كاملة عن كل طالب
  List<StudentModel> _children = [];

  /// خريطة ملخص أداء كل طالب
  /// المفتاح: معرف الطالب، القيمة: بيانات الأداء
  /// تتضمن: المعدل، الحضور، عدد الغيابات، حالة الرسوم
  Map<String, Map<String, dynamic>> _childrenSummary = {};

  /// قائمة الإشعارات الحديثة من المدرسة
  /// مرتبة حسب التاريخ من الأحدث إلى الأقدم
  List<Map<String, dynamic>> _recentNotifications = [];

  /// قائمة الأحداث والمواعيد القادمة
  /// تشمل: امتحانات، اجتماعات أولياء أمور، فعاليات مدرسية
  List<Map<String, dynamic>> _upcomingEvents = [];

  /// قائمة آخر النتائج المنشورة لجميع الأبناء
  /// مرتبة حسب تاريخ النشر من الأحدث إلى الأقدم
  List<Map<String, dynamic>> _latestResults = [];

  // ===================================================================
  // متغيرات حالة التحميل والتفاعل
  // ===================================================================

  /// حالة تحميل البيانات الأولية
  /// true عند تحميل البيانات لأول مرة
  bool _isLoading = false;

  /// حالة تحديث البيانات
  /// true عند سحب الشاشة للتحديث
  bool _isRefreshing = false;

  /// حالة وجود خطأ في التحميل
  /// true إذا فشل تحميل البيانات
  bool _hasError = false;

  /// رسالة الخطأ إن وجدت
  /// تحتوي على تفاصيل الخطأ لعرضها للمستخدم
  String _errorMessage = '';

  // ===================================================================
  // إحصائيات سريعة للعرض
  // ===================================================================

  /// إجمالي عدد الأبناء المسجلين
  int _totalChildren = 0;

  /// عدد الأبناء الذين لديهم نتائج ممتازة (معدل 90+)
  int _excellentPerformers = 0;

  /// عدد الأبناء الذين يحتاجون متابعة (معدل أقل من 70)
  int _needsAttention = 0;

  /// إجمالي الرسوم المستحقة لجميع الأبناء
  double _totalOutstandingFees = 0.0;

  /// عدد الإشعارات غير المقروءة
  int _unreadNotifications = 0;

  /// عدد الأحداث القادمة في الأسبوع القادم
  int _upcomingEventsCount = 0;

  @override
  void initState() {
    super.initState();

    // إعداد متحكمات الرسوم المتحركة
    _setupAnimations();

    // تحميل البيانات الأولية
    _loadDashboardData();
  }

  @override
  void dispose() {
    // تنظيف الموارد عند إغلاق الشاشة
    _cardAnimationController.dispose();
    super.dispose();
  }

  /// إعداد الرسوم المتحركة للواجهة
  ///
  /// هذه الدالة تقوم بإنشاء وتكوين جميع الرسوم المتحركة
  /// المستخدمة في الشاشة لإنشاء تجربة مستخدم سلسة وجذابة
  void _setupAnimations() {
    // إنشاء متحكم الرسوم المتحركة مع مدة 800 مللي ثانية
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // إنشاء رسم متحرك للشفافية من 0 إلى 1
    _cardFadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _cardAnimationController,
        curve: Curves.easeInOut,
      ),
    );

    // إنشاء رسم متحرك للحركة من الأسفل
    _cardSlideAnimation = Tween<Offset>(
      begin: const Offset(0.0, 0.3), // يبدأ من 30% أسفل الموضع النهائي
      end: Offset.zero, // ينتهي في الموضع النهائي
    ).animate(
      CurvedAnimation(
        parent: _cardAnimationController,
        curve: Curves.easeOutCubic,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع تصميم مخصص للأولياء
      appBar: AppBar(
        title: const Text(
          'لوحة تحكم ولي الأمر',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.green[800], // لون أخضر داكن مناسب للأولياء
        elevation: 2,

        // أزرار الإجراءات في شريط التطبيق
        actions: [
          // زر الإشعارات مع عداد الرسائل غير المقروءة
          Stack(
            children: [
              IconButton(
                icon: const Icon(Icons.notifications, color: Colors.white),
                onPressed: () => _showNotificationsDialog(),
                tooltip: 'الإشعارات',
              ),
              // عداد الإشعارات غير المقروءة
              if (_unreadNotifications > 0)
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(10),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 16,
                      minHeight: 16,
                    ),
                    child: Text(
                      '$_unreadNotifications',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),

          // زر تحديث البيانات
          if (_isRefreshing)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              ),
            )
          else
            IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white),
              onPressed: () => _refreshDashboard(),
              tooltip: 'تحديث البيانات',
            ),
        ],
      ),

      // محتوى الشاشة الرئيسي
      body: _buildBody(),

      // زر عائم للإجراءات السريعة
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => _showQuickActionsDialog(),
        backgroundColor: Colors.green[600],
        icon: const Icon(Icons.family_restroom, color: Colors.white),
        label: const Text(
          'إجراءات سريعة',
          style: TextStyle(color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }

  // ===================================================================
  // دوال تحميل البيانات والتحليل
  // ===================================================================

  /// تحميل بيانات لوحة التحكم من قاعدة البيانات
  ///
  /// هذه الدالة تقوم بتحميل جميع البيانات المطلوبة لعرض لوحة التحكم:
  /// - قائمة الأبناء المرتبطين بولي الأمر
  /// - ملخص أداء كل طالب (الدرجات، الحضور، الرسوم)
  /// - الإشعارات الحديثة من المدرسة
  /// - الأحداث والمواعيد القادمة
  /// - آخر النتائج المنشورة
  /// - حساب الإحصائيات والملخصات
  Future<void> _loadDashboardData() async {
    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      // TODO: تحميل البيانات الفعلية من قاعدة البيانات
      // هنا سيتم استدعاء الخدمات لجلب:
      // 1. قائمة الأبناء من جدول الطلاب
      // 2. درجات وحضور كل طالب
      // 3. حالة الرسوم الدراسية
      // 4. الإشعارات والأحداث

      // محاكاة تحميل البيانات
      await Future.delayed(const Duration(seconds: 2));

      // محاكاة بيانات الأبناء والأداء
      _simulateChildrenData();

      // محاكاة الإشعارات والأحداث
      _simulateNotificationsAndEvents();

      // حساب الإحصائيات والملخصات
      _calculateStatistics();

      // بدء الرسوم المتحركة بعد تحميل البيانات
      _cardAnimationController.forward();
    } catch (e) {
      // معالجة الأخطاء وعرض رسالة مناسبة
      setState(() {
        _hasError = true;
        _errorMessage = 'خطأ في تحميل البيانات: $e';
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_errorMessage),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'إعادة المحاولة',
              textColor: Colors.white,
              onPressed: () => _loadDashboardData(),
            ),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// محاكاة بيانات الأبناء والأداء للاختبار
  ///
  /// هذه الدالة تنشئ بيانات وهمية لاختبار الواجهة
  /// في التطبيق الفعلي ستحل محلها البيانات من قاعدة البيانات
  void _simulateChildrenData() {
    final now = DateTime.now();

    // محاكاة قائمة الأبناء
    _children = [
      StudentModel(
        id: 'student1',
        name: 'أحمد محمد علي',
        email: '<EMAIL>',
        studentNumber: 'STU001',
        studentClass: 'الصف السادس أ',
        classId: 'class1',
        createdAt: Timestamp.fromDate(now.subtract(const Duration(days: 365))),
        dateOfBirth: DateTime(2010, 5, 15),
        address: 'صنعاء، اليمن',
        phoneNumber: '777123456',
        isActive: true,
        profileImageUrl: null,
        gender: 'ذكر',
      ),
      StudentModel(
        id: 'student2',
        name: 'فاطمة أحمد محمد',
        email: '<EMAIL>',
        studentNumber: 'STU002',
        studentClass: 'الصف الرابع ب',
        classId: 'class2',
        createdAt: Timestamp.fromDate(now.subtract(const Duration(days: 200))),
        dateOfBirth: DateTime(2012, 8, 22),
        address: 'صنعاء، اليمن',
        phoneNumber: '777123457',
        isActive: true,
        profileImageUrl: null,
        gender: 'أنثى',
      ),
    ];

    // محاكاة ملخص أداء كل طالب
    _childrenSummary = {
      'student1': {
        'averageGrade': 87.5,
        'attendanceRate': 95.2,
        'totalAbsences': 3,
        'outstandingFees': 50000.0,
        'lastExamGrade': 92.0,
        'lastExamSubject': 'الرياضيات',
        'behaviorRating': 'ممتاز',
        'teacherNotes': 'طالب متفوق ومجتهد',
      },
      'student2': {
        'averageGrade': 78.3,
        'attendanceRate': 88.7,
        'totalAbsences': 8,
        'outstandingFees': 25000.0,
        'lastExamGrade': 85.0,
        'lastExamSubject': 'العلوم',
        'behaviorRating': 'جيد جداً',
        'teacherNotes': 'تحتاج مزيد من التركيز في الرياضيات',
      },
    };
  }

  /// محاكاة الإشعارات والأحداث
  ///
  /// تنشئ بيانات وهمية للإشعارات والأحداث القادمة
  void _simulateNotificationsAndEvents() {
    final now = DateTime.now();

    // محاكاة الإشعارات الحديثة
    _recentNotifications = [
      {
        'id': 'notif1',
        'title': 'نتائج امتحان الفصل الأول',
        'message': 'تم نشر نتائج امتحان الفصل الأول لجميع المواد',
        'date': now.subtract(const Duration(hours: 2)),
        'isRead': false,
        'type': 'results',
        'priority': 'high',
      },
      {
        'id': 'notif2',
        'title': 'اجتماع أولياء الأمور',
        'message': 'يرجى حضور اجتماع أولياء الأمور يوم الخميس القادم',
        'date': now.subtract(const Duration(days: 1)),
        'isRead': false,
        'type': 'meeting',
        'priority': 'medium',
      },
      {
        'id': 'notif3',
        'title': 'تذكير بالرسوم الدراسية',
        'message': 'يرجى سداد الرسوم الدراسية المستحقة قبل نهاية الشهر',
        'date': now.subtract(const Duration(days: 3)),
        'isRead': true,
        'type': 'fees',
        'priority': 'high',
      },
    ];

    // محاكاة الأحداث القادمة
    _upcomingEvents = [
      {
        'id': 'event1',
        'title': 'امتحان الرياضيات',
        'description': 'امتحان نصف الفصل في مادة الرياضيات',
        'date': now.add(const Duration(days: 3)),
        'type': 'exam',
        'studentId': 'student1',
        'studentName': 'أحمد محمد علي',
      },
      {
        'id': 'event2',
        'title': 'اجتماع أولياء الأمور',
        'description': 'اجتماع دوري لمناقشة أداء الطلاب',
        'date': now.add(const Duration(days: 5)),
        'type': 'meeting',
        'studentId': null,
        'studentName': null,
      },
      {
        'id': 'event3',
        'title': 'امتحان العلوم',
        'description': 'امتحان شهري في مادة العلوم',
        'date': now.add(const Duration(days: 7)),
        'type': 'exam',
        'studentId': 'student2',
        'studentName': 'فاطمة أحمد محمد',
      },
    ];

    // محاكاة آخر النتائج
    _latestResults = [
      {
        'studentId': 'student1',
        'studentName': 'أحمد محمد علي',
        'subject': 'الرياضيات',
        'grade': 92.0,
        'maxGrade': 100.0,
        'examType': 'نصفي',
        'publishDate': now.subtract(const Duration(hours: 6)),
      },
      {
        'studentId': 'student2',
        'studentName': 'فاطمة أحمد محمد',
        'subject': 'العلوم',
        'grade': 85.0,
        'maxGrade': 100.0,
        'examType': 'شهري',
        'publishDate': now.subtract(const Duration(days: 1)),
      },
    ];
  }

  /// حساب الإحصائيات والملخصات
  ///
  /// يحسب جميع الإحصائيات المطلوبة لعرض الملخص في لوحة التحكم
  void _calculateStatistics() {
    // حساب إجمالي عدد الأبناء
    _totalChildren = _children.length;

    // حساب عدد المتفوقين والذين يحتاجون متابعة
    _excellentPerformers = 0;
    _needsAttention = 0;
    _totalOutstandingFees = 0.0;

    for (final child in _children) {
      final summary = _childrenSummary[child.id];
      if (summary != null) {
        final average = summary['averageGrade'] as double;
        final fees = summary['outstandingFees'] as double;

        if (average >= 90.0) {
          _excellentPerformers++;
        } else if (average < 70.0) {
          _needsAttention++;
        }

        _totalOutstandingFees += fees;
      }
    }

    // حساب عدد الإشعارات غير المقروءة
    _unreadNotifications =
        _recentNotifications
            .where((notif) => !(notif['isRead'] as bool))
            .length;

    // حساب عدد الأحداث القادمة في الأسبوع القادم
    final nextWeek = DateTime.now().add(const Duration(days: 7));
    _upcomingEventsCount =
        _upcomingEvents
            .where((event) => (event['date'] as DateTime).isBefore(nextWeek))
            .length;
  }

  /// تحديث بيانات لوحة التحكم
  ///
  /// يتم استدعاؤها عند سحب الشاشة للأسفل أو الضغط على زر التحديث
  Future<void> _refreshDashboard() async {
    setState(() {
      _isRefreshing = true;
    });

    try {
      // إعادة تحميل جميع البيانات
      await _loadDashboardData();

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم تحديث البيانات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  /// عرض حوار الإشعارات
  ///
  /// يعرض قائمة بجميع الإشعارات الحديثة مع إمكانية قراءتها وإدارتها
  void _showNotificationsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('الإشعارات'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: _recentNotifications.length,
                itemBuilder: (context, index) {
                  final notification = _recentNotifications[index];
                  return ListTile(
                    leading: Icon(
                      _getNotificationIcon(notification['type'] as String),
                      color:
                          notification['isRead'] as bool
                              ? Colors.grey
                              : Colors.blue,
                    ),
                    title: Text(
                      notification['title'] as String,
                      style: TextStyle(
                        fontWeight:
                            notification['isRead'] as bool
                                ? FontWeight.normal
                                : FontWeight.bold,
                      ),
                    ),
                    subtitle: Text(notification['message'] as String),
                    onTap: () {
                      // تحديد الإشعار كمقروء
                      setState(() {
                        notification['isRead'] = true;
                      });
                      Navigator.pop(context);
                    },
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// عرض حوار الإجراءات السريعة
  ///
  /// يعرض قائمة بالإجراءات الأكثر استخداماً للوصول السريع
  void _showQuickActionsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إجراءات سريعة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: const Icon(Icons.assessment, color: Colors.blue),
                  title: const Text('عرض جميع النتائج'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: الانتقال إلى شاشة النتائج
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.payment, color: Colors.orange),
                  title: const Text('دفع الرسوم'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: الانتقال إلى شاشة الرسوم
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.message, color: Colors.green),
                  title: const Text('التواصل مع المدرسة'),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: الانتقال إلى شاشة التواصل
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إغلاق'),
              ),
            ],
          ),
    );
  }

  /// الحصول على أيقونة الإشعار حسب النوع
  ///
  /// [type] نوع الإشعار (results, meeting, fees, etc.)
  /// يرجع الأيقونة المناسبة لكل نوع
  IconData _getNotificationIcon(String type) {
    switch (type) {
      case 'results':
        return Icons.assessment;
      case 'meeting':
        return Icons.event;
      case 'fees':
        return Icons.payment;
      case 'announcement':
        return Icons.campaign;
      default:
        return Icons.notifications;
    }
  }

  /// بناء محتوى الشاشة الرئيسي
  ///
  /// يعرض المحتوى المناسب حسب حالة التحميل والبيانات
  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: LoadingIndicator());
    }

    if (_hasError) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في تحميل البيانات',
              style: Theme.of(context).textTheme.titleLarge,
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => _loadDashboardData(),
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      key: _refreshKey,
      onRefresh: _refreshDashboard,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // قسم الإحصائيات السريعة
            _buildQuickStatsSection(),
            const SizedBox(height: 24),

            // قسم الأبناء
            _buildChildrenSection(),
            const SizedBox(height: 24),

            // قسم الإشعارات الحديثة
            _buildNotificationsSection(),
            const SizedBox(height: 24),

            // قسم الأحداث القادمة
            _buildUpcomingEventsSection(),
            const SizedBox(height: 24),

            // قسم آخر النتائج
            _buildLatestResultsSection(),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection() {
    return const Center(
      child: Text(
        'الإحصائيات السريعة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم الأبناء
  Widget _buildChildrenSection() {
    return const Center(
      child: Text(
        'قسم الأبناء\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم الإشعارات
  Widget _buildNotificationsSection() {
    return const Center(
      child: Text(
        'الإشعارات الحديثة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم الأحداث القادمة
  Widget _buildUpcomingEventsSection() {
    return const Center(
      child: Text(
        'الأحداث القادمة\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }

  /// بناء قسم آخر النتائج
  Widget _buildLatestResultsSection() {
    return const Center(
      child: Text(
        'آخر النتائج\n(قيد التطوير)',
        textAlign: TextAlign.center,
        style: TextStyle(fontSize: 16, color: Colors.grey),
      ),
    );
  }
}
