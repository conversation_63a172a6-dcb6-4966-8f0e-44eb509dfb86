
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/admin_screens/classes_management_screen.dart'; // For selectedClassIdProvider
import 'package:school_management_system/admin_screens/widgets/confirm_delete_class_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/class_dialogs.dart';

class ClassesList extends ConsumerWidget {
  final List<ClassModel> classes;

  const ClassesList({super.key, required this.classes});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedClassId = ref.watch(selectedClassIdProvider);

    return ListView.builder(
      itemCount: classes.length,
      itemBuilder: (context, index) {
        final classDoc = classes[index];
        return ListTile(
          title: Text(classDoc.name),
          selected: selectedClassId == classDoc.id,
          onTap: () => ref.read(selectedClassIdProvider.notifier).state = classDoc.id,
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              IconButton(
                icon: const Icon(Icons.edit),
                onPressed: () => showEditClassDialog(context, classDoc),
              ),
              IconButton(
                icon: const Icon(Icons.delete, color: Colors.red),
                onPressed: () => showConfirmDeleteClassDialog(context, ref, classDoc),
              ),
            ],
          ),
        );
      },
    );
  }
}
