import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/providers/student_providers.dart';
import '../widgets/loading_indicator.dart';
import '../widgets/error_message.dart';

class StudentGradesScreen extends ConsumerWidget {
  final String studentId;

  const StudentGradesScreen({Key? key, required this.studentId}) : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final gradesAsyncValue = ref.watch(studentGradesProvider(studentId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('الدرجات والتقييم'),
        centerTitle: true,
      ),
      body: gradesAsyncValue.when(
        loading: () => const LoadingIndicator(),
        error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
        data: (grades) {
          if (grades.isEmpty) {
            return const Center(
              child: Text(
                'لا توجد درجات مسجلة حتى الآن.',
                style: TextStyle(fontSize: 18, color: Colors.grey),
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: grades.length,
            itemBuilder: (context, index) {
              final grade = grades[index];
              final dateValue = grade['date'];
              final String formattedDate;

              if (dateValue is Timestamp) {
                final DateTime date = dateValue.toDate();
                formattedDate = "${date.year}/${date.month}/${date.day}";
              } else {
                formattedDate = 'تاريخ غير محدد';
              }

              return Card(
                elevation: 4.0,
                margin: const EdgeInsets.symmetric(vertical: 8.0),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12.0),
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 16.0),
                  leading: CircleAvatar(
                    backgroundColor: Theme.of(context).primaryColor,
                    child: Text(
                      grade['grade']?.toString() ?? 'N/A',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    grade['subject_name'] ?? 'مادة غير محددة',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: Text(
                    'نوع التقييم: ${grade['assessment_type'] ?? 'غير محدد'}\nتاريخ التسجيل: $formattedDate',
                    style: TextStyle(color: Colors.grey[600]),
                  ),
                  trailing: const Icon(Icons.assessment_outlined),
                ),
              );
            },
          );
        },
      ),
    );
  }
}
