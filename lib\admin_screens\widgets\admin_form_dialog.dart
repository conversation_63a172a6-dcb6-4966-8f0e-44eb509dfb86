import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';

/// ديالوج لإضافة أو تعديل بيانات مسؤول.
class AdminFormDialog extends ConsumerStatefulWidget {
  final UserModel? admin; // يكون null في حالة الإضافة

  const AdminFormDialog({super.key, this.admin});

  @override
  ConsumerState<AdminFormDialog> createState() => _AdminFormDialogState();
}

class _AdminFormDialogState extends ConsumerState<AdminFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;

  bool get _isEditMode => widget.admin != null;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.admin?.name ?? '');
    _emailController = TextEditingController(text: widget.admin?.email ?? '');
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      try {
        final firebaseService = ref.read(firebaseServiceProvider);
        if (_isEditMode) {
          // وضع التعديل
          await firebaseService.updateAdmin(
            widget.admin!.id,
            _nameController.text,
          );
          showSuccessSnackBar(context, 'تم تحديث بيانات المسؤول بنجاح.');
        } else {
          // وضع الإضافة
          await firebaseService.addAdmin(
            _nameController.text,
            _emailController.text,
            _passwordController.text,
          );
          showSuccessSnackBar(context, 'تمت إضافة المسؤول بنجاح.');
        }
        Navigator.of(context).pop(); // إغلاق الديالوج عند النجاح
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditMode ? 'تعديل بيانات المسؤول' : 'إضافة مسؤول جديد'),
      content: Form(
        key: _formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(labelText: 'الاسم الكامل'),
              validator: (value) =>
                  value!.isEmpty ? 'الرجاء إدخال الاسم' : null,
            ),
            if (!_isEditMode) ...[
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                validator: (value) => value!.isEmpty || !value.contains('@')
                    ? 'الرجاء إدخال بريد إلكتروني صالح'
                    : null,
              ),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(labelText: 'كلمة المرور'),
                obscureText: true,
                validator: (value) => value!.length < 6
                    ? 'كلمة المرور يجب أن تكون 6 أحرف على الأقل'
                    : null,
              ),
            ],
            if (_isEditMode)
              const Padding(
                padding: EdgeInsets.only(top: 8.0),
                child: Text(
                  'ملاحظة: تعديل البريد الإلكتروني وكلمة المرور يتطلب إجراءات أمان إضافية.',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _submit,
          child: Text(_isEditMode ? 'حفظ التعديلات' : 'إضافة'),
        ),
      ],
    );
  }
}
