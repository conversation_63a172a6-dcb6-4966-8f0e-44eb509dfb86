                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\baha\Baha\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=F:\baha\Baha\Flutter\New\school_management_system\build\app\intermediates\cxx\Debug\k1aw2o4v\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=F:\baha\Baha\Flutter\New\school_management_system\build\app\intermediates\cxx\Debug\k1aw2o4v\obj\x86
-DCMAKE_BUILD_TYPE=Debug
-BF:\baha\Baha\Flutter\New\school_management_system\android\app\.cxx\Debug\k1aw2o4v\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2