
/// موديل لتمثيل بيانات المسابقة.
class CompetitionModel {
  final String id; // المعرّف الفريد للمسابقة (من Firestore)
  final String title; // عنوان المسابقة
  final String description; // وصف المسابقة
  final DateTime? startDate; // تاريخ بدء المسابقة
  final DateTime? endDate; // تاريخ انتهاء المسابقة

  CompetitionModel({
    required this.id,
    required this.title,
    required this.description,
    this.startDate,
    this.endDate,
  });

  /// دالة لتحويل مستند Firestore إلى كائن `CompetitionModel`.
  factory CompetitionModel.fromMap(Map<String, dynamic> data, String documentId) {
    return CompetitionModel(
      id: documentId,
      title: data['title'] ?? '', // جلب العنوان
      description: data['description'] ?? '', // جلب الوصف
      // جلب التواريخ وتحويلها من Timestamp إلى DateTime
      startDate: data['startDate']?.toDate(),
      endDate: data['endDate']?.toDate(),
    );
  }

  /// دالة لتحويل كائن `CompetitionModel` إلى Map ليتم تخزينه في Firestore.
  Map<String, dynamic> toMap() {
    return {
      'title': title,
      'description': description,
      'startDate': startDate,
      'endDate': endDate,
    };
  }
}
