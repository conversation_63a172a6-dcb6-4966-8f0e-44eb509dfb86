<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>واجهة الجوال</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/flowbite@2.0.0/dist/flowbite.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="mobile_theme_1.css">
    <style>
        body {
            font-family: var(--font-sans);
            background: var(--background);
            color: var(--foreground);
        }
        .nav-item-active {
            border-right: 3px solid var(--primary);
        }
    </style>
</head>
<body class="flex flex-col min-h-screen">
    <!-- شريط الحالة -->
    <div class="px-4 py-2 text-sm flex justify-between">
        <span>٩:٣٠ ص</span>
        <div class="flex gap-2">
            <span>٩٠٪</span>
            <svg width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M17 4h3v16H4V4h3V2h2v2h6V2h2v2ZM7 18h10v-6H7v6Z"/></svg>
        </div>
    </div>

    <!-- الرأس -->
    <header class="px-4 py-3 flex items-center justify-between border-b border-muted">
        <button class="p-2 hover:bg-muted rounded-lg">
            <svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M3 18h18v-2H3v2Zm0-5h18v-2H3v2Zm0-7v2h18V6H3Z"/></svg>
        </button>
        <h1 class="text-xl font-semibold">التطبيق</h1>
        <div class="w-8"></div>
    </header>

    <!-- المحتوى الرئيسي -->
    <main class="flex-1 overflow-y-auto px-4 py-3">
        <div class="bg-muted rounded-xl p-4 mb-4">
            <h2 class="text-lg font-semibold mb-2">مرحبا بك!</h2>
            <p class="text-muted-foreground">اختر من الخيارات أدناه للبدء</p>
        </div>

        <!-- قائمة الخيارات -->
        <div class="space-y-2">
            <div class="bg-secondary p-3 rounded-lg flex items-center gap-3">
                <div class="bg-primary p-2 rounded-md">
                    <svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 2a10 10 0 0 1 10 10a10 10 0 0 1-10 10A10 10 0 0 1 2 12A10 10 0 0 1 12 2m0 2a8 8 0 0 0-8 8a8 8 0 0 0 8 8a8 8 0 0 0 8-8a8 8 0 0 0-8-8m-1 6h2v5h-2v-5m0 6h2v2h-2v-2"/></svg>
                </div>
                <span>الإعدادات</span>
                <svg class="mr-auto" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M8.59 16.58L13.17 12L8.59 7.41L10 6l6 6l-6 6l-1.41-1.42Z"/></svg>
            </div>

            <!-- تكرار العنصر مع تغيير الأيقونة والنص -->
            <div class="bg-muted p-3 rounded-lg flex items-center gap-3">
                <div class="bg-primary p-2 rounded-md">
                    <svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M12 4a4 4 0 0 1 4 4a4 4 0 0 1-4 4a4 4 0 0 1-4-4a4 4 0 0 1 4-4m0 10c4.42 0 8 1.79 8 4v2H4v-2c0-2.21 3.58-4 8-4Z"/></svg>
                </div>
                <span>الملف الشخصي</span>
                <svg class="mr-auto" width="20" height="20" viewBox="0 0 24 24"><path fill="currentColor" d="M8.59 16.58L13.17 12L8.59 7.41L10 6l6 6l-6 6l-1.41-1.42Z"/></svg>
            </div>
        </div>

        <!-- زر الإجراء الرئيسي -->
        <button class="w-full bg-primary text-primary-foreground py-3 rounded-xl font-semibold mt-6 hover:bg-primary/90 transition-colors">
            بدء الاستخدام
        </button>
    </main>

    <!-- التنقل السفلي -->
    <nav class="bg-muted border-t border-muted-foreground/10 py-2">
        <div class="flex justify-around">
            <a href="#" class="flex flex-col items-center text-sm px-2 py-1 rounded-lg nav-item-active">
                <svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M10 20v-6h4v6h5v-8h3L12 3L2 12h3v8h5Z"/></svg>
                <span>الرئيسية</span>
            </a>
            <!-- تكرار العناصر مع تغيير الأيقونات -->
            <a href="#" class="flex flex-col items-center text-sm px-2 py-1 rounded-lg text-muted-foreground">
                <svg width="24" height="24" viewBox="0 0 24 24"><path fill="currentColor" d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm0 16H5V5h14v14z"/></svg>
                <span>الإشعارات</span>
            </a>
        </div>
    </nav>
</body>
</html>