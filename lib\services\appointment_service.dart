import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/appointment_model.dart';
import 'package:school_management_system/models/conversation_model.dart'
    hide ParticipantInfo;

/// خدمة إدارة المواعيد والاجتماعات
///
/// تقدم هذه الخدمة جميع الوظائف المطلوبة لإدارة المواعيد في النظام المدرسي
/// بما في ذلك إنشاء وتعديل وحذف المواعيد، وإدارة المشاركين، والتذكيرات
///
/// الميزات الرئيسية:
/// - إنشاء مواعيد فردية وجماعية
/// - إدارة المواعيد المتكررة
/// - تأكيد وإلغاء المواعيد
/// - إرسال التذكيرات التلقائية
/// - تتبع حضور المشاركين
/// - إنشاء تقارير شاملة
/// - دعم المواعيد الإلكترونية
/// - إدارة الموافقات والصلاحيات
class AppointmentService {
  /// مرجع مجموعة المواعيد في Firestore
  final CollectionReference _appointmentsCollection = FirebaseFirestore.instance
      .collection('appointments');

  /// مرجع مجموعة المستخدمين للحصول على معلومات المشاركين
  final CollectionReference _usersCollection = FirebaseFirestore.instance
      .collection('users');

  /// مرجع مجموعة الطلاب للربط مع المواعيد
  final CollectionReference _studentsCollection = FirebaseFirestore.instance
      .collection('students');

  // ===================================================================
  // دوال إنشاء وإدارة المواعيد
  // ===================================================================

  /// إنشاء موعد جديد
  ///
  /// يقوم بإنشاء موعد جديد في النظام مع جميع التفاصيل المطلوبة
  /// ويرسل إشعارات للمشاركين المدعوين
  ///
  /// [title] عنوان الموعد
  /// [description] وصف تفصيلي للموعد
  /// [type] نوع الموعد (اجتماع، استشارة، إلخ)
  /// [category] فئة الموعد (أكاديمي، سلوكي، إلخ)
  /// [priority] أولوية الموعد
  /// [startTime] تاريخ ووقت بداية الموعد
  /// [endTime] تاريخ ووقت انتهاء الموعد
  /// [location] موقع الاجتماع
  /// [organizerId] معرف منظم الموعد
  /// [organizerName] اسم منظم الموعد
  /// [organizerRole] دور منظم الموعد
  /// [participantIds] قائمة معرفات المشاركين
  /// [studentId] معرف الطالب المرتبط (اختياري)
  /// [classId] معرف الصف المرتبط (اختياري)
  /// [meetingLink] رابط الاجتماع الإلكتروني (اختياري)
  /// [notes] ملاحظات إضافية (اختياري)
  /// [isRecurring] هل الموعد متكرر؟
  /// [recurrencePattern] نمط التكرار (إن وجد)
  /// [requiresApproval] هل يتطلب موافقة؟
  /// [approvalRequiredFrom] معرف من يجب أن يوافق
  ///
  /// يرجع معرف الموعد الجديد
  Future<String> createAppointment({
    required String title,
    required String description,
    required AppointmentType type,
    required AppointmentCategory category,
    required AppointmentPriority priority,
    required DateTime startTime,
    required DateTime endTime,
    required String location,
    required String organizerId,
    required String organizerName,
    required String organizerRole,
    required List<String> participantIds,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    String? meetingLink,
    String? meetingPassword,
    String? notes,
    bool isRecurring = false,
    RecurrencePattern? recurrencePattern,
    DateTime? recurrenceEndDate,
    bool requiresApproval = false,
    String? approvalRequiredFrom,
    List<String> tags = const [],
    bool isPrivate = false,
    bool allowGuestInvites = false,
    int? maxParticipants,
    List<ReminderSetting> reminders = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات المدخلة
      _validateAppointmentData(
        title: title,
        description: description,
        startTime: startTime,
        endTime: endTime,
        location: location,
        organizerId: organizerId,
        participantIds: participantIds,
      );

      // التحقق من توفر الوقت للمنظم والمشاركين
      await _checkTimeAvailability(
        startTime: startTime,
        endTime: endTime,
        participantIds: [organizerId, ...participantIds],
      );

      // الحصول على معلومات المشاركين
      final participants = await _getParticipantsInfo(participantIds);

      // إنشاء معرف فريد للموعد
      final appointmentRef = _appointmentsCollection.doc();
      final appointmentId = appointmentRef.id;

      // إنشاء نموذج الموعد
      final appointment = AppointmentModel(
        id: appointmentId,
        title: title,
        description: description,
        type: type,
        category: category,
        priority: priority,
        status:
            requiresApproval
                ? AppointmentStatus.pending
                : AppointmentStatus.confirmed,
        startTime: startTime,
        endTime: endTime,
        location: location,
        meetingLink: meetingLink,
        meetingPassword: meetingPassword,
        organizerId: organizerId,
        organizerName: organizerName,
        organizerRole: organizerRole,
        participantIds: participantIds,
        participants: participants,
        studentId: studentId,
        studentName: studentName,
        classId: classId,
        className: className,
        isRecurring: isRecurring,
        recurrencePattern: recurrencePattern,
        recurrenceEndDate: recurrenceEndDate,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        createdBy: organizerId,
        notes: notes,
        tags: tags,
        isPrivate: isPrivate,
        allowGuestInvites: allowGuestInvites,
        maxParticipants: maxParticipants,
        requiresApproval: requiresApproval,
        approvalRequiredFrom: approvalRequiredFrom,
        approvalStatus: requiresApproval ? ApprovalStatus.pending : null,
        reminders: reminders,
        metadata: metadata,
      );

      // حفظ الموعد في قاعدة البيانات
      await appointmentRef.set(appointment.toMap());

      // إنشاء المواعيد المتكررة إذا كان مطلوباً
      if (isRecurring && recurrencePattern != null) {
        await _createRecurringAppointments(
          appointment,
          recurrencePattern,
          recurrenceEndDate,
        );
      }

      // إرسال إشعارات للمشاركين
      await _sendAppointmentNotifications(
        appointment,
        NotificationType.created,
      );

      // إرسال طلب موافقة إذا كان مطلوباً
      if (requiresApproval && approvalRequiredFrom != null) {
        await _sendApprovalRequest(appointment, approvalRequiredFrom);
      }

      // جدولة التذكيرات
      await _scheduleReminders(appointment);

      return appointmentId;
    } catch (e) {
      throw Exception('فشل في إنشاء الموعد: $e');
    }
  }

  /// الحصول على قائمة المواعيد للمستخدم
  ///
  /// يرجع جميع المواعيد التي يشارك فيها المستخدم أو ينظمها
  /// مع إمكانية الفلترة حسب التاريخ والحالة والنوع
  ///
  /// [userId] معرف المستخدم
  /// [startDate] تاريخ البداية للفلترة (اختياري)
  /// [endDate] تاريخ النهاية للفلترة (اختياري)
  /// [status] حالة المواعيد للفلترة (اختياري)
  /// [type] نوع المواعيد للفلترة (اختياري)
  /// [limit] عدد المواعيد المطلوب إرجاعها (افتراضي 50)
  Stream<List<AppointmentModel>> getUserAppointments(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
    AppointmentStatus? status,
    AppointmentType? type,
    int limit = 50,
  }) {
    try {
      Query query = _appointmentsCollection
          .where('participantIds', arrayContains: userId)
          .orderBy('startTime', descending: false)
          .limit(limit);

      // تطبيق فلاتر إضافية
      if (startDate != null) {
        query = query.where('startTime', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('startTime', isLessThanOrEqualTo: endDate);
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }

      if (type != null) {
        query = query.where('type', isEqualTo: type.toString());
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => AppointmentModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      throw Exception('فشل في جلب المواعيد: $e');
    }
  }

  /// الحصول على تفاصيل موعد محدد
  ///
  /// [appointmentId] معرف الموعد
  /// يرجع تفاصيل الموعد أو null إذا لم يوجد
  Future<AppointmentModel?> getAppointment(String appointmentId) async {
    try {
      final doc = await _appointmentsCollection.doc(appointmentId).get();

      if (doc.exists) {
        return AppointmentModel.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل الموعد: $e');
    }
  }

  /// تحديث موعد موجود
  ///
  /// يسمح بتحديث تفاصيل الموعد مع إرسال إشعارات للمشاركين
  ///
  /// [appointmentId] معرف الموعد
  /// [updatedBy] معرف من قام بالتحديث
  /// [updates] الحقول المراد تحديثها
  Future<void> updateAppointment(
    String appointmentId,
    String updatedBy,
    Map<String, dynamic> updates,
  ) async {
    try {
      // التحقق من وجود الموعد
      final appointment = await getAppointment(appointmentId);
      if (appointment == null) {
        throw Exception('الموعد غير موجود');
      }

      // التحقق من صلاحية التعديل
      if (!appointment.canBeModified) {
        throw Exception('لا يمكن تعديل هذا الموعد');
      }

      // إضافة معلومات التحديث
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
      updates['updatedBy'] = updatedBy;

      // تحديث الموعد في قاعدة البيانات
      await _appointmentsCollection.doc(appointmentId).update(updates);

      // إرسال إشعارات للمشاركين
      final updatedAppointment = await getAppointment(appointmentId);
      if (updatedAppointment != null) {
        await _sendAppointmentNotifications(
          updatedAppointment,
          NotificationType.updated,
        );
      }
    } catch (e) {
      throw Exception('فشل في تحديث الموعد: $e');
    }
  }

  /// تأكيد الموعد
  ///
  /// يقوم بتأكيد الموعد وإرسال إشعارات للمشاركين
  ///
  /// [appointmentId] معرف الموعد
  /// [confirmedBy] معرف من قام بالتأكيد
  Future<void> confirmAppointment(
    String appointmentId,
    String confirmedBy,
  ) async {
    try {
      await _appointmentsCollection.doc(appointmentId).update({
        'status': AppointmentStatus.confirmed.toString(),
        'confirmedAt': Timestamp.fromDate(DateTime.now()),
        'confirmedBy': confirmedBy,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': confirmedBy,
      });

      // إرسال إشعارات التأكيد
      final appointment = await getAppointment(appointmentId);
      if (appointment != null) {
        await _sendAppointmentNotifications(
          appointment,
          NotificationType.confirmed,
        );
      }
    } catch (e) {
      throw Exception('فشل في تأكيد الموعد: $e');
    }
  }

  /// إلغاء الموعد
  ///
  /// يقوم بإلغاء الموعد مع تسجيل السبب وإرسال إشعارات
  ///
  /// [appointmentId] معرف الموعد
  /// [cancelledBy] معرف من قام بالإلغاء
  /// [reason] سبب الإلغاء
  Future<void> cancelAppointment(
    String appointmentId,
    String cancelledBy,
    String reason,
  ) async {
    try {
      await _appointmentsCollection.doc(appointmentId).update({
        'status': AppointmentStatus.cancelled.toString(),
        'cancellationReason': reason,
        'cancelledAt': Timestamp.fromDate(DateTime.now()),
        'cancelledBy': cancelledBy,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': cancelledBy,
      });

      // إرسال إشعارات الإلغاء
      final appointment = await getAppointment(appointmentId);
      if (appointment != null) {
        await _sendAppointmentNotifications(
          appointment,
          NotificationType.cancelled,
        );
      }
    } catch (e) {
      throw Exception('فشل في إلغاء الموعد: $e');
    }
  }

  /// تأجيل الموعد
  ///
  /// يقوم بتأجيل الموعد إلى وقت جديد
  ///
  /// [appointmentId] معرف الموعد
  /// [newStartTime] الوقت الجديد للبداية
  /// [newEndTime] الوقت الجديد للنهاية
  /// [postponedBy] معرف من قام بالتأجيل
  /// [reason] سبب التأجيل
  Future<void> postponeAppointment(
    String appointmentId,
    DateTime newStartTime,
    DateTime newEndTime,
    String postponedBy,
    String reason,
  ) async {
    try {
      // التحقق من توفر الوقت الجديد
      final appointment = await getAppointment(appointmentId);
      if (appointment == null) {
        throw Exception('الموعد غير موجود');
      }

      await _checkTimeAvailability(
        startTime: newStartTime,
        endTime: newEndTime,
        participantIds: [
          appointment.organizerId,
          ...appointment.participantIds,
        ],
        excludeAppointmentId: appointmentId,
      );

      await _appointmentsCollection.doc(appointmentId).update({
        'startTime': Timestamp.fromDate(newStartTime),
        'endTime': Timestamp.fromDate(newEndTime),
        'status': AppointmentStatus.postponed.toString(),
        'cancellationReason': reason,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': postponedBy,
      });

      // إرسال إشعارات التأجيل
      final updatedAppointment = await getAppointment(appointmentId);
      if (updatedAppointment != null) {
        await _sendAppointmentNotifications(
          updatedAppointment,
          NotificationType.postponed,
        );
      }
    } catch (e) {
      throw Exception('فشل في تأجيل الموعد: $e');
    }
  }

  /// إضافة مشارك جديد للموعد
  ///
  /// [appointmentId] معرف الموعد
  /// [participantId] معرف المشارك الجديد
  /// [addedBy] معرف من قام بالإضافة
  Future<void> addParticipant(
    String appointmentId,
    String participantId,
    String addedBy,
  ) async {
    try {
      final appointment = await getAppointment(appointmentId);
      if (appointment == null) {
        throw Exception('الموعد غير موجود');
      }

      // التحقق من عدم وجود المشارك مسبقاً
      if (appointment.participantIds.contains(participantId)) {
        throw Exception('المشارك موجود بالفعل في الموعد');
      }

      // التحقق من الحد الأقصى للمشاركين
      if (appointment.maxParticipants != null &&
          appointment.participantIds.length >= appointment.maxParticipants!) {
        throw Exception('تم الوصول للحد الأقصى من المشاركين');
      }

      // الحصول على معلومات المشارك الجديد
      final participantInfo = await _getParticipantInfo(participantId);

      // تحديث قائمة المشاركين
      final updatedParticipantIds = [
        ...appointment.participantIds,
        participantId,
      ];
      final updatedParticipants = Map<String, ParticipantInfo>.from(
        appointment.participants,
      );
      updatedParticipants[participantId] = participantInfo;

      await _appointmentsCollection.doc(appointmentId).update({
        'participantIds': updatedParticipantIds,
        'participants': updatedParticipants.map(
          (key, value) => MapEntry(key, value.toMap()),
        ),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': addedBy,
      });

      // إرسال إشعار للمشارك الجديد
      await _sendParticipantAddedNotification(appointment, participantInfo);
    } catch (e) {
      throw Exception('فشل في إضافة المشارك: $e');
    }
  }

  // ===================================================================
  // الدوال المساعدة الخاصة
  // ===================================================================

  /// التحقق من صحة بيانات الموعد
  void _validateAppointmentData({
    required String title,
    required String description,
    required DateTime startTime,
    required DateTime endTime,
    required String location,
    required String organizerId,
    required List<String> participantIds,
  }) {
    if (title.trim().isEmpty) {
      throw Exception('عنوان الموعد مطلوب');
    }

    if (description.trim().isEmpty) {
      throw Exception('وصف الموعد مطلوب');
    }

    if (location.trim().isEmpty) {
      throw Exception('موقع الموعد مطلوب');
    }

    if (organizerId.trim().isEmpty) {
      throw Exception('منظم الموعد مطلوب');
    }

    if (participantIds.isEmpty) {
      throw Exception('يجب دعوة مشارك واحد على الأقل');
    }

    if (startTime.isBefore(DateTime.now())) {
      throw Exception('لا يمكن إنشاء موعد في الماضي');
    }

    if (endTime.isBefore(startTime)) {
      throw Exception('وقت انتهاء الموعد يجب أن يكون بعد وقت البداية');
    }

    if (endTime.difference(startTime).inMinutes < 15) {
      throw Exception('مدة الموعد يجب أن تكون 15 دقيقة على الأقل');
    }
  }

  /// التحقق من توفر الوقت للمشاركين
  Future<void> _checkTimeAvailability({
    required DateTime startTime,
    required DateTime endTime,
    required List<String> participantIds,
    String? excludeAppointmentId,
  }) async {
    try {
      for (String participantId in participantIds) {
        Query query = _appointmentsCollection
            .where('participantIds', arrayContains: participantId)
            .where(
              'status',
              whereIn: [
                AppointmentStatus.confirmed.toString(),
                AppointmentStatus.inProgress.toString(),
              ],
            );

        final snapshot = await query.get();

        for (var doc in snapshot.docs) {
          // تجاهل الموعد المستثنى (في حالة التحديث)
          if (excludeAppointmentId != null && doc.id == excludeAppointmentId) {
            continue;
          }

          final appointment = AppointmentModel.fromFirestore(doc);

          // التحقق من تداخل الأوقات
          if (_isTimeOverlapping(
            startTime,
            endTime,
            appointment.startTime,
            appointment.endTime,
          )) {
            throw Exception(
              'يوجد تعارض في الوقت مع موعد آخر للمشارك: ${appointment.participants[participantId]?.name ?? participantId}',
            );
          }
        }
      }
    } catch (e) {
      if (e.toString().contains('يوجد تعارض')) {
        rethrow;
      }
      throw Exception('فشل في التحقق من توفر الوقت: $e');
    }
  }

  /// التحقق من تداخل الأوقات
  bool _isTimeOverlapping(
    DateTime start1,
    DateTime end1,
    DateTime start2,
    DateTime end2,
  ) {
    return start1.isBefore(end2) && end1.isAfter(start2);
  }

  /// الحصول على معلومات المشاركين
  Future<Map<String, ParticipantInfo>> _getParticipantsInfo(
    List<String> participantIds,
  ) async {
    final participants = <String, ParticipantInfo>{};

    for (String participantId in participantIds) {
      final participantInfo = await _getParticipantInfo(participantId);
      participants[participantId] = participantInfo;
    }

    return participants;
  }

  /// الحصول على معلومات مشارك واحد
  Future<ParticipantInfo> _getParticipantInfo(String participantId) async {
    try {
      final userDoc = await _usersCollection.doc(participantId).get();

      if (userDoc.exists) {
        final userData = userDoc.data() as Map<String, dynamic>;
        return ParticipantInfo(
          id: participantId,
          name: userData['name'] ?? 'مستخدم غير معروف',
          role: _parseParticipantRole(userData['role']),
          department: userData['department'],
          avatarUrl: userData['avatarUrl'],
        );
      } else {
        // في حالة عدم وجود المستخدم، إنشاء معلومات افتراضية
        return ParticipantInfo(
          id: participantId,
          name: 'مستخدم غير معروف',
          role: ParticipantRole.parent,
        );
      }
    } catch (e) {
      throw Exception('فشل في جلب معلومات المشارك: $e');
    }
  }

  /// تحويل نص الدور إلى تعداد
  ParticipantRole _parseParticipantRole(String? role) {
    switch (role?.toLowerCase()) {
      case 'teacher':
        return ParticipantRole.teacher;
      case 'admin':
        return ParticipantRole.admin;
      case 'principal':
        return ParticipantRole.principal;
      case 'counselor':
        return ParticipantRole.counselor;
      case 'nurse':
        return ParticipantRole.nurse;
      case 'staff':
        return ParticipantRole.staff;
      default:
        return ParticipantRole.parent;
    }
  }

  /// إنشاء المواعيد المتكررة
  Future<void> _createRecurringAppointments(
    AppointmentModel baseAppointment,
    RecurrencePattern pattern,
    DateTime? endDate,
  ) async {
    // TODO: تطبيق منطق إنشاء المواعيد المتكررة
    // هذه دالة معقدة تحتاج تطبيق مفصل حسب نوع التكرار
  }

  /// إرسال إشعارات الموعد
  Future<void> _sendAppointmentNotifications(
    AppointmentModel appointment,
    NotificationType type,
  ) async {
    // TODO: تطبيق إرسال الإشعارات
    // يمكن دمجها مع نظام الإشعارات الموجود
  }

  /// إرسال طلب موافقة
  Future<void> _sendApprovalRequest(
    AppointmentModel appointment,
    String approverUserId,
  ) async {
    // TODO: تطبيق إرسال طلب الموافقة
  }

  /// جدولة التذكيرات
  Future<void> _scheduleReminders(AppointmentModel appointment) async {
    // TODO: تطبيق جدولة التذكيرات
  }

  /// إرسال إشعار إضافة مشارك
  Future<void> _sendParticipantAddedNotification(
    AppointmentModel appointment,
    ParticipantInfo participant,
  ) async {
    // TODO: تطبيق إرسال إشعار إضافة المشارك
  }
}

/// أنواع الإشعارات
enum NotificationType {
  created, // تم إنشاء الموعد
  updated, // تم تحديث الموعد
  confirmed, // تم تأكيد الموعد
  cancelled, // تم إلغاء الموعد
  postponed, // تم تأجيل الموعد
  reminder, // تذكير بالموعد
}
