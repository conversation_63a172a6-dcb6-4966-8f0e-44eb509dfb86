import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة إعدادات نظام الامتحانات
///
/// هذه الشاشة تسمح للإدارة بتخصيص وإعداد نظام الامتحانات
/// وتشمل إعدادات أنواع الامتحانات والدرجات والمرافق
///
/// الوظائف الرئيسية:
/// - إعداد أنواع الامتحانات وأوزانها
/// - تحديد درجات النجاح لكل مادة
/// - إدارة القاعات والمرافق
/// - تحديد صلاحيات المعلمين
/// - إعدادات النسخ الاحتياطي والأمان
class ExamSettingsScreen extends ConsumerStatefulWidget {
  const ExamSettingsScreen({super.key});

  @override
  ConsumerState<ExamSettingsScreen> createState() => _ExamSettingsScreenState();
}

class _ExamSettingsScreenState extends ConsumerState<ExamSettingsScreen>
    with TickerProviderStateMixin {
  // متحكم التبويبات
  late TabController _tabController;

  // متحكمات النماذج
  final _formKey = GlobalKey<FormState>();

  // إعدادات أنواع الامتحانات
  Map<ExamType, double> _examTypeWeights = {
    ExamType.monthly: 0.2,
    ExamType.midterm: 0.3,
    ExamType.finalExam: 0.5,
    ExamType.makeup: 0.0,
  };

  // إعدادات درجات النجاح
  Map<String, int> _passingGrades = {
    'الرياضيات': 50,
    'العربية': 50,
    'الإنجليزية': 50,
    'العلوم': 50,
    'الاجتماعيات': 50,
    'التربية الإسلامية': 50,
  };

  // إعدادات عامة
  int _defaultExamDuration = 120; // بالدقائق
  bool _allowLateSubmission = false;
  bool _enableAutoBackup = true;
  bool _requireApprovalForGrades = true;
  int _gradeEntryDeadlineDays = 3;

  @override
  void initState() {
    super.initState();
    // إنشاء متحكم التبويبات مع 4 تبويبات
    _tabController = TabController(length: 4, vsync: this);

    // تحميل الإعدادات المحفوظة
    _loadSettings();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // شريط التطبيق مع التبويبات
      appBar: AppBar(
        title: const Text(
          'إعدادات نظام الامتحانات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.deepPurple[800],
        elevation: 2,

        // التبويبات السفلية
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          isScrollable: true,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 13,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.category, size: 20), text: 'أنواع الامتحانات'),
            Tab(icon: Icon(Icons.grade, size: 20), text: 'درجات النجاح'),
            Tab(icon: Icon(Icons.room, size: 20), text: 'القاعات والمرافق'),
            Tab(icon: Icon(Icons.settings, size: 20), text: 'إعدادات عامة'),
          ],
        ),

        // أزرار الإجراءات
        actions: [
          // زر الحفظ
          IconButton(
            icon: const Icon(Icons.save, color: Colors.white),
            onPressed: () => _saveSettings(),
            tooltip: 'حفظ الإعدادات',
          ),
          // زر إعادة التعيين
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () => _resetToDefaults(),
            tooltip: 'إعادة تعيين للافتراضي',
          ),
        ],
      ),

      // محتوى التبويبات
      body: TabBarView(
        controller: _tabController,
        children: [
          // تبويب أنواع الامتحانات
          _buildExamTypesTab(),

          // تبويب درجات النجاح
          _buildPassingGradesTab(),

          // تبويب القاعات والمرافق
          _buildFacilitiesTab(),

          // تبويب الإعدادات العامة
          _buildGeneralSettingsTab(),
        ],
      ),
    );
  }

  /// بناء تبويب أنواع الامتحانات
  ///
  /// يسمح بتحديد أوزان كل نوع امتحان في المعدل النهائي
  Widget _buildExamTypesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          const Text(
            'إعدادات أنواع الامتحانات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد الوزن النسبي لكل نوع امتحان في حساب المعدل النهائي',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // بطاقات أنواع الامتحانات
          ...ExamType.values.map((examType) => _buildExamTypeCard(examType)),

          const SizedBox(height: 24),

          // معلومات إضافية
          _buildWeightSummaryCard(),
        ],
      ),
    );
  }

  /// بناء بطاقة نوع امتحان واحد
  ///
  /// تعرض اسم النوع مع منزلق لتحديد الوزن
  Widget _buildExamTypeCard(ExamType examType) {
    final weight = _examTypeWeights[examType] ?? 0.0;
    final percentage = (weight * 100).round();

    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                // أيقونة نوع الامتحان
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getExamTypeColor(examType).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getExamTypeIcon(examType),
                    color: _getExamTypeColor(examType),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),

                // اسم نوع الامتحان
                Expanded(
                  child: Text(
                    examType.arabicName,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),

                // عرض النسبة المئوية
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getExamTypeColor(examType),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '$percentage%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // منزلق تحديد الوزن
            Row(
              children: [
                const Text(
                  '0%',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
                Expanded(
                  child: Slider(
                    value: weight,
                    min: 0.0,
                    max: 1.0,
                    divisions: 20,
                    activeColor: _getExamTypeColor(examType),
                    onChanged: (value) {
                      setState(() {
                        _examTypeWeights[examType] = value;
                      });
                    },
                  ),
                ),
                const Text(
                  '100%',
                  style: TextStyle(fontSize: 12, color: Colors.grey),
                ),
              ],
            ),

            // وصف نوع الامتحان
            Text(
              _getExamTypeDescription(examType),
              style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة ملخص الأوزان
  ///
  /// تعرض مجموع الأوزان والتحقق من صحتها
  Widget _buildWeightSummaryCard() {
    final totalWeight = _examTypeWeights.values.fold(
      0.0,
      (sum, weight) => sum + weight,
    );
    final isValid =
        (totalWeight - 1.0).abs() < 0.01; // تسامح صغير للأخطاء العائمة

    return CustomCard(
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: isValid ? Colors.green[50] : Colors.red[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isValid ? Colors.green[200]! : Colors.red[200]!,
          ),
        ),
        child: Column(
          children: [
            // رأس الملخص
            Row(
              children: [
                Icon(
                  isValid ? Icons.check_circle : Icons.warning,
                  color: isValid ? Colors.green[600] : Colors.red[600],
                ),
                const SizedBox(width: 8),
                Text(
                  'ملخص الأوزان',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: isValid ? Colors.green[700] : Colors.red[700],
                  ),
                ),
                const Spacer(),
                Text(
                  '${(totalWeight * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: isValid ? Colors.green[700] : Colors.red[700],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),

            // رسالة التحقق
            Text(
              isValid
                  ? 'الأوزان صحيحة ومجموعها 100%'
                  : 'تحذير: مجموع الأوزان يجب أن يساوي 100%',
              style: TextStyle(
                fontSize: 12,
                color: isValid ? Colors.green[600] : Colors.red[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب درجات النجاح
  ///
  /// يسمح بتحديد درجة النجاح لكل مادة
  Widget _buildPassingGradesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          const Text(
            'درجات النجاح للمواد',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'حدد الحد الأدنى للنجاح في كل مادة دراسية',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // قائمة المواد ودرجات النجاح
          ..._passingGrades.entries.map(
            (entry) => _buildSubjectGradeCard(entry.key, entry.value),
          ),

          const SizedBox(height: 16),

          // زر إضافة مادة جديدة
          SizedBox(
            width: double.infinity,
            child: OutlinedButton.icon(
              onPressed: () => _showAddSubjectDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة مادة جديدة'),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة مادة ودرجة النجاح
  Widget _buildSubjectGradeCard(String subject, int passingGrade) {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Row(
          children: [
            // أيقونة المادة
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.blue[100],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(Icons.book, color: Colors.blue[600], size: 20),
            ),
            const SizedBox(width: 12),

            // اسم المادة
            Expanded(
              child: Text(
                subject,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),

            // حقل إدخال درجة النجاح
            SizedBox(
              width: 80,
              child: TextFormField(
                initialValue: passingGrade.toString(),
                keyboardType: TextInputType.number,
                textAlign: TextAlign.center,
                decoration: const InputDecoration(
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(vertical: 8),
                  suffix: Text('%'),
                ),
                onChanged: (value) {
                  final grade = int.tryParse(value);
                  if (grade != null && grade >= 0 && grade <= 100) {
                    setState(() {
                      _passingGrades[subject] = grade;
                    });
                  }
                },
              ),
            ),
            const SizedBox(width: 8),

            // زر حذف المادة
            IconButton(
              onPressed: () => _removeSubject(subject),
              icon: Icon(Icons.delete_outline, color: Colors.red[400]),
              tooltip: 'حذف المادة',
            ),
          ],
        ),
      ),
    );
  }

  /// بناء تبويب القاعات والمرافق
  Widget _buildFacilitiesTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          const Text(
            'إدارة القاعات والمرافق',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة قاعات الامتحانات والمعدات المطلوبة',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 24),

          // بطاقة إحصائيات القاعات
          _buildFacilitiesStatsCard(),

          const SizedBox(height: 16),

          // قائمة القاعات
          _buildRoomsList(),

          const SizedBox(height: 16),

          // زر إضافة قاعة جديدة
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _showAddRoomDialog(),
              icon: const Icon(Icons.add),
              label: const Text('إضافة قاعة جديدة'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green[600],
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة إحصائيات القاعات
  Widget _buildFacilitiesStatsCard() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // عنوان الإحصائيات
            const Text(
              'إحصائيات القاعات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // شبكة الإحصائيات
            Row(
              children: [
                // إجمالي القاعات
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.room,
                    label: 'إجمالي القاعات',
                    value: '12',
                    color: Colors.blue,
                  ),
                ),

                // القاعات المتاحة
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.check_circle,
                    label: 'متاحة',
                    value: '10',
                    color: Colors.green,
                  ),
                ),

                // القاعات قيد الصيانة
                Expanded(
                  child: _buildStatItem(
                    icon: Icons.build,
                    label: 'قيد الصيانة',
                    value: '2',
                    color: Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إحصائي واحد
  Widget _buildStatItem({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// بناء قائمة القاعات
  Widget _buildRoomsList() {
    // قائمة مؤقتة للقاعات (يجب جلبها من قاعدة البيانات)
    final rooms = [
      {'name': 'قاعة A1', 'capacity': 30, 'status': 'متاحة'},
      {'name': 'قاعة A2', 'capacity': 25, 'status': 'متاحة'},
      {'name': 'قاعة B1', 'capacity': 35, 'status': 'قيد الصيانة'},
      {'name': 'قاعة B2', 'capacity': 28, 'status': 'متاحة'},
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'قائمة القاعات',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),

        ...rooms.map((room) => _buildRoomCard(room)),
      ],
    );
  }

  /// بناء بطاقة قاعة واحدة
  Widget _buildRoomCard(Map<String, dynamic> room) {
    final isAvailable = room['status'] == 'متاحة';

    return CustomCard(
      child: ListTile(
        // أيقونة القاعة
        leading: CircleAvatar(
          backgroundColor: isAvailable ? Colors.green[100] : Colors.orange[100],
          child: Icon(
            Icons.room,
            color: isAvailable ? Colors.green[600] : Colors.orange[600],
          ),
        ),

        // اسم القاعة
        title: Text(
          room['name'],
          style: const TextStyle(fontWeight: FontWeight.w600),
        ),

        // السعة والحالة
        subtitle: Text(
          'السعة: ${room['capacity']} طالب • ${room['status']}',
          style: TextStyle(color: Colors.grey[600], fontSize: 12),
        ),

        // أزرار الإجراءات
        trailing: PopupMenuButton(
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'edit',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 16),
                      SizedBox(width: 8),
                      Text('تعديل'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'maintenance',
                  child: Row(
                    children: [
                      Icon(Icons.build, size: 16),
                      SizedBox(width: 8),
                      Text('صيانة'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete, size: 16, color: Colors.red),
                      SizedBox(width: 8),
                      Text('حذف', style: TextStyle(color: Colors.red)),
                    ],
                  ),
                ),
              ],
          onSelected:
              (value) => _handleRoomAction(room['name'], value.toString()),
        ),
      ),
    );
  }

  /// بناء تبويب الإعدادات العامة
  Widget _buildGeneralSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          const Text(
            'الإعدادات العامة',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 24),

          // إعدادات الامتحانات
          _buildGeneralSettingsCard(),

          const SizedBox(height: 16),

          // إعدادات الدرجات
          _buildGradeSettingsCard(),

          const SizedBox(height: 16),

          // إعدادات النسخ الاحتياطي
          _buildBackupSettingsCard(),
        ],
      ),
    );
  }

  /// بناء بطاقة الإعدادات العامة
  Widget _buildGeneralSettingsCard() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الامتحانات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // مدة الامتحان الافتراضية
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'مدة الامتحان الافتراضية (بالدقائق)',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
                SizedBox(
                  width: 80,
                  child: TextFormField(
                    initialValue: _defaultExamDuration.toString(),
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                    ),
                    onChanged: (value) {
                      final duration = int.tryParse(value);
                      if (duration != null && duration > 0) {
                        setState(() {
                          _defaultExamDuration = duration;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // السماح بالتسليم المتأخر
            SwitchListTile(
              title: const Text('السماح بالتسليم المتأخر'),
              subtitle: const Text('السماح للطلاب بدخول الامتحان بعد البداية'),
              value: _allowLateSubmission,
              onChanged: (value) {
                setState(() {
                  _allowLateSubmission = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إعدادات الدرجات
  Widget _buildGradeSettingsCard() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الدرجات',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // مطالبة بالموافقة على الدرجات
            SwitchListTile(
              title: const Text('مطالبة بالموافقة على الدرجات'),
              subtitle: const Text('تتطلب موافقة الإدارة قبل نشر الدرجات'),
              value: _requireApprovalForGrades,
              onChanged: (value) {
                setState(() {
                  _requireApprovalForGrades = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // مهلة إدخال الدرجات
            Row(
              children: [
                const Expanded(
                  child: Text(
                    'مهلة إدخال الدرجات (بالأيام)',
                    style: TextStyle(fontSize: 14),
                  ),
                ),
                SizedBox(
                  width: 80,
                  child: TextFormField(
                    initialValue: _gradeEntryDeadlineDays.toString(),
                    keyboardType: TextInputType.number,
                    textAlign: TextAlign.center,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                    ),
                    onChanged: (value) {
                      final days = int.tryParse(value);
                      if (days != null && days > 0) {
                        setState(() {
                          _gradeEntryDeadlineDays = days;
                        });
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إعدادات النسخ الاحتياطي
  Widget _buildBackupSettingsCard() {
    return CustomCard(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات النسخ الاحتياطي',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // تفعيل النسخ الاحتياطي التلقائي
            SwitchListTile(
              title: const Text('النسخ الاحتياطي التلقائي'),
              subtitle: const Text('إنشاء نسخة احتياطية يومية من البيانات'),
              value: _enableAutoBackup,
              onChanged: (value) {
                setState(() {
                  _enableAutoBackup = value;
                });
              },
            ),
            const SizedBox(height: 16),

            // أزرار النسخ الاحتياطي اليدوي
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _createBackup(),
                    icon: const Icon(Icons.backup),
                    label: const Text('إنشاء نسخة احتياطية'),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: () => _restoreBackup(),
                    icon: const Icon(Icons.restore),
                    label: const Text('استعادة نسخة احتياطية'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // ===================================================================
  // دوال المساعدة والأدوات
  // ===================================================================

  /// الحصول على لون نوع الامتحان
  Color _getExamTypeColor(ExamType examType) {
    switch (examType) {
      case ExamType.monthly:
        return Colors.blue;
      case ExamType.midterm:
        return Colors.orange;
      case ExamType.finalExam:
        return Colors.red;
      case ExamType.makeup:
        return Colors.grey;
    }
  }

  /// الحصول على أيقونة نوع الامتحان
  IconData _getExamTypeIcon(ExamType examType) {
    switch (examType) {
      case ExamType.monthly:
        return Icons.calendar_month;
      case ExamType.midterm:
        return Icons.schedule;
      case ExamType.finalExam:
        return Icons.school;
      case ExamType.makeup:
        return Icons.refresh;
    }
  }

  /// الحصول على وصف نوع الامتحان
  String _getExamTypeDescription(ExamType examType) {
    switch (examType) {
      case ExamType.monthly:
        return 'امتحانات شهرية للتقييم المستمر';
      case ExamType.midterm:
        return 'امتحانات نصف فصلية';
      case ExamType.finalExam:
        return 'امتحانات نهائية للفصل الدراسي';
      case ExamType.makeup:
        return 'امتحانات إعادة للطلاب الغائبين';
    }
  }

  // ===================================================================
  // دوال الإجراءات
  // ===================================================================

  /// تحميل الإعدادات المحفوظة
  Future<void> _loadSettings() async {
    // TODO: تحميل الإعدادات من قاعدة البيانات
    // هذه قيم افتراضية مؤقتة
  }

  /// حفظ الإعدادات
  Future<void> _saveSettings() async {
    try {
      // TODO: حفظ الإعدادات في قاعدة البيانات

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم حفظ الإعدادات بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في حفظ الإعدادات: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  /// إعادة تعيين للإعدادات الافتراضية
  void _resetToDefaults() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إعادة تعيين الإعدادات'),
            content: const Text(
              'هل أنت متأكد من إعادة تعيين جميع الإعدادات للقيم الافتراضية؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _examTypeWeights = {
                      ExamType.monthly: 0.2,
                      ExamType.midterm: 0.3,
                      ExamType.finalExam: 0.5,
                      ExamType.makeup: 0.0,
                    };
                    _defaultExamDuration = 120;
                    _allowLateSubmission = false;
                    _enableAutoBackup = true;
                    _requireApprovalForGrades = true;
                    _gradeEntryDeadlineDays = 3;
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('تم إعادة تعيين الإعدادات'),
                      backgroundColor: Colors.blue,
                    ),
                  );
                },
                child: const Text('إعادة تعيين'),
              ),
            ],
          ),
    );
  }

  /// إضافة مادة جديدة
  void _showAddSubjectDialog() {
    final controller = TextEditingController();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة مادة جديدة'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(
                labelText: 'اسم المادة',
                hintText: 'مثال: الفيزياء',
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  final subject = controller.text.trim();
                  if (subject.isNotEmpty &&
                      !_passingGrades.containsKey(subject)) {
                    setState(() {
                      _passingGrades[subject] = 50;
                    });
                    Navigator.pop(context);
                  }
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
    );
  }

  /// حذف مادة
  void _removeSubject(String subject) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف المادة'),
            content: Text('هل أنت متأكد من حذف مادة "$subject"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _passingGrades.remove(subject);
                  });
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  /// إضافة قاعة جديدة
  void _showAddRoomDialog() {
    // TODO: تطبيق حوار إضافة قاعة
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق إضافة القاعات قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// التعامل مع إجراءات القاعة
  void _handleRoomAction(String roomName, String action) {
    // TODO: تطبيق إجراءات القاعة
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم تنفيذ $action على $roomName'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  void _createBackup() {
    // TODO: تطبيق إنشاء النسخة الاحتياطية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('جاري إنشاء النسخة الاحتياطية...'),
        backgroundColor: Colors.blue,
      ),
    );
  }

  /// استعادة نسخة احتياطية
  void _restoreBackup() {
    // TODO: تطبيق استعادة النسخة الاحتياطية
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('سيتم تطبيق استعادة النسخة الاحتياطية قريباً'),
        backgroundColor: Colors.blue,
      ),
    );
  }
}
