import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/assignment_model.dart';


/// خدمة متخصصة في التعامل مع كل ما يخص الواجبات في Firestore.
class AssignmentService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب قائمة الواجبات الخاصة بطالب معين.
  ///
  /// تستمع هذه الدالة للتغيرات الفورية في قاعدة البيانات.
  Stream<List<AssignmentModel>> getStudentAssignments(String studentId) {
    // TODO: يجب تحسين هذا الاستعلام ليجلب الواجبات الخاصة بصف الطالب فقط
    // بدلاً من جلب كل الواجبات.
    // يتطلب هذا تعديل بنية البيانات في Firestore.
    return _firestore
        .collection('assignments')
        .orderBy('dueDate', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) => AssignmentModel.fromFirestore(doc)).toList();
    });
  }

  /// رفع ملف حل الواجب.
  Future<void> submitAssignment(String assignmentId, String studentId, String fileUrl) async {
    // TODO: منطق رفع الملفات الفعلي يجب أن يكون هنا.
    // هذا مجرد مثال لكيفية تحديث حالة التسليم.
    await _firestore
        .collection('assignments')
        .doc(assignmentId)
        .collection('submissions')
        .doc(studentId)
        .set({
      'submittedAt': FieldValue.serverTimestamp(),
      'fileUrl': fileUrl,
      'status': 'تم التسليم'
    });
  }

  /// تحديد حالة الواجب (مطلوب، متأخر، تم التسليم).
  /// هذا مجرد مثال، يجب تحسينه ليعكس البيانات الحقيقية.
  Map<String, dynamic> getAssignmentStatus(Map<String, dynamic> assignmentData) {
    final bool isSubmitted = false; // قيمة وهمية، يجب جلبها من submissions
    final DateTime dueDate = (assignmentData['dueDate'] as Timestamp).toDate();
    final DateTime now = DateTime.now();

    if (isSubmitted) {
      return {'text': 'تم التسليم', 'color': 'Colors.green.shade100'};
    }
    if (now.isAfter(dueDate)) {
      return {'text': 'متأخر', 'color': 'Colors.red.shade100'};
    }
    return {'text': 'مطلوب', 'color': 'Colors.yellow.shade100'};
  }
}
