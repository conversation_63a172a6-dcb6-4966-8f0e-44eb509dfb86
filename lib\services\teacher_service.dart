import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/teacher_model.dart';

/// خدمة متخصصة في التعامل مع كل ما يخص المعلمين في Firestore.
class TeacherService {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// جلب قائمة المعلمين مع الاستماع للتغيرات الفورية.
  Stream<List<TeacherModel>> getTeachers() {
    return _firestore
        .collection('users')
        .where('role', isEqualTo: 'teacher')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) => TeacherModel.fromFirestore(doc)).toList();
    });
  }
}
