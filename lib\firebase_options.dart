// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAOtyESgVNucz85xZSEWPcr3e9s4b66WJw',
    appId: '1:361093905515:web:13d875ef9641f4991a052e',
    messagingSenderId: '361093905515',
    projectId: 'school-management-system-412fe',
    authDomain: 'school-management-system-412fe.firebaseapp.com',
    storageBucket: 'school-management-system-412fe.firebasestorage.app',
    measurementId: 'G-G83CHCF2K3',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBHVqtOvTIeKIcSdNXDcsonC3ewNJ1SQ_g',
    appId: '1:361093905515:android:494d6b9902aa9ded1a052e',
    messagingSenderId: '361093905515',
    projectId: 'school-management-system-412fe',
    storageBucket: 'school-management-system-412fe.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDX_2xDJ3JxOZhf5MPUz3gI5WyJpMbkv5w',
    appId: '1:361093905515:ios:cc0cd9c81e1486661a052e',
    messagingSenderId: '361093905515',
    projectId: 'school-management-system-412fe',
    storageBucket: 'school-management-system-412fe.firebasestorage.app',
    iosBundleId: 'com.example.schoolManagementSystem',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyDX_2xDJ3JxOZhf5MPUz3gI5WyJpMbkv5w',
    appId: '1:361093905515:ios:cc0cd9c81e1486661a052e',
    messagingSenderId: '361093905515',
    projectId: 'school-management-system-412fe',
    storageBucket: 'school-management-system-412fe.firebasestorage.app',
    iosBundleId: 'com.example.schoolManagementSystem',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyAOtyESgVNucz85xZSEWPcr3e9s4b66WJw',
    appId: '1:361093905515:web:665952879902ef541a052e',
    messagingSenderId: '361093905515',
    projectId: 'school-management-system-412fe',
    authDomain: 'school-management-system-412fe.firebaseapp.com',
    storageBucket: 'school-management-system-412fe.firebasestorage.app',
    measurementId: 'G-25DV1DGZFV',
  );

}