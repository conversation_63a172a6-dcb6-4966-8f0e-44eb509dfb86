import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/behavior_evaluation_model.dart';

/// خدمة تقييم السلوك المتقدمة
///
/// تقدم هذه الخدمة جميع الوظائف المطلوبة لإدارة تقييمات السلوك في النظام المدرسي
/// بما في ذلك إنشاء وتعديل وحذف التقييمات، وإدارة خطط التحسين، والتقارير
///
/// الميزات الرئيسية:
/// - إنشاء تقييمات سلوكية شاملة ومفصلة
/// - تتبع تطور السلوك عبر الزمن
/// - إدارة خطط تحسين السلوك
/// - حساب نقاط السلوك والمكافآت
/// - إرسال إشعارات لأولياء الأمور
/// - إنشاء تقارير سلوكية متقدمة
/// - تحليل الأنماط السلوكية
/// - إدارة الإجراءات التأديبية والمكافآت
class BehaviorEvaluationService {
  /// مرجع مجموعة تقييمات السلوك في Firestore
  final CollectionReference _evaluationsCollection = FirebaseFirestore.instance
      .collection('behavior_evaluations');

  /// مرجع مجموعة الطلاب للحصول على معلومات الطالب
  final CollectionReference _studentsCollection = FirebaseFirestore.instance
      .collection('students');

  /// مرجع مجموعة المستخدمين للحصول على معلومات المُقيِّمين
  final CollectionReference _usersCollection = FirebaseFirestore.instance
      .collection('users');

  /// مرجع مجموعة نقاط السلوك لتتبع النقاط الإجمالية
  final CollectionReference _behaviorPointsCollection = FirebaseFirestore
      .instance
      .collection('behavior_points');

  // ===================================================================
  // دوال إنشاء وإدارة التقييمات
  // ===================================================================

  /// إنشاء تقييم سلوك جديد
  ///
  /// يقوم بإنشاء تقييم سلوك جديد مع جميع التفاصيل المطلوبة
  /// ويحدث نقاط السلوك الإجمالية للطالب
  ///
  /// [studentId] معرف الطالب المُقيَّم
  /// [studentName] اسم الطالب المُقيَّم
  /// [classId] معرف الصف الدراسي
  /// [className] اسم الصف الدراسي
  /// [evaluatorId] معرف المُقيِّم
  /// [evaluatorName] اسم المُقيِّم
  /// [evaluatorRole] دور المُقيِّم
  /// [evaluationType] نوع التقييم
  /// [category] فئة السلوك
  /// [behaviorType] نوع السلوك (إيجابي، سلبي، محايد)
  /// [severityLevel] مستوى شدة السلوك
  /// [overallRating] التقييم العام
  /// [pointsAwarded] النقاط المكتسبة أو المفقودة
  /// [title] عنوان التقييم
  /// [description] وصف تفصيلي للسلوك
  /// [context] السياق الذي حدث فيه السلوك
  /// [location] موقع حدوث السلوك
  /// [incidentDateTime] تاريخ ووقت حدوث السلوك
  /// [criteriaRatings] تقييمات المعايير المختلفة
  /// [actionsTaken] الإجراءات المتخذة
  /// [recommendations] التوصيات
  /// [subjectId] معرف المادة (اختياري)
  /// [subjectName] اسم المادة (اختياري)
  /// [improvementPlan] خطة التحسين (اختياري)
  /// [requiresFollowUp] هل يحتاج متابعة؟
  /// [followUpDate] تاريخ المتابعة
  /// [tags] العلامات والتصنيفات
  /// [evaluatorNotes] ملاحظات المُقيِّم
  /// [privacyLevel] مستوى الخصوصية
  ///
  /// يرجع معرف التقييم الجديد
  Future<String> createEvaluation({
    required String studentId,
    required String studentName,
    required String classId,
    required String className,
    required String evaluatorId,
    required String evaluatorName,
    required String evaluatorRole,
    required EvaluationType evaluationType,
    required BehaviorCategory category,
    required BehaviorType behaviorType,
    required SeverityLevel severityLevel,
    required OverallRating overallRating,
    required int pointsAwarded,
    required String title,
    required String description,
    required String context,
    required String location,
    required DateTime incidentDateTime,
    required Map<BehaviorCriteria, CriteriaRating> criteriaRatings,
    required List<BehaviorAction> actionsTaken,
    required List<String> recommendations,
    String? subjectId,
    String? subjectName,
    BehaviorImprovementPlan? improvementPlan,
    bool requiresFollowUp = false,
    DateTime? followUpDate,
    List<String> tags = const [],
    String? evaluatorNotes,
    PrivacyLevel privacyLevel = PrivacyLevel.general,
    List<BehaviorAttachment> attachments = const [],
    List<Witness> witnesses = const [],
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات المدخلة
      _validateEvaluationData(
        studentId: studentId,
        evaluatorId: evaluatorId,
        title: title,
        description: description,
        context: context,
        location: location,
        criteriaRatings: criteriaRatings,
      );

      // الحصول على النقاط الحالية للطالب
      final currentPoints = await _getCurrentBehaviorPoints(studentId);
      final totalPoints = currentPoints + pointsAwarded;

      // إنشاء معرف فريد للتقييم
      final evaluationRef = _evaluationsCollection.doc();
      final evaluationId = evaluationRef.id;

      // إنشاء نموذج التقييم
      final evaluation = BehaviorEvaluationModel(
        id: evaluationId,
        studentId: studentId,
        studentName: studentName,
        classId: classId,
        className: className,
        evaluatorId: evaluatorId,
        evaluatorName: evaluatorName,
        evaluatorRole: evaluatorRole,
        evaluationType: evaluationType,
        category: category,
        behaviorType: behaviorType,
        severityLevel: severityLevel,
        overallRating: overallRating,
        pointsAwarded: pointsAwarded,
        totalBehaviorPoints: totalPoints,
        title: title,
        description: description,
        context: context,
        subjectId: subjectId,
        subjectName: subjectName,
        location: location,
        incidentDateTime: incidentDateTime,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        criteriaRatings: criteriaRatings,
        actionsTaken: actionsTaken,
        recommendations: recommendations,
        improvementPlan: improvementPlan,
        requiresFollowUp: requiresFollowUp,
        followUpDate: followUpDate,
        tags: tags,
        evaluatorNotes: evaluatorNotes,
        privacyLevel: privacyLevel,
        attachments: attachments,
        witnesses: witnesses,
        status: EvaluationStatus.draft,
        metadata: metadata,
      );

      // حفظ التقييم في قاعدة البيانات
      await evaluationRef.set(evaluation.toMap());

      // تحديث نقاط السلوك الإجمالية للطالب
      await _updateStudentBehaviorPoints(studentId, totalPoints);

      // إرسال إشعار لولي الأمر إذا كان مطلوباً
      if (evaluation.shouldNotifyParent) {
        await _sendParentNotification(evaluation);
      }

      // إنشاء خطة تحسين إذا كانت مطلوبة
      if (improvementPlan != null) {
        await _createImprovementPlan(evaluationId, improvementPlan);
      }

      // جدولة المتابعة إذا كانت مطلوبة
      if (requiresFollowUp && followUpDate != null) {
        await _scheduleFollowUp(evaluationId, followUpDate);
      }

      return evaluationId;
    } catch (e) {
      throw Exception('فشل في إنشاء تقييم السلوك: $e');
    }
  }

  /// الحصول على تقييمات السلوك للطالب
  ///
  /// يرجع جميع تقييمات السلوك للطالب مع إمكانية الفلترة
  ///
  /// [studentId] معرف الطالب
  /// [startDate] تاريخ البداية للفلترة (اختياري)
  /// [endDate] تاريخ النهاية للفلترة (اختياري)
  /// [behaviorType] نوع السلوك للفلترة (اختياري)
  /// [category] فئة السلوك للفلترة (اختياري)
  /// [limit] عدد التقييمات المطلوب إرجاعها (افتراضي 50)
  Stream<List<BehaviorEvaluationModel>> getStudentEvaluations(
    String studentId, {
    DateTime? startDate,
    DateTime? endDate,
    BehaviorType? behaviorType,
    BehaviorCategory? category,
    int limit = 50,
  }) {
    try {
      Query query = _evaluationsCollection
          .where('studentId', isEqualTo: studentId)
          .orderBy('incidentDateTime', descending: true)
          .limit(limit);

      // تطبيق فلاتر إضافية
      if (startDate != null) {
        query = query.where(
          'incidentDateTime',
          isGreaterThanOrEqualTo: startDate,
        );
      }

      if (endDate != null) {
        query = query.where('incidentDateTime', isLessThanOrEqualTo: endDate);
      }

      if (behaviorType != null) {
        query = query.where('behaviorType', isEqualTo: behaviorType.toString());
      }

      if (category != null) {
        query = query.where('category', isEqualTo: category.toString());
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => BehaviorEvaluationModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      throw Exception('فشل في جلب تقييمات السلوك: $e');
    }
  }

  /// الحصول على تفاصيل تقييم محدد
  ///
  /// [evaluationId] معرف التقييم
  /// يرجع تفاصيل التقييم أو null إذا لم يوجد
  Future<BehaviorEvaluationModel?> getEvaluation(String evaluationId) async {
    try {
      final doc = await _evaluationsCollection.doc(evaluationId).get();

      if (doc.exists) {
        return BehaviorEvaluationModel.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل التقييم: $e');
    }
  }

  /// تحديث تقييم موجود
  ///
  /// يسمح بتحديث تفاصيل التقييم مع إرسال إشعارات عند الحاجة
  ///
  /// [evaluationId] معرف التقييم
  /// [updatedBy] معرف من قام بالتحديث
  /// [updates] الحقول المراد تحديثها
  Future<void> updateEvaluation(
    String evaluationId,
    String updatedBy,
    Map<String, dynamic> updates,
  ) async {
    try {
      // التحقق من وجود التقييم
      final evaluation = await getEvaluation(evaluationId);
      if (evaluation == null) {
        throw Exception('التقييم غير موجود');
      }

      // التحقق من صلاحية التعديل
      if (!evaluation.canBeModified) {
        throw Exception('لا يمكن تعديل هذا التقييم');
      }

      // إضافة معلومات التحديث
      updates['updatedAt'] = Timestamp.fromDate(DateTime.now());
      updates['updatedBy'] = updatedBy;

      // تحديث التقييم في قاعدة البيانات
      await _evaluationsCollection.doc(evaluationId).update(updates);

      // إرسال إشعارات إذا كان مطلوباً
      final updatedEvaluation = await getEvaluation(evaluationId);
      if (updatedEvaluation != null && updatedEvaluation.shouldNotifyParent) {
        await _sendParentNotification(updatedEvaluation);
      }
    } catch (e) {
      throw Exception('فشل في تحديث التقييم: $e');
    }
  }

  /// إنهاء التقييم (تحويله إلى نهائي)
  ///
  /// يقوم بتحويل التقييم من مسودة إلى نهائي
  ///
  /// [evaluationId] معرف التقييم
  /// [finalizedBy] معرف من قام بالإنهاء
  Future<void> finalizeEvaluation(
    String evaluationId,
    String finalizedBy,
  ) async {
    try {
      await _evaluationsCollection.doc(evaluationId).update({
        'status': EvaluationStatus.final_.toString(),
        'updatedAt': Timestamp.fromDate(DateTime.now()),
        'updatedBy': finalizedBy,
      });

      // إرسال إشعار نهائي لولي الأمر
      final evaluation = await getEvaluation(evaluationId);
      if (evaluation != null) {
        await _sendParentNotification(evaluation);
      }
    } catch (e) {
      throw Exception('فشل في إنهاء التقييم: $e');
    }
  }

  /// حذف تقييم
  ///
  /// يقوم بحذف التقييم إذا كان في حالة مسودة
  ///
  /// [evaluationId] معرف التقييم
  /// [deletedBy] معرف من قام بالحذف
  Future<void> deleteEvaluation(String evaluationId, String deletedBy) async {
    try {
      final evaluation = await getEvaluation(evaluationId);
      if (evaluation == null) {
        throw Exception('التقييم غير موجود');
      }

      if (!evaluation.canBeDeleted) {
        throw Exception('لا يمكن حذف هذا التقييم');
      }

      // حذف التقييم من قاعدة البيانات
      await _evaluationsCollection.doc(evaluationId).delete();

      // تحديث نقاط السلوك للطالب
      final newTotal =
          evaluation.totalBehaviorPoints - evaluation.pointsAwarded;
      await _updateStudentBehaviorPoints(evaluation.studentId, newTotal);
    } catch (e) {
      throw Exception('فشل في حذف التقييم: $e');
    }
  }

  /// الحصول على نقاط السلوك الحالية للطالب
  ///
  /// [studentId] معرف الطالب
  /// يرجع إجمالي نقاط السلوك للطالب
  Future<int> getCurrentBehaviorPoints(String studentId) async {
    try {
      return await _getCurrentBehaviorPoints(studentId);
    } catch (e) {
      throw Exception('فشل في جلب نقاط السلوك: $e');
    }
  }

  /// الحصول على إحصائيات السلوك للطالب
  ///
  /// [studentId] معرف الطالب
  /// [startDate] تاريخ البداية (اختياري)
  /// [endDate] تاريخ النهاية (اختياري)
  /// يرجع إحصائيات شاملة لسلوك الطالب
  Future<BehaviorStatistics> getStudentBehaviorStatistics(
    String studentId, {
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _evaluationsCollection.where(
        'studentId',
        isEqualTo: studentId,
      );

      if (startDate != null) {
        query = query.where(
          'incidentDateTime',
          isGreaterThanOrEqualTo: startDate,
        );
      }

      if (endDate != null) {
        query = query.where('incidentDateTime', isLessThanOrEqualTo: endDate);
      }

      final snapshot = await query.get();
      final evaluations =
          snapshot.docs
              .map((doc) => BehaviorEvaluationModel.fromFirestore(doc))
              .toList();

      return _calculateBehaviorStatistics(evaluations);
    } catch (e) {
      throw Exception('فشل في حساب إحصائيات السلوك: $e');
    }
  }

  /// الحصول على تقييمات تحتاج متابعة
  ///
  /// يرجع التقييمات التي تحتاج متابعة في التاريخ المحدد أو قبله
  ///
  /// [date] التاريخ المحدد للمتابعة (افتراضي اليوم)
  Stream<List<BehaviorEvaluationModel>> getEvaluationsRequiringFollowUp({
    DateTime? date,
  }) {
    try {
      final targetDate = date ?? DateTime.now();

      return _evaluationsCollection
          .where('requiresFollowUp', isEqualTo: true)
          .where('followUpDate', isLessThanOrEqualTo: targetDate)
          .where('status', isNotEqualTo: EvaluationStatus.archived.toString())
          .snapshots()
          .map((snapshot) {
            return snapshot.docs
                .map((doc) => BehaviorEvaluationModel.fromFirestore(doc))
                .toList();
          });
    } catch (e) {
      throw Exception('فشل في جلب التقييمات المطلوب متابعتها: $e');
    }
  }

  // ===================================================================
  // الدوال المساعدة الخاصة
  // ===================================================================

  /// التحقق من صحة بيانات التقييم
  void _validateEvaluationData({
    required String studentId,
    required String evaluatorId,
    required String title,
    required String description,
    required String context,
    required String location,
    required Map<BehaviorCriteria, CriteriaRating> criteriaRatings,
  }) {
    if (studentId.trim().isEmpty) {
      throw Exception('معرف الطالب مطلوب');
    }

    if (evaluatorId.trim().isEmpty) {
      throw Exception('معرف المُقيِّم مطلوب');
    }

    if (title.trim().isEmpty) {
      throw Exception('عنوان التقييم مطلوب');
    }

    if (description.trim().isEmpty) {
      throw Exception('وصف السلوك مطلوب');
    }

    if (context.trim().isEmpty) {
      throw Exception('سياق السلوك مطلوب');
    }

    if (location.trim().isEmpty) {
      throw Exception('موقع السلوك مطلوب');
    }

    if (criteriaRatings.isEmpty) {
      throw Exception('يجب تقييم معيار واحد على الأقل');
    }
  }

  /// الحصول على النقاط الحالية للطالب من قاعدة البيانات
  Future<int> _getCurrentBehaviorPoints(String studentId) async {
    try {
      final doc = await _behaviorPointsCollection.doc(studentId).get();

      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return data['totalPoints'] ?? 0;
      }

      return 0; // نقاط افتراضية للطلاب الجدد
    } catch (e) {
      return 0;
    }
  }

  /// تحديث نقاط السلوك الإجمالية للطالب
  Future<void> _updateStudentBehaviorPoints(
    String studentId,
    int totalPoints,
  ) async {
    try {
      await _behaviorPointsCollection.doc(studentId).set({
        'studentId': studentId,
        'totalPoints': totalPoints,
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
      }, SetOptions(merge: true));
    } catch (e) {
      // تسجيل الخطأ ولكن عدم إيقاف العملية
      print('خطأ في تحديث نقاط السلوك: $e');
    }
  }

  /// إرسال إشعار لولي الأمر
  Future<void> _sendParentNotification(
    BehaviorEvaluationModel evaluation,
  ) async {
    try {
      // TODO: تنفيذ إرسال الإشعارات عبر Firebase Cloud Messaging
      // أو النظام المستخدم للإشعارات في التطبيق

      // تحديث حالة الإشعار في التقييم
      await _evaluationsCollection.doc(evaluation.id).update({
        'parentNotified': true,
        'parentNotificationDate': Timestamp.fromDate(DateTime.now()),
        'notificationMethod': NotificationMethod.message.toString(),
      });
    } catch (e) {
      print('خطأ في إرسال إشعار ولي الأمر: $e');
    }
  }

  /// إنشاء خطة تحسين السلوك
  Future<void> _createImprovementPlan(
    String evaluationId,
    BehaviorImprovementPlan plan,
  ) async {
    try {
      // TODO: تنفيذ إنشاء خطة التحسين في مجموعة منفصلة
      // أو ربطها بالتقييم الحالي
      print('تم إنشاء خطة تحسين للتقييم: $evaluationId');
    } catch (e) {
      print('خطأ في إنشاء خطة التحسين: $e');
    }
  }

  /// جدولة متابعة للتقييم
  Future<void> _scheduleFollowUp(
    String evaluationId,
    DateTime followUpDate,
  ) async {
    try {
      // TODO: تنفيذ جدولة المتابعة
      // يمكن استخدام Firebase Functions أو نظام جدولة آخر
      print('تم جدولة متابعة للتقييم: $evaluationId في $followUpDate');
    } catch (e) {
      print('خطأ في جدولة المتابعة: $e');
    }
  }

  /// حساب إحصائيات السلوك
  BehaviorStatistics _calculateBehaviorStatistics(
    List<BehaviorEvaluationModel> evaluations,
  ) {
    if (evaluations.isEmpty) {
      return BehaviorStatistics.empty();
    }

    int totalEvaluations = evaluations.length;
    int positiveEvaluations = 0;
    int negativeEvaluations = 0;
    int neutralEvaluations = 0;
    int totalPoints = 0;
    double totalRating = 0.0;

    Map<BehaviorCategory, int> categoryCount = {};
    Map<OverallRating, int> ratingCount = {};

    for (final evaluation in evaluations) {
      // حساب أنواع السلوك
      switch (evaluation.behaviorType) {
        case BehaviorType.positive:
          positiveEvaluations++;
          break;
        case BehaviorType.negative:
          negativeEvaluations++;
          break;
        case BehaviorType.neutral:
          neutralEvaluations++;
          break;
      }

      // حساب النقاط والتقييمات
      totalPoints += evaluation.pointsAwarded;
      totalRating += evaluation.averageCriteriaRating;

      // حساب الفئات
      categoryCount[evaluation.category] =
          (categoryCount[evaluation.category] ?? 0) + 1;

      // حساب التقييمات
      ratingCount[evaluation.overallRating] =
          (ratingCount[evaluation.overallRating] ?? 0) + 1;
    }

    final averageRating = totalRating / totalEvaluations;
    final mostCommonCategory =
        categoryCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    final mostCommonRating =
        ratingCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    return BehaviorStatistics(
      totalEvaluations: totalEvaluations,
      positiveEvaluations: positiveEvaluations,
      negativeEvaluations: negativeEvaluations,
      neutralEvaluations: neutralEvaluations,
      totalPoints: totalPoints,
      averageRating: averageRating,
      mostCommonCategory: mostCommonCategory,
      mostCommonRating: mostCommonRating,
      categoryDistribution: categoryCount,
      ratingDistribution: ratingCount,
    );
  }
}

/// فئة إحصائيات السلوك
class BehaviorStatistics {
  /// إجمالي عدد التقييمات
  final int totalEvaluations;

  /// عدد التقييمات الإيجابية
  final int positiveEvaluations;

  /// عدد التقييمات السلبية
  final int negativeEvaluations;

  /// عدد التقييمات المحايدة
  final int neutralEvaluations;

  /// إجمالي النقاط
  final int totalPoints;

  /// متوسط التقييم
  final double averageRating;

  /// الفئة الأكثر شيوعاً
  final BehaviorCategory mostCommonCategory;

  /// التقييم الأكثر شيوعاً
  final OverallRating mostCommonRating;

  /// توزيع الفئات
  final Map<BehaviorCategory, int> categoryDistribution;

  /// توزيع التقييمات
  final Map<OverallRating, int> ratingDistribution;

  const BehaviorStatistics({
    required this.totalEvaluations,
    required this.positiveEvaluations,
    required this.negativeEvaluations,
    required this.neutralEvaluations,
    required this.totalPoints,
    required this.averageRating,
    required this.mostCommonCategory,
    required this.mostCommonRating,
    required this.categoryDistribution,
    required this.ratingDistribution,
  });

  /// إنشاء إحصائيات فارغة
  factory BehaviorStatistics.empty() {
    return const BehaviorStatistics(
      totalEvaluations: 0,
      positiveEvaluations: 0,
      negativeEvaluations: 0,
      neutralEvaluations: 0,
      totalPoints: 0,
      averageRating: 0.0,
      mostCommonCategory: BehaviorCategory.discipline,
      mostCommonRating: OverallRating.acceptable,
      categoryDistribution: {},
      ratingDistribution: {},
    );
  }

  /// نسبة التقييمات الإيجابية
  double get positivePercentage {
    if (totalEvaluations == 0) return 0.0;
    return (positiveEvaluations / totalEvaluations) * 100;
  }

  /// نسبة التقييمات السلبية
  double get negativePercentage {
    if (totalEvaluations == 0) return 0.0;
    return (negativeEvaluations / totalEvaluations) * 100;
  }

  /// نسبة التقييمات المحايدة
  double get neutralPercentage {
    if (totalEvaluations == 0) return 0.0;
    return (neutralEvaluations / totalEvaluations) * 100;
  }

  /// متوسط النقاط لكل تقييم
  double get averagePointsPerEvaluation {
    if (totalEvaluations == 0) return 0.0;
    return totalPoints / totalEvaluations;
  }
}
