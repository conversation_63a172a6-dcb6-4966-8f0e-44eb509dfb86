import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// ويدجت مشترك لعرض قائمة منسدلة بالمواد الدراسية مع إمكانية التصفية حسب الصف
class SubjectDropdown extends StatefulWidget {
  final String? selectedValue;
  final void Function(String?) onChanged;
  final String? classId; // مُعلم جديد لتصفية المواد
  final String? Function(String?)? validator;

  const SubjectDropdown({
    super.key,
    required this.selectedValue,
    required this.onChanged,
    this.classId, // جعله اختيارياً
    this.validator,
  });

  @override
  State<SubjectDropdown> createState() => _SubjectDropdownState();
}

class _SubjectDropdownState extends State<SubjectDropdown> {
  // استخدام Future لتخزين نتيجة جلب المواد لتجنب إعادة الجلب مع كل إعادة بناء.
  Future<List<QueryDocumentSnapshot>>? _subjectsFuture;

  @override
  void initState() {
    super.initState();
    _fetchSubjects();
  }

  @override
  void didUpdateWidget(covariant SubjectDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    // يتم استدعاء هذه الدالة عندما يتم تحديث الويدجت من الخارج.
    // نقوم بإعادة جلب المواد فقط إذا تغير `classId` الممرر للويدجت.
    if (widget.classId != oldWidget.classId) {
      _fetchSubjects();
    }
  }

  /// دالة لجلب المواد من Firestore بناءً على `classId` المحدد.
  void _fetchSubjects() {
    // إذا لم يتم تحديد صف (classId is null)، نعيد قائمة فارغة فوراً.
    if (widget.classId == null) {
      setState(() {
        _subjectsFuture = Future.value([]);
      });
      return;
    }

    final subjectsCollection = FirebaseFirestore.instance.collection('subjects');
    
    // استخدام استعلام واحد فعال باستخدام 'whereIn'.
    // هذا يتطلب وجود الفهرس المركب (classId, name) الذي تم إنشاؤه مسبقًا.
    final future = subjectsCollection
        .where('classId', whereIn: [widget.classId, 'all'])
        .orderBy('name')
        .get()
        .then((snapshot) => snapshot.docs);

    setState(() {
      _subjectsFuture = future;
    });
  }

  @override
  Widget build(BuildContext context) {
    // استخدام FutureBuilder لإعادة بناء الواجهة عند اكتمال الـ Future.
    return FutureBuilder<List<QueryDocumentSnapshot>>(
      future: _subjectsFuture,
      builder: (context, snapshot) {
        // عرض مؤشر تحميل أثناء انتظار البيانات.
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        // عرض قائمة منسدلة معطلة في حالة وجود خطأ، عدم وجود بيانات، أو إذا كانت القائمة فارغة.
        if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
          return DropdownButtonFormField<String>(
            items: const [],
            onChanged: null, // تعطيل القائمة
            decoration: InputDecoration(
              labelText: 'المادة الدراسية',
              hintText: widget.classId == null ? 'الرجاء اختيار الصف أولاً' : 'لا توجد مواد متاحة',
              border: const OutlineInputBorder(),
              disabledBorder: OutlineInputBorder(
                borderSide: BorderSide(color: Colors.grey.shade400),
              ),
            ),
            disabledHint: Text(widget.classId == null ? 'الرجاء اختيار الصف أولاً' : 'لا توجد مواد متاحة'),
          );
        }

        // استخلاص أسماء المواد من المستندات التي تم جلبها.
        // استخدام Set يضمن عدم تكرار أسماء المواد في القائمة المنسدلة.
        final subjectNames = snapshot.data!
            .map((doc) => doc['name'] as String)
            .toSet()
            .toList();

        // تحويل قائمة الأسماء إلى قائمة من DropdownMenuItem.
        final items = subjectNames.map((name) {
          return DropdownMenuItem<String>(
            value: name,
            child: Text(name),
          );
        }).toList();

        // التحقق من أن القيمة المحددة حالياً (selectedValue) موجودة ضمن قائمة المواد المتاحة.
        // هذا يمنع حدوث خطأ إذا كانت القيمة المحفوظة لم تعد صالحة (مثلاً، تم حذف المادة).
        final isValueInItems = widget.selectedValue != null && subjectNames.contains(widget.selectedValue);

        return DropdownButtonFormField<String>(
          // عرض القيمة المحددة فقط إذا كانت موجودة في القائمة، وإلا نعرض null.
          value: isValueInItems ? widget.selectedValue : null,
          onChanged: widget.onChanged,
          validator: widget.validator ?? (value) => value == null ? 'الرجاء اختيار المادة' : null,
          items: items,
          decoration: const InputDecoration(
            labelText: 'المادة الدراسية',
            border: OutlineInputBorder(),
          ),
          hint: const Text('اختر المادة'),
        );
      },
    );
  }
}
