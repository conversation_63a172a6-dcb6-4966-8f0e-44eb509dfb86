import 'dart:convert';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

// 1. State Class
class AboutSchoolState {
  final bool isLoading;
  final bool isSaving;
  final String vision;
  final String mission;
  final String contactInfo;
  final String history;
  final String values;
  final String principalMessage;
  final Document description;
  final String? logoUrl;
  final XFile? newLogoFile;
  final List<String> galleryImageUrls;
  final List<XFile> newGalleryImages;

  AboutSchoolState({
    this.isLoading = true,
    this.isSaving = false,
    this.vision = '',
    this.mission = '',
    this.contactInfo = '',
    this.history = '',
    this.values = '',
    this.principalMessage = '',
    required this.description,
    this.logoUrl,
    this.newLogoFile,
    this.galleryImageUrls = const [],
    this.newGalleryImages = const [],
  });

  AboutSchoolState copyWith({
    bool? isLoading,
    bool? isSaving,
    String? vision,
    String? mission,
    String? contactInfo,
    String? history,
    String? values,
    String? principalMessage,
    Document? description,
    String? logoUrl,
    XFile? newLogoFile,
    List<String>? galleryImageUrls,
    List<XFile>? newGalleryImages,
    bool clearNewLogo = false,
  }) {
    return AboutSchoolState(
      isLoading: isLoading ?? this.isLoading,
      isSaving: isSaving ?? this.isSaving,
      vision: vision ?? this.vision,
      mission: mission ?? this.mission,
      contactInfo: contactInfo ?? this.contactInfo,
      history: history ?? this.history,
      values: values ?? this.values,
      principalMessage: principalMessage ?? this.principalMessage,
      description: description ?? this.description,
      logoUrl: logoUrl ?? this.logoUrl,
      newLogoFile: clearNewLogo ? null : newLogoFile ?? this.newLogoFile,
      galleryImageUrls: galleryImageUrls ?? this.galleryImageUrls,
      newGalleryImages: newGalleryImages ?? this.newGalleryImages,
    );
  }
}

// 2. Controller (StateNotifier)
class AboutSchoolController extends StateNotifier<AboutSchoolState> {
  final FirebaseService _firebaseService;
  final DocumentReference _aboutDocRef =
      FirebaseFirestore.instance.collection('school_info').doc('about_us');

  AboutSchoolController(this._firebaseService)
      : super(AboutSchoolState(description: Document()));

  Future<void> loadContent() async {
    try {
      final doc = await _aboutDocRef.get();
      if (doc.exists && doc.data() != null) {
        final data = doc.data() as Map<String, dynamic>;
        
        Document descriptionDoc = Document();
        final descriptionJson = data['description'];
        if (descriptionJson != null && descriptionJson is String && descriptionJson.isNotEmpty) {
          try {
            final decoded = jsonDecode(descriptionJson);
            descriptionDoc = Document.fromJson(decoded);
          } catch (e) {
            descriptionDoc = Document()..insert(0, descriptionJson);
          }
        }

        state = state.copyWith(
          vision: data['vision'] ?? '',
          mission: data['mission'] ?? '',
          contactInfo: data['contactInfo'] ?? '',
          history: data['history'] ?? '',
          values: data['values'] ?? '',
          principalMessage: data['principalMessage'] ?? '',
          logoUrl: data['logoUrl'],
          galleryImageUrls: List<String>.from(data['galleryImageUrls'] ?? []),
          description: descriptionDoc,
          isLoading: false,
        );
      } else {
        state = state.copyWith(isLoading: false);
      }
    } catch (e) {
      state = state.copyWith(isLoading: false);
      // Handle error appropriately
    }
  }

  void pickNewLogo() async {
    final image = await _firebaseService.pickImage();
    if (image != null) {
      state = state.copyWith(newLogoFile: image);
    }
  }

  void pickGalleryImages() async {
    final images = await _firebaseService.pickMultipleImages();
    if (images.isNotEmpty) {
      state = state.copyWith(
        newGalleryImages: [...state.newGalleryImages, ...images],
      );
    }
  }
  
  void removeNewGalleryImage(int index) {
    final currentImages = List<XFile>.from(state.newGalleryImages);
    currentImages.removeAt(index);
    state = state.copyWith(newGalleryImages: currentImages);
  }

  Future<void> deleteGalleryImage(String imageUrl) async {
    state = state.copyWith(isSaving: true);
    try {
      await _firebaseService.deleteImage(imageUrl);
      final updatedUrls = List<String>.from(state.galleryImageUrls)..remove(imageUrl);
      await _aboutDocRef.update({'galleryImageUrls': updatedUrls});
      state = state.copyWith(galleryImageUrls: updatedUrls, isSaving: false);
    } catch (e) {
      state = state.copyWith(isSaving: false);
      rethrow;
    }
  }

  Future<void> saveContent({
    required String vision,
    required String mission,
    required String contactInfo,
    required String history,
    required String values,
    required String principalMessage,
    required Document description,
  }) async {
    state = state.copyWith(isSaving: true);
    String? finalLogoUrl = state.logoUrl;

    try {
      if (state.newLogoFile != null) {
        if (state.logoUrl != null && state.logoUrl!.isNotEmpty) {
          await _firebaseService.deleteImage(state.logoUrl!);
        }
        finalLogoUrl = await _firebaseService.uploadImage(state.newLogoFile!, 'school_info');
      }

      List<String> finalGalleryUrls = List.from(state.galleryImageUrls);
      if (state.newGalleryImages.isNotEmpty) {
        final uploadedUrls = await _firebaseService.uploadMultipleImages(state.newGalleryImages, 'school_info/gallery');
        finalGalleryUrls.addAll(uploadedUrls);
      }

      final descriptionJson = jsonEncode(description.toDelta().toJson());

      final Map<String, dynamic> dataToSave = {
        'vision': vision,
        'mission': mission,
        'contactInfo': contactInfo,
        'history': history,
        'values': values,
        'principalMessage': principalMessage,
        'description': descriptionJson,
        'logoUrl': finalLogoUrl,
        'galleryImageUrls': finalGalleryUrls,
        'lastUpdated': FieldValue.serverTimestamp(),
      };

      await _aboutDocRef.set(dataToSave);

      state = state.copyWith(
        isSaving: false,
        logoUrl: finalLogoUrl,
        galleryImageUrls: finalGalleryUrls,
        newGalleryImages: [],
        clearNewLogo: true,
      );
    } catch (e) {
      state = state.copyWith(isSaving: false);
      rethrow;
    }
  }
}

// 3. Provider
final aboutSchoolControllerProvider = StateNotifierProvider<AboutSchoolController, AboutSchoolState>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return AboutSchoolController(firebaseService)..loadContent();
});
