import 'package:cloud_firestore/cloud_firestore.dart';

class Subject {
  final String id;
  final String name;
  final String classId; // 'all' for a general subject

  Subject({
    required this.id,
    required this.name,
    required this.classId,
  });

  factory Subject.fromFirestore(DocumentSnapshot doc) {
    Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
    return Subject(
      id: doc.id,
      name: data['name'] ?? '',
      classId: data['classId'] ?? 'all',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'classId': classId,
    };
  }
}
