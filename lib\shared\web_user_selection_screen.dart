import 'package:flutter/material.dart';
import 'package:school_management_system/admin_screens/admin_auth_gate.dart';
import 'package:school_management_system/teacher_screens/teacher_auth_gate.dart';

/// شاشة اختيار نوع المستخدم للويب
/// 
/// هذه الشاشة تظهر للمستخدمين على الويب لاختيار نوع الحساب:
/// - مدير النظام (الإدارة)
/// - معلم
/// 
/// التصميم:
/// - بطاقات كبيرة وواضحة لكل نوع مستخدم
/// - ألوان مميزة لكل نوع
/// - أيقونات واضحة
/// - تصميم متجاوب
class WebUserSelectionScreen extends StatelessWidget {
  const WebUserSelectionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[100],
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 800),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // شعار ورأس الصفحة
                _buildHeader(),
                const SizedBox(height: 48),
                
                // بطاقات اختيار نوع المستخدم
                Row(
                  children: [
                    // بطاقة الإدارة
                    Expanded(
                      child: _buildUserTypeCard(
                        context: context,
                        title: 'مدير النظام',
                        subtitle: 'لوحة تحكم الإدارة',
                        description: 'إدارة شاملة للمدرسة والطلاب والموظفين',
                        icon: Icons.admin_panel_settings,
                        color: Colors.blue,
                        onTap: () => _navigateToAdmin(context),
                      ),
                    ),
                    const SizedBox(width: 24),
                    
                    // بطاقة المعلم
                    Expanded(
                      child: _buildUserTypeCard(
                        context: context,
                        title: 'معلم',
                        subtitle: 'لوحة تحكم المعلم',
                        description: 'إدارة الامتحانات والدرجات والمناهج',
                        icon: Icons.school,
                        color: Colors.green,
                        onTap: () => _navigateToTeacher(context),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 32),
                
                // معلومات إضافية
                _buildFooterInfo(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Column(
      children: [
        // شعار المدرسة
        Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: Colors.white,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.2),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: const Icon(
            Icons.school,
            size: 64,
            color: Colors.indigo,
          ),
        ),
        const SizedBox(height: 24),
        
        // عنوان النظام
        const Text(
          'نظام إدارة المدرسة',
          style: TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.indigo,
          ),
        ),
        const SizedBox(height: 8),
        
        // وصف النظام
        Text(
          'اختر نوع حسابك للوصول إلى لوحة التحكم المناسبة',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة نوع مستخدم واحد
  Widget _buildUserTypeCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: color.withValues(alpha: 0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            // أيقونة نوع المستخدم
            Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: color,
              ),
            ),
            const SizedBox(height: 24),
            
            // عنوان نوع المستخدم
            Text(
              title,
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 8),
            
            // عنوان فرعي
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 16),
            
            // وصف الوظائف
            Text(
              description,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                height: 1.5,
              ),
            ),
            const SizedBox(height: 24),
            
            // زر الدخول
            ElevatedButton.icon(
              onPressed: onTap,
              icon: const Icon(Icons.login, color: Colors.white),
              label: const Text(
                'تسجيل الدخول',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء معلومات التذييل
  Widget _buildFooterInfo() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.info_outline, color: Colors.blue[600], size: 20),
              const SizedBox(width: 8),
              Text(
                'معلومات مهمة',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.blue[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'للطلاب وأولياء الأمور: يرجى استخدام تطبيق الجوال للوصول إلى حساباتكم',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إذا كنت تواجه مشاكل في تسجيل الدخول، تواصل مع قسم تقنية المعلومات',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  /// التنقل إلى لوحة تحكم الإدارة
  void _navigateToAdmin(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AdminAuthGate(),
      ),
    );
  }

  /// التنقل إلى لوحة تحكم المعلم
  void _navigateToTeacher(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const TeacherAuthGate(),
      ),
    );
  }
}
