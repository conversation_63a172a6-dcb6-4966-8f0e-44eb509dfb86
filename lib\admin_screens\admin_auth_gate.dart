import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:school_management_system/admin_screens/admin_login_screen.dart';
import 'package:school_management_system/admin_screens/admin_main_layout.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// بوابة المصادقة لواجهة الويب الخاصة بالإدارة
class AdminAuthGate extends StatelessWidget {
  const AdminAuthGate({super.key});

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<User?>(
      stream: FirebaseService().authStateChanges,
      builder: (context, authSnapshot) {
        if (authSnapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (authSnapshot.hasData) {
          // المستخدم مسجل دخوله، الآن تحقق من صلاحياته
          return FutureBuilder<String?>(
            future: FirebaseService().getUserRole(authSnapshot.data!.uid),
            builder: (context, roleSnapshot) {
              if (roleSnapshot.connectionState == ConnectionState.waiting) {
                return const LoadingIndicator();
              }

              if (roleSnapshot.hasData && roleSnapshot.data == 'admin') {
                // المستخدم هو admin، اسمح له بالدخول
                return const AdminMainLayout();
              }

              // إذا لم يكن admin أو حدث خطأ، اعرض رسالة وارجعه لشاشة الدخول
              // من الأفضل تسجيل خروجه أيضاً لمنع أي التباس
              return Scaffold(
                body: Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Text('ليس لديك صلاحية للوصول إلى هذه الصفحة.'),
                      const SizedBox(height: 20),
                      ElevatedButton(
                        onPressed: () {
                          FirebaseService().signOut();
                        },
                        child: const Text('العودة لتسجيل الدخول'),
                      )
                    ],
                  ),
                ),
              );
            },
          );
        }

        return const AdminLoginScreen();
      },
    );
  }
}
