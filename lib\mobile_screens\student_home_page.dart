import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/providers/student_providers.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/shared_app_drawer.dart';
import 'package:school_management_system/mobile_screens/student_grades_screen.dart';
import 'package:school_management_system/mobile_screens/student_attendance_screen.dart';
import 'package:school_management_system/mobile_screens/student_timetable_screen.dart';
import 'package:school_management_system/mobile_screens/student_assignments_screen.dart';
import 'package:school_management_system/mobile_screens/student_fees_screen.dart';
import 'package:school_management_system/mobile_screens/public_notifications_screen.dart';
// استيراد الشاشات المتكاملة للطلاب
// ملاحظة: تم استبدال الشاشات الوهمية بالشاشات المتكاملة الموجودة

class StudentHomeScreen extends ConsumerWidget {
  final String studentId;

  const StudentHomeScreen({Key? key, required this.studentId})
    : super(key: key);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardDataAsync = ref.watch(studentDashboardProvider(studentId));

    return Scaffold(
      appBar: AppBar(title: const Text('بوابة الطالب'), centerTitle: true),
      drawer: const SharedAppDrawer(),
      body: RefreshIndicator(
        onRefresh: () async {
          // Invalidate the provider to refetch the data
          ref.invalidate(studentDashboardProvider(studentId));
        },
        child: dashboardDataAsync.when(
          loading: () => const LoadingIndicator(),
          error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
          data: (data) {
            final studentInfo = data['studentInfo'] as Map<String, dynamic>;
            final upcomingAssignments =
                data['upcomingAssignments'] as List<dynamic>;
            final latestAnnouncement =
                data['latestAnnouncement'] as Map<String, dynamic>?;

            return ListView(
              padding: const EdgeInsets.all(16.0),
              children: [
                _buildWelcomeSection(context, studentInfo['name'] ?? 'طالب'),
                const SizedBox(height: 24),
                _buildExamQuickActions(context, studentId),
                const SizedBox(height: 24),
                _buildNavigationGrid(context, studentId),
                const SizedBox(height: 24),
                if (upcomingAssignments.isNotEmpty)
                  _buildUpcomingAssignments(context, upcomingAssignments),
                if (latestAnnouncement != null)
                  _buildLatestAnnouncement(context, latestAnnouncement),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildWelcomeSection(BuildContext context, String studentName) {
    return Text(
      'مرحباً بك، $studentName',
      style: Theme.of(
        context,
      ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
    );
  }

  /// بناء قسم الإجراءات السريعة للامتحانات
  Widget _buildExamQuickActions(BuildContext context, String studentId) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الامتحانات والنتائج',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.indigo[800],
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                context,
                icon: Icons.assignment,
                title: 'واجباتي',
                subtitle: 'راجع واجباتك وتتبع تقدمك',
                color: Colors.purple,
                onTap:
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                StudentAssignmentsScreen(studentId: studentId),
                      ),
                    ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                context,
                icon: Icons.analytics,
                title: 'درجاتي',
                subtitle: 'اطلع على درجاتك وتقييماتك',
                color: Colors.teal,
                onTap:
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder:
                            (context) =>
                                StudentGradesScreen(studentId: studentId),
                      ),
                    ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: _buildQuickActionCard(
            context,
            icon: Icons.calendar_today,
            title: 'جدولي الدراسي',
            subtitle: 'تابع جدولك الدراسي ومواعيدك',
            color: Colors.indigo,
            onTap:
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder:
                        (context) =>
                            StudentTimetableScreen(studentId: studentId),
                  ),
                ),
          ),
        ),
      ],
    );
  }

  /// بناء بطاقة إجراء سريع
  Widget _buildQuickActionCard(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Icon(icon, size: 32, color: color),
              const SizedBox(height: 8),
              Text(
                title,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                subtitle,
                style: Theme.of(
                  context,
                ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavigationGrid(BuildContext context, String studentId) {
    final items = [
      {
        'icon': Icons.assessment,
        'label': 'الدرجات',
        'screen': StudentGradesScreen(studentId: studentId),
      },
      {
        'icon': Icons.event_available,
        'label': 'الحاضر',
        'screen': StudentAttendanceScreen(studentId: studentId),
      },
      {
        'icon': Icons.schedule,
        'label': 'الجدول',
        'screen': StudentTimetableScreen(studentId: studentId),
      },
      {
        'icon': Icons.assignment,
        'label': 'الواجبات',
        'screen': StudentAssignmentsScreen(studentId: studentId),
      },
      {
        'icon': Icons.payment,
        'label': 'الرسوم',
        'screen': StudentFeesScreen(studentId: studentId),
      },
      {
        'icon': Icons.campaign,
        'label': 'الإعلانات',
        'screen': const PublicNotificationsScreen(),
      },
      // الشاشات المتكاملة للطلاب
      {
        'icon': Icons.assignment,
        'label': 'الواجبات المدرسية',
        'screen': StudentAssignmentsScreen(studentId: studentId),
      },
      {
        'icon': Icons.analytics,
        'label': 'درجاتي وتقييمي',
        'screen': StudentGradesScreen(studentId: studentId),
      },
      {
        'icon': Icons.calendar_today,
        'label': 'جدولي الدراسي',
        'screen': StudentTimetableScreen(studentId: studentId),
      },
    ];

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
        childAspectRatio: 1.1,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return GestureDetector(
          onTap:
              () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => item['screen'] as Widget,
                ),
              ),
          child: Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  item['icon'] as IconData,
                  size: 36,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(height: 8),
                Text(item['label'] as String, textAlign: TextAlign.center),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildUpcomingAssignments(
    BuildContext context,
    List<dynamic> assignments,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, 'الواجبات القادمة', Icons.assignment_late),
        Card(
          elevation: 2,
          child: Column(
            children:
                assignments.map((assignment) {
                  final dueDate = (assignment['dueDate'] as Timestamp).toDate();
                  final formattedDate = DateFormat.yMMMMd('ar').format(dueDate);
                  return ListTile(
                    leading: const Icon(
                      Icons.book_outlined,
                      color: Colors.brown,
                    ),
                    title: Text(assignment['title'] ?? 'بلا عنوان'),
                    subtitle: Text(
                      assignment['subjectName'] ?? 'مادة غير محددة',
                    ),
                    trailing: Text(
                      'التسليم: $formattedDate',
                      style: const TextStyle(fontSize: 12),
                    ),
                  );
                }).toList(),
          ),
        ),
        const SizedBox(height: 24),
      ],
    );
  }

  Widget _buildLatestAnnouncement(
    BuildContext context,
    Map<String, dynamic> announcement,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(context, 'آخر الإعلانات', Icons.campaign_outlined),
        Card(
          elevation: 2,
          color: Colors.blue.shade50,
          child: ListTile(
            leading: const Icon(Icons.info_outline, color: Colors.blue),
            title: Text(
              announcement['title'] ?? 'بلا عنوان',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            subtitle: Text(
              announcement['content'] ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            onTap:
                () => Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const PublicNotificationsScreen(),
                  ),
                ),
          ),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, color: Theme.of(context).primaryColorDark),
          const SizedBox(width: 8),
          Text(title, style: Theme.of(context).textTheme.titleLarge),
        ],
      ),
    );
  }
}
