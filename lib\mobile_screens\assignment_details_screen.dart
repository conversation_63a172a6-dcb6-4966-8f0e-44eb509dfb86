import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/providers/assignment_providers.dart';

/// شاشة تفاصيل الواجب المدرسي
/// تعرض جميع تفاصيل الواجب بشكل مفصل مع إمكانية عرض المرفقات
class AssignmentDetailsScreen extends ConsumerWidget {
  final AssignmentModel assignment;
  final String studentId;

  const AssignmentDetailsScreen({
    super.key,
    required this.assignment,
    required this.studentId,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مراقبة حالة الواجب (مُسلم، متأخر، مطلوب)
    final cardState = ref.watch(
      assignmentCardControllerProvider((
        studentId: studentId,
        assignment: assignment,
      )),
    );

    // تنسيق التواريخ باللغة العربية
    final formattedDueDate = DateFormat.yMMMMd('ar').format(assignment.dueDate);
    final formattedCreatedDate = DateFormat.yMMMMd(
      'ar',
    ).format(assignment.createdAt.toDate());

    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الواجب'),
        centerTitle: true,
        elevation: 0,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // بطاقة معلومات الواجب الأساسية
            _buildMainInfoCard(
              context,
              cardState,
              formattedDueDate,
              formattedCreatedDate,
            ),

            const SizedBox(height: 16),

            // بطاقة الوصف التفصيلي
            _buildDescriptionCard(context),

            const SizedBox(height: 16),

            // بطاقة المرفقات (إذا كانت متوفرة)
            if (assignment.hasAttachments) _buildAttachmentsCard(context),

            const SizedBox(height: 16),

            // بطاقة معلومات إضافية
            _buildAdditionalInfoCard(context),

            const SizedBox(height: 24),

            // زر رفع الحل (إذا لم يتم التسليم بعد)
            if (!cardState.isSubmitted &&
                DateTime.now().isBefore(assignment.dueDate))
              _buildSubmitButton(context, cardState, ref),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المعلومات الأساسية للواجب
  Widget _buildMainInfoCard(
    BuildContext context,
    AssignmentCardState cardState,
    String formattedDueDate,
    String formattedCreatedDate,
  ) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // العنوان مع أيقونة الأولوية
            Row(
              children: [
                Icon(
                  assignment.priorityIcon,
                  color: assignment.priorityColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    assignment.title,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // معلومات المادة والمعلم
            _buildInfoRow(
              icon: Icons.subject,
              label: 'المادة',
              value: assignment.subjectName,
            ),

            if (assignment.teacherName != null)
              _buildInfoRow(
                icon: Icons.person,
                label: 'المعلم',
                value: assignment.teacherName!,
              ),

            // تاريخ الإنشاء وتاريخ التسليم
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'تاريخ الإنشاء',
              value: formattedCreatedDate,
            ),

            _buildInfoRow(
              icon: Icons.schedule,
              label: 'تاريخ التسليم',
              value: formattedDueDate,
              valueColor:
                  DateTime.now().isAfter(assignment.dueDate)
                      ? Colors.red
                      : Colors.orange,
            ),

            // الدرجة العظمى
            _buildInfoRow(
              icon: Icons.grade,
              label: 'الدرجة العظمى',
              value: '${assignment.maxGrade} درجة',
            ),

            const SizedBox(height: 16),

            // حالة التسليم
            Row(
              children: [
                const Icon(Icons.info_outline, color: Colors.blue),
                const SizedBox(width: 8),
                const Text(
                  'الحالة: ',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: cardState.statusColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    cardState.statusText,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),

            // شريط الأولوية
            const SizedBox(height: 12),
            Row(
              children: [
                const Icon(Icons.flag, color: Colors.grey),
                const SizedBox(width: 8),
                const Text(
                  'الأولوية: ',
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: assignment.priorityColor.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(color: assignment.priorityColor),
                  ),
                  child: Text(
                    assignment.priority.arabicName,
                    style: TextStyle(
                      color: assignment.priorityColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة الوصف التفصيلي
  Widget _buildDescriptionCard(BuildContext context) {
    if (assignment.description.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.description, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'وصف الواجب',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                assignment.description,
                style: const TextStyle(fontSize: 14, height: 1.5),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة المرفقات
  Widget _buildAttachmentsCard(BuildContext context) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                const Icon(Icons.attach_file, color: Colors.green),
                const SizedBox(width: 8),
                Text(
                  'المرفقات (${assignment.attachmentCount})',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // عرض قائمة المرفقات
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: assignment.attachmentCount,
              separatorBuilder: (context, index) => const SizedBox(height: 8),
              itemBuilder: (context, index) {
                final attachment = assignment.attachments[index];
                return _buildAttachmentItem(
                  context,
                  attachment.url,
                  attachment.name,
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر مرفق واحد
  Widget _buildAttachmentItem(BuildContext context, String url, String name) {
    // تحديد نوع الملف من الامتداد
    final extension = url.split('.').last.toLowerCase();
    IconData icon;
    Color iconColor;

    switch (extension) {
      case 'pdf':
        icon = Icons.picture_as_pdf;
        iconColor = Colors.red;
        break;
      case 'doc':
      case 'docx':
        icon = Icons.description;
        iconColor = Colors.blue;
        break;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        icon = Icons.image;
        iconColor = Colors.green;
        break;
      case 'mp4':
      case 'avi':
      case 'mov':
        icon = Icons.video_file;
        iconColor = Colors.purple;
        break;
      default:
        icon = Icons.insert_drive_file;
        iconColor = Colors.grey;
    }

    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Row(
        children: [
          Icon(icon, color: iconColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  'اضغط للعرض أو التحميل',
                  style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => _openAttachment(url),
            icon: const Icon(Icons.open_in_new),
            tooltip: 'فتح المرفق',
          ),
        ],
      ),
    );
  }

  /// بناء بطاقة المعلومات الإضافية
  Widget _buildAdditionalInfoCard(BuildContext context) {
    // حساب الوقت المتبقي للتسليم
    final now = DateTime.now();
    final timeRemaining = assignment.dueDate.difference(now);
    String timeRemainingText;
    Color timeRemainingColor;

    if (timeRemaining.isNegative) {
      timeRemainingText = 'انتهى الموعد النهائي';
      timeRemainingColor = Colors.red;
    } else if (timeRemaining.inDays > 0) {
      timeRemainingText = 'متبقي ${timeRemaining.inDays} يوم';
      timeRemainingColor =
          timeRemaining.inDays <= 2 ? Colors.orange : Colors.green;
    } else if (timeRemaining.inHours > 0) {
      timeRemainingText = 'متبقي ${timeRemaining.inHours} ساعة';
      timeRemainingColor = Colors.orange;
    } else {
      timeRemainingText = 'متبقي ${timeRemaining.inMinutes} دقيقة';
      timeRemainingColor = Colors.red;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Row(
              children: [
                Icon(Icons.info, color: Colors.blue),
                SizedBox(width: 8),
                Text(
                  'معلومات إضافية',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 12),

            // الوقت المتبقي
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: timeRemainingColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: timeRemainingColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(Icons.timer, color: timeRemainingColor),
                  const SizedBox(width: 8),
                  Text(
                    timeRemainingText,
                    style: TextStyle(
                      color: timeRemainingColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 12),

            // نصائح للطالب
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.lightbulb, color: Colors.blue),
                      SizedBox(width: 8),
                      Text(
                        'نصائح مهمة:',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• اقرأ التعليمات بعناية قبل البدء\n'
                    '• راجع المرفقات إن وجدت\n'
                    '• لا تتأخر عن الموعد النهائي\n'
                    '• اطلب المساعدة من المعلم عند الحاجة',
                    style: TextStyle(fontSize: 13, height: 1.4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر رفع الحل
  Widget _buildSubmitButton(
    BuildContext context,
    AssignmentCardState cardState,
    WidgetRef ref,
  ) {
    final cardController = ref.read(
      assignmentCardControllerProvider((
        studentId: studentId,
        assignment: assignment,
      )).notifier,
    );

    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton.icon(
        onPressed:
            cardState.isLoading
                ? null
                : () {
                  // تنفيذ منطق رفع الحل
                  // يمكن إضافة منطق اختيار الملف ورفعه هنا
                  _showSubmitDialog(context, cardController);
                },
        icon:
            cardState.isLoading
                ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
                : const Icon(Icons.upload_file),
        label: Text(
          cardState.isLoading ? 'جاري الرفع...' : 'رفع الحل',
          style: const TextStyle(fontSize: 16),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: Theme.of(context).primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }

  /// بناء صف معلومات مع أيقونة
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey.shade600),
          const SizedBox(width: 8),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w600)),
          Expanded(
            child: Text(
              value,
              style: TextStyle(color: valueColor ?? Colors.black87),
            ),
          ),
        ],
      ),
    );
  }

  /// فتح المرفق في المتصفح أو التطبيق المناسب
  Future<void> _openAttachment(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        // يمكن إضافة رسالة خطأ هنا
        debugPrint('لا يمكن فتح الرابط: $url');
      }
    } catch (e) {
      debugPrint('خطأ في فتح المرفق: $e');
    }
  }

  /// عرض حوار رفع الحل
  void _showSubmitDialog(
    BuildContext context,
    AssignmentCardController cardController,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('رفع الحل'),
            content: const Text(
              'هذه الميزة قيد التطوير حالياً.\n'
              'سيتم إضافة إمكانية رفع الملفات قريباً.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('موافق'),
              ),
            ],
          ),
    );
  }
}
