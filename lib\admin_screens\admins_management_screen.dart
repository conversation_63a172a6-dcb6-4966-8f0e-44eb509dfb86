import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/providers/admin_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/admin_screens/widgets/admin_form_dialog.dart';

/// شاشة إدارة حسابات المسؤولين المعاد هيكلتها.
class AdminsManagementScreen extends ConsumerWidget {
  const AdminsManagementScreen({super.key});

  /// دالة لعرض ديالوج تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    WidgetRef ref,
    UserModel admin,
  ) async {
    final confirmed = await showConfirmationDialog(
      context: context,
      title: 'تأكيد الحذف',
      content:
          'هل أنت متأكد أنك تريد حذف المسؤول "${admin.name}"؟ هذا الإجراء لا يمكن التراجع عنه.',
    );

    if (confirmed) {
      try {
        await ref.read(firebaseServiceProvider).deleteAdmin(admin.id);
        showSuccessSnackBar(context, 'تم حذف المسؤول بنجاح.');
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final adminsAsyncValue = ref.watch(adminsStreamProvider);
    final filteredAdmins = ref.watch(filteredAdminsProvider);

    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                labelText: 'بحث عن مسؤول...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                ref.read(adminSearchQueryProvider.notifier).state = value;
              },
            ),
          ),
          Expanded(
            child: adminsAsyncValue.when(
              data: (_) {
                if (filteredAdmins.isEmpty) {
                  return const Center(
                      child: Text('لم يتم العثور على نتائج.'));
                }
                return ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  itemCount: filteredAdmins.length,
                  itemBuilder: (context, index) {
                    final admin = filteredAdmins[index];
                    return CustomCard(
                      child: ListTile(
                        leading: const Icon(Icons.admin_panel_settings),
                        title: Text(admin.name),
                        subtitle: Text(admin.email),
                        trailing: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              icon:
                                  const Icon(Icons.edit, color: Colors.blue),
                              onPressed: () {
                                showDialog(
                                  context: context,
                                  builder: (_) => AdminFormDialog(admin: admin),
                                );
                              },
                            ),
                            IconButton(
                              icon:
                                  const Icon(Icons.delete, color: Colors.red),
                              onPressed: () {
                                _showDeleteConfirmationDialog(
                                    context, ref, admin);
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          showDialog(
            context: context,
            builder: (_) => const AdminFormDialog(),
          );
        },
        tooltip: 'إضافة مسؤول جديد',
        child: const Icon(Icons.add),
      ),
    );
  }
}
