import 'package:cloud_firestore/cloud_firestore.dart';

/// تعداد أنواع الامتحانات المختلفة في النظام التعليمي اليمني
enum ExamType {
  monthly,    // امتحان شهري
  midterm,    // امتحان نصف فصلي
  final,      // امتحان نهائي
  makeup      // امتحان إعادة
}

/// تعداد حالات الامتحان
enum ExamStatus {
  scheduled,  // مجدول
  ongoing,    // جاري
  completed,  // مكتمل
  cancelled   // ملغي
}

/// تعداد أولوية المواضيع في المنهج
enum TopicPriority {
  critical,   // مهم جداً
  important,  // مهم
  normal      // عادي
}

/// تعداد أنواع النصائح الدراسية
enum TipType {
  general,    // نصيحة عامة
  specific    // نصيحة مخصصة
}

/// تعداد أنواع الحوادث أثناء الامتحان
enum IncidentType {
  absence,    // غياب
  cheating,   // غش
  illness,    // مرض
  technical,  // مشكلة تقنية
  other       // أخرى
}

/// امتدادات مفيدة لتعداد أنواع الامتحانات
extension ExamTypeExtension on ExamType {
  /// الاسم العربي لنوع الامتحان
  String get arabicName {
    switch (this) {
      case ExamType.monthly:
        return 'امتحان شهري';
      case ExamType.midterm:
        return 'امتحان نصف فصلي';
      case ExamType.final:
        return 'امتحان نهائي';
      case ExamType.makeup:
        return 'امتحان إعادة';
    }
  }

  /// الوزن النسبي لنوع الامتحان في المعدل النهائي
  double get weight {
    switch (this) {
      case ExamType.monthly:
        return 0.2; // 20%
      case ExamType.midterm:
        return 0.3; // 30%
      case ExamType.final:
        return 0.5; // 50%
      case ExamType.makeup:
        return 0.0; // لا يحسب في المعدل
    }
  }
}

/// امتدادات مفيدة لتعداد حالات الامتحان
extension ExamStatusExtension on ExamStatus {
  /// الاسم العربي لحالة الامتحان
  String get arabicName {
    switch (this) {
      case ExamStatus.scheduled:
        return 'مجدول';
      case ExamStatus.ongoing:
        return 'جاري';
      case ExamStatus.completed:
        return 'مكتمل';
      case ExamStatus.cancelled:
        return 'ملغي';
    }
  }
}

/// امتدادات مفيدة لتعداد أولوية المواضيع
extension TopicPriorityExtension on TopicPriority {
  /// الاسم العربي لأولوية الموضوع
  String get arabicName {
    switch (this) {
      case TopicPriority.critical:
        return 'مهم جداً';
      case TopicPriority.important:
        return 'مهم';
      case TopicPriority.normal:
        return 'عادي';
    }
  }
}

/// امتدادات مفيدة لتعداد أنواع الحوادث
extension IncidentTypeExtension on IncidentType {
  /// الاسم العربي لنوع الحادث
  String get arabicName {
    switch (this) {
      case IncidentType.absence:
        return 'غياب';
      case IncidentType.cheating:
        return 'غش';
      case IncidentType.illness:
        return 'مرض';
      case IncidentType.technical:
        return 'مشكلة تقنية';
      case IncidentType.other:
        return 'أخرى';
    }
  }
}

/// نموذج الامتحان الأساسي
/// يحتوي على جميع المعلومات الأساسية للامتحان
class ExamModel {
  final String id;
  final String name;              // اسم الامتحان مثل "امتحان الفصل الأول 2024"
  final String academicYear;      // السنة الدراسية مثل "2023-2024"
  final String semester;          // الفصل الدراسي مثل "الفصل الأول"
  final ExamType type;            // نوع الامتحان
  final DateTime startDate;       // تاريخ بداية الامتحانات
  final DateTime endDate;         // تاريخ نهاية الامتحانات
  final List<String> classIds;    // معرفات الصفوف المشاركة
  final ExamStatus status;        // حالة الامتحان
  final String? description;      // وصف إضافي للامتحان
  final DateTime createdAt;       // تاريخ الإنشاء
  final String createdBy;         // معرف منشئ الامتحان
  final DateTime? updatedAt;      // تاريخ آخر تحديث
  final String? updatedBy;        // معرف آخر محدث

  const ExamModel({
    required this.id,
    required this.name,
    required this.academicYear,
    required this.semester,
    required this.type,
    required this.startDate,
    required this.endDate,
    required this.classIds,
    required this.status,
    this.description,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  /// إنشاء نموذج امتحان من مستند Firestore
  factory ExamModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ExamModel(
      id: doc.id,
      name: data['name'] as String? ?? '',
      academicYear: data['academicYear'] as String? ?? '',
      semester: data['semester'] as String? ?? '',
      type: ExamType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => ExamType.monthly,
      ),
      startDate: (data['startDate'] as Timestamp).toDate(),
      endDate: (data['endDate'] as Timestamp).toDate(),
      classIds: List<String>.from(data['classIds'] ?? []),
      status: ExamStatus.values.firstWhere(
        (e) => e.toString() == data['status'],
        orElse: () => ExamStatus.scheduled,
      ),
      description: data['description'] as String?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate() 
          : null,
      updatedBy: data['updatedBy'] as String?,
    );
  }

  /// تحويل نموذج الامتحان إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'academicYear': academicYear,
      'semester': semester,
      'type': type.toString(),
      'startDate': Timestamp.fromDate(startDate),
      'endDate': Timestamp.fromDate(endDate),
      'classIds': classIds,
      'status': status.toString(),
      'description': description,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updatedBy': updatedBy,
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  ExamModel copyWith({
    String? id,
    String? name,
    String? academicYear,
    String? semester,
    ExamType? type,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? classIds,
    ExamStatus? status,
    String? description,
    DateTime? createdAt,
    String? createdBy,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return ExamModel(
      id: id ?? this.id,
      name: name ?? this.name,
      academicYear: academicYear ?? this.academicYear,
      semester: semester ?? this.semester,
      type: type ?? this.type,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      classIds: classIds ?? this.classIds,
      status: status ?? this.status,
      description: description ?? this.description,
      createdAt: createdAt ?? this.createdAt,
      createdBy: createdBy ?? this.createdBy,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }

  /// التحقق من صحة بيانات الامتحان
  bool get isValid {
    return name.isNotEmpty &&
           academicYear.isNotEmpty &&
           semester.isNotEmpty &&
           classIds.isNotEmpty &&
           startDate.isBefore(endDate) &&
           createdBy.isNotEmpty;
  }

  /// حساب مدة الامتحان بالأيام
  int get durationInDays {
    return endDate.difference(startDate).inDays + 1;
  }

  /// التحقق من كون الامتحان جاري حالياً
  bool get isOngoing {
    final now = DateTime.now();
    return status == ExamStatus.ongoing ||
           (now.isAfter(startDate) && now.isBefore(endDate.add(const Duration(days: 1))));
  }

  /// التحقق من كون الامتحان قادم
  bool get isUpcoming {
    final now = DateTime.now();
    return status == ExamStatus.scheduled && now.isBefore(startDate);
  }

  /// التحقق من كون الامتحان منتهي
  bool get isCompleted {
    final now = DateTime.now();
    return status == ExamStatus.completed || now.isAfter(endDate);
  }

  /// حساب الأيام المتبقية للامتحان
  int get daysRemaining {
    if (isCompleted) return 0;
    final now = DateTime.now();
    return startDate.difference(now).inDays;
  }

  @override
  String toString() {
    return 'ExamModel(id: $id, name: $name, type: ${type.arabicName}, status: ${status.arabicName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExamModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
