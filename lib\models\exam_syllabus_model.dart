import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/exam_model.dart';

/// نموذج موضوع دراسي في منهج الامتحان
/// يمثل موضوع واحد يجب على الطالب مراجعته للامتحان
class StudyTopic {
  final String id;
  final String title;             // عنوان الموضوع مثل "الجبر - المعادلات الخطية"
  final String description;       // وصف تفصيلي للموضوع
  final TopicPriority priority;   // أولوية الموضوع (مهم جداً، مهم، عادي)
  final int estimatedHours;       // ساعات المراجعة المقترحة
  final List<String> resources;   // المراجع والمصادر للمراجعة
  final List<String> keyPoints;   // النقاط الرئيسية في الموضوع
  final String? chapterReference; // مرجع الفصل في الكتاب
  final String? pageReference;    // مرجع الصفحات في الكتاب
  final DateTime createdAt;       // تاريخ الإضافة
  final String createdBy;         // معرف المعلم الذي أضاف الموضوع

  const StudyTopic({
    required this.id,
    required this.title,
    required this.description,
    required this.priority,
    required this.estimatedHours,
    required this.resources,
    required this.keyPoints,
    this.chapterReference,
    this.pageReference,
    required this.createdAt,
    required this.createdBy,
  });

  /// إنشاء موضوع دراسي من مستند Firestore
  factory StudyTopic.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StudyTopic(
      id: doc.id,
      title: data['title'] as String? ?? '',
      description: data['description'] as String? ?? '',
      priority: TopicPriority.values.firstWhere(
        (e) => e.toString() == data['priority'],
        orElse: () => TopicPriority.normal,
      ),
      estimatedHours: data['estimatedHours'] as int? ?? 2,
      resources: List<String>.from(data['resources'] ?? []),
      keyPoints: List<String>.from(data['keyPoints'] ?? []),
      chapterReference: data['chapterReference'] as String?,
      pageReference: data['pageReference'] as String?,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
    );
  }

  /// تحويل الموضوع الدراسي إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'priority': priority.toString(),
      'estimatedHours': estimatedHours,
      'resources': resources,
      'keyPoints': keyPoints,
      'chapterReference': chapterReference,
      'pageReference': pageReference,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
    };
  }

  /// التحقق من صحة بيانات الموضوع
  bool get isValid {
    return title.isNotEmpty &&
           description.isNotEmpty &&
           estimatedHours > 0 &&
           createdBy.isNotEmpty;
  }

  @override
  String toString() {
    return 'StudyTopic(id: $id, title: $title, priority: ${priority.arabicName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudyTopic && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج نصيحة دراسية من المعلم
/// يحتوي على نصائح وإرشادات للطلاب حول كيفية المراجعة
class StudyTip {
  final String id;
  final String title;             // عنوان النصيحة
  final String content;           // محتوى النصيحة
  final TipType type;             // نوع النصيحة (عامة أم مخصصة)
  final String? targetStudentId;  // معرف الطالب المستهدف (للنصائح المخصصة)
  final String? targetStudentName; // اسم الطالب المستهدف
  final TipCategory category;     // فئة النصيحة
  final DateTime createdAt;       // تاريخ الإضافة
  final String createdBy;         // معرف المعلم

  const StudyTip({
    required this.id,
    required this.title,
    required this.content,
    required this.type,
    this.targetStudentId,
    this.targetStudentName,
    required this.category,
    required this.createdAt,
    required this.createdBy,
  });

  /// إنشاء نصيحة دراسية من مستند Firestore
  factory StudyTip.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StudyTip(
      id: doc.id,
      title: data['title'] as String? ?? '',
      content: data['content'] as String? ?? '',
      type: TipType.values.firstWhere(
        (e) => e.toString() == data['type'],
        orElse: () => TipType.general,
      ),
      targetStudentId: data['targetStudentId'] as String?,
      targetStudentName: data['targetStudentName'] as String?,
      category: TipCategory.values.firstWhere(
        (e) => e.toString() == data['category'],
        orElse: () => TipCategory.general,
      ),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
    );
  }

  /// تحويل النصيحة الدراسية إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'content': content,
      'type': type.toString(),
      'targetStudentId': targetStudentId,
      'targetStudentName': targetStudentName,
      'category': category.toString(),
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
    };
  }

  /// التحقق من صحة بيانات النصيحة
  bool get isValid {
    return title.isNotEmpty &&
           content.isNotEmpty &&
           createdBy.isNotEmpty &&
           (type == TipType.general || targetStudentId != null);
  }

  @override
  String toString() {
    return 'StudyTip(id: $id, title: $title, type: ${type.name})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StudyTip && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// تعداد فئات النصائح الدراسية
enum TipCategory {
  general,        // نصائح عامة
  timeManagement, // إدارة الوقت
  studyMethod,    // طرق الدراسة
  examStrategy,   // استراتيجيات الامتحان
  motivation,     // تحفيز
  health          // صحة ونوم
}

/// امتدادات مفيدة لفئات النصائح
extension TipCategoryExtension on TipCategory {
  /// الاسم العربي لفئة النصيحة
  String get arabicName {
    switch (this) {
      case TipCategory.general:
        return 'نصائح عامة';
      case TipCategory.timeManagement:
        return 'إدارة الوقت';
      case TipCategory.studyMethod:
        return 'طرق الدراسة';
      case TipCategory.examStrategy:
        return 'استراتيجيات الامتحان';
      case TipCategory.motivation:
        return 'تحفيز';
      case TipCategory.health:
        return 'صحة ونوم';
    }
  }
}

/// نموذج منهج الامتحان الكامل
/// يحتوي على جميع المواضيع والنصائح لامتحان مادة معينة
class ExamSyllabus {
  final String id;
  final String examId;            // معرف الامتحان
  final String subjectId;         // معرف المادة
  final String subjectName;       // اسم المادة
  final String classId;           // معرف الصف
  final String className;         // اسم الصف
  final String teacherId;         // معرف المعلم
  final String teacherName;       // اسم المعلم
  final List<StudyTopic> topics;  // قائمة المواضيع المطلوبة
  final List<StudyTip> tips;      // قائمة النصائح الدراسية
  final String? generalNotes;     // ملاحظات عامة من المعلم
  final int totalEstimatedHours;  // إجمالي ساعات المراجعة المقترحة
  final DateTime examDate;        // تاريخ الامتحان
  final bool isPublished;         // هل تم نشر المنهج للطلاب
  final DateTime createdAt;       // تاريخ الإنشاء
  final String createdBy;         // منشئ المنهج
  final DateTime? updatedAt;      // تاريخ آخر تحديث
  final String? updatedBy;        // آخر محدث

  const ExamSyllabus({
    required this.id,
    required this.examId,
    required this.subjectId,
    required this.subjectName,
    required this.classId,
    required this.className,
    required this.teacherId,
    required this.teacherName,
    required this.topics,
    required this.tips,
    this.generalNotes,
    required this.totalEstimatedHours,
    required this.examDate,
    required this.isPublished,
    required this.createdAt,
    required this.createdBy,
    this.updatedAt,
    this.updatedBy,
  });

  /// إنشاء منهج امتحان من مستند Firestore
  factory ExamSyllabus.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return ExamSyllabus(
      id: doc.id,
      examId: data['examId'] as String? ?? '',
      subjectId: data['subjectId'] as String? ?? '',
      subjectName: data['subjectName'] as String? ?? '',
      classId: data['classId'] as String? ?? '',
      className: data['className'] as String? ?? '',
      teacherId: data['teacherId'] as String? ?? '',
      teacherName: data['teacherName'] as String? ?? '',
      topics: [], // سيتم تحميلها من مجموعة فرعية
      tips: [],   // سيتم تحميلها من مجموعة فرعية
      generalNotes: data['generalNotes'] as String?,
      totalEstimatedHours: data['totalEstimatedHours'] as int? ?? 0,
      examDate: (data['examDate'] as Timestamp).toDate(),
      isPublished: data['isPublished'] as bool? ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      createdBy: data['createdBy'] as String? ?? '',
      updatedAt: data['updatedAt'] != null 
          ? (data['updatedAt'] as Timestamp).toDate() 
          : null,
      updatedBy: data['updatedBy'] as String?,
    );
  }

  /// تحويل منهج الامتحان إلى Map للحفظ في Firestore
  Map<String, dynamic> toFirestore() {
    return {
      'examId': examId,
      'subjectId': subjectId,
      'subjectName': subjectName,
      'classId': classId,
      'className': className,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'generalNotes': generalNotes,
      'totalEstimatedHours': totalEstimatedHours,
      'examDate': Timestamp.fromDate(examDate),
      'isPublished': isPublished,
      'createdAt': Timestamp.fromDate(createdAt),
      'createdBy': createdBy,
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
      'updatedBy': updatedBy,
    };
  }

  /// حساب إجمالي ساعات المراجعة من المواضيع
  int get calculatedTotalHours {
    return topics.fold(0, (sum, topic) => sum + topic.estimatedHours);
  }

  /// حساب عدد المواضيع حسب الأولوية
  Map<TopicPriority, int> get topicsByPriority {
    final Map<TopicPriority, int> result = {};
    for (final priority in TopicPriority.values) {
      result[priority] = topics.where((topic) => topic.priority == priority).length;
    }
    return result;
  }

  /// التحقق من صحة بيانات المنهج
  bool get isValid {
    return examId.isNotEmpty &&
           subjectId.isNotEmpty &&
           classId.isNotEmpty &&
           teacherId.isNotEmpty &&
           topics.isNotEmpty &&
           createdBy.isNotEmpty;
  }

  @override
  String toString() {
    return 'ExamSyllabus(id: $id, subject: $subjectName, class: $className, topics: ${topics.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ExamSyllabus && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
