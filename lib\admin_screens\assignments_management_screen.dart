import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/admin_screens/widgets/assignment_form_dialog.dart';
import 'package:school_management_system/models/assignment_model.dart';
import 'package:school_management_system/providers/assignment_providers.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة إدارة الواجبات المعاد هيكلتها باستخدام Riverpod
class AssignmentsManagementScreen extends ConsumerWidget {
  const AssignmentsManagementScreen({super.key});

  void _showDeleteConfirmation(
    BuildContext context,
    WidgetRef ref,
    String assignmentId,
  ) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من رغبتك في حذف هذا الواجب؟'),
          actions: <Widget>[
            TextButton(
              child: const Text('إلغاء'),
              onPressed: () => Navigator.of(context).pop(),
            ),
            TextButton(
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
              onPressed: () async {
                try {
                  await ref
                      .read(firebaseServiceProvider)
                      .deleteAssignment(assignmentId);
                  if (context.mounted) {
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم حذف الواجب بنجاح')),
                    );
                  }
                } catch (e) {
                  if (context.mounted) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('خطأ في الحذف: ${e.toString()}')),
                    );
                  }
                }
              },
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assignmentsAsyncValue = ref.watch(assignmentsStreamProvider);
    final filteredAssignments = ref.watch(adminFilteredAssignmentsProvider);
    final selectedPriorityFilter = ref.watch(adminPriorityFilterProvider);
    final selectedStatusFilter = ref.watch(adminStatusFilterProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الواجبات'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          IconButton(
            icon: const Icon(Icons.analytics),
            tooltip: 'عرض الإحصائيات',
            onPressed: () => _showStatisticsDialog(context, ref),
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.download),
            tooltip: 'تصدير البيانات',
            onPressed: () => _exportAssignments(context, ref),
          ),
        ],
      ),
      body: Column(
        children: [
          // شريط البحث والتصفية المحسن
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: Colors.grey.shade50,
              border: Border(bottom: BorderSide(color: Colors.grey.shade200)),
            ),
            child: Column(
              children: [
                // شريط البحث
                TextField(
                  onChanged: (value) {
                    ref
                        .read(adminAssignmentSearchQueryProvider.notifier)
                        .state = value;
                  },
                  decoration: InputDecoration(
                    labelText: 'ابحث عن واجب بالعنوان أو المادة أو المعلم...',
                    prefixIcon: const Icon(Icons.search),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                ),

                const SizedBox(height: 12),

                // مرشحات الأولوية والحالة
                Row(
                  children: [
                    // مرشح الأولوية
                    Expanded(
                      child: DropdownButtonFormField<AssignmentPriority?>(
                        value: selectedPriorityFilter,
                        decoration: InputDecoration(
                          labelText: 'تصفية حسب الأولوية',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        items: [
                          const DropdownMenuItem<AssignmentPriority?>(
                            value: null,
                            child: Text('جميع الأولويات'),
                          ),
                          ...AssignmentPriority.values.map(
                            (priority) => DropdownMenuItem<AssignmentPriority?>(
                              value: priority,
                              child: Row(
                                children: [
                                  Icon(
                                    priority.icon,
                                    color: priority.color,
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(priority.arabicName),
                                ],
                              ),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          ref.read(adminPriorityFilterProvider.notifier).state =
                              value;
                        },
                      ),
                    ),

                    const SizedBox(width: 12),

                    // مرشح الحالة
                    Expanded(
                      child: DropdownButtonFormField<AssignmentStatus?>(
                        value: selectedStatusFilter,
                        decoration: InputDecoration(
                          labelText: 'تصفية حسب الحالة',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          filled: true,
                          fillColor: Colors.white,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        items: [
                          const DropdownMenuItem<AssignmentStatus?>(
                            value: null,
                            child: Text('جميع الحالات'),
                          ),
                          ...AssignmentStatus.values.map(
                            (status) => DropdownMenuItem<AssignmentStatus?>(
                              value: status,
                              child: Row(
                                children: [
                                  Icon(
                                    _getStatusIcon(status),
                                    color: _getStatusColor(status),
                                    size: 16,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(status.arabicName),
                                ],
                              ),
                            ),
                          ),
                        ],
                        onChanged: (value) {
                          ref.read(adminStatusFilterProvider.notifier).state =
                              value;
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          // قائمة الواجبات المحسنة
          Expanded(
            child: assignmentsAsyncValue.when(
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: 'خطأ: $err'),
              data: (assignments) {
                if (assignments.isEmpty) {
                  return _buildEmptyState(context);
                }
                if (filteredAssignments.isEmpty) {
                  return _buildNoResultsState(context);
                }
                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: filteredAssignments.length,
                  itemBuilder: (context, index) {
                    final assignment = filteredAssignments[index];
                    return _buildEnhancedAssignmentCard(
                      context,
                      ref,
                      assignment,
                      index,
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed:
            () => showDialog(
              context: context,
              builder: (_) => const AssignmentFormDialog(),
            ),
        tooltip: 'إضافة واجب جديد',
        icon: const Icon(Icons.add),
        label: const Text('إضافة واجب'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
    );
  }

  /// بناء بطاقة واجب محسنة للإدارة
  Widget _buildEnhancedAssignmentCard(
    BuildContext context,
    WidgetRef ref,
    AssignmentModel assignment,
    int index,
  ) {
    final now = DateTime.now();
    final isOverdue = now.isAfter(assignment.dueDate);
    final timeRemaining = assignment.dueDate.difference(now);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        elevation: 3,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Colors.white,
                assignment.priorityColor.withAlpha((255 * 0.02).round()),
              ],
            ),
            border: Border(
              right: BorderSide(color: assignment.priorityColor, width: 4),
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // الصف العلوي: الأولوية والحالة والإجراءات
                Row(
                  children: [
                    // شارة الأولوية
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: assignment.priorityColor,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            assignment.priorityIcon,
                            color: Colors.white,
                            size: 14,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            assignment.priority.arabicName,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 12),

                    // شارة الحالة
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 10,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getStatusColor(
                          assignment.currentStatus,
                        ).withAlpha((255 * 0.1).round()),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(
                          color: _getStatusColor(assignment.currentStatus),
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getStatusIcon(assignment.currentStatus),
                            color: _getStatusColor(assignment.currentStatus),
                            size: 14,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            assignment.currentStatus.arabicName,
                            style: TextStyle(
                              color: _getStatusColor(assignment.currentStatus),
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const Spacer(),

                    // أزرار الإجراءات
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // زر التعديل
                        IconButton(
                          icon: const Icon(Icons.edit, color: Colors.blue),
                          tooltip: 'تعديل الواجب',
                          onPressed:
                              () => showDialog(
                                context: context,
                                builder:
                                    (_) => AssignmentFormDialog(
                                      assignment: assignment,
                                    ),
                              ),
                        ),
                        // زر الحذف
                        IconButton(
                          icon: const Icon(Icons.delete, color: Colors.red),
                          tooltip: 'حذف الواجب',
                          onPressed:
                              () => _showDeleteConfirmation(
                                context,
                                ref,
                                assignment.id,
                              ),
                        ),
                        // زر المزيد
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert),
                          onSelected:
                              (value) => _handleMenuAction(
                                context,
                                ref,
                                assignment,
                                value,
                              ),
                          itemBuilder:
                              (context) => [
                                const PopupMenuItem(
                                  value: 'duplicate',
                                  child: Row(
                                    children: [
                                      Icon(Icons.copy, size: 16),
                                      SizedBox(width: 8),
                                      Text('نسخ الواجب'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'submissions',
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.assignment_turned_in,
                                        size: 16,
                                      ),
                                      SizedBox(width: 8),
                                      Text('عرض التسليمات'),
                                    ],
                                  ),
                                ),
                                const PopupMenuItem(
                                  value: 'export',
                                  child: Row(
                                    children: [
                                      Icon(Icons.download, size: 16),
                                      SizedBox(width: 8),
                                      Text('تصدير البيانات'),
                                    ],
                                  ),
                                ),
                              ],
                        ),
                      ],
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // العنوان والوصف
                Text(
                  assignment.title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),

                if (assignment.description.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    assignment.description,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                      height: 1.4,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],

                const SizedBox(height: 16),

                // معلومات المادة والمعلم
                Row(
                  children: [
                    Icon(Icons.subject, size: 16, color: Colors.blue.shade600),
                    const SizedBox(width: 6),
                    Text(
                      assignment.subjectName,
                      style: TextStyle(
                        color: Colors.blue.shade600,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (assignment.teacherName != null) ...[
                      const SizedBox(width: 16),
                      Icon(
                        Icons.person,
                        size: 16,
                        color: Colors.green.shade600,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          assignment.teacherName!,
                          style: TextStyle(
                            color: Colors.green.shade600,
                            fontWeight: FontWeight.w600,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ],
                ),

                const SizedBox(height: 12),

                // معلومات التاريخ والوقت
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color:
                        isOverdue
                            ? Colors.red.shade50
                            : timeRemaining.inDays <= 3
                            ? Colors.orange.shade50
                            : Colors.green.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color:
                          isOverdue
                              ? Colors.red.shade200
                              : timeRemaining.inDays <= 3
                              ? Colors.orange.shade200
                              : Colors.green.shade200,
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.schedule,
                        size: 16,
                        color:
                            isOverdue
                                ? Colors.red.shade700
                                : timeRemaining.inDays <= 3
                                ? Colors.orange.shade700
                                : Colors.green.shade700,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تاريخ التسليم: ${DateFormat.yMMMMEEEEd('ar').format(assignment.dueDate)}',
                              style: TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.w600,
                                color:
                                    isOverdue
                                        ? Colors.red.shade700
                                        : timeRemaining.inDays <= 3
                                        ? Colors.orange.shade700
                                        : Colors.green.shade700,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              isOverdue
                                  ? 'متأخر بـ ${timeRemaining.inDays.abs()} يوم'
                                  : timeRemaining.inDays > 0
                                  ? 'متبقي ${timeRemaining.inDays} يوم'
                                  : timeRemaining.inHours > 0
                                  ? 'متبقي ${timeRemaining.inHours} ساعة'
                                  : 'متبقي ${timeRemaining.inMinutes} دقيقة',
                              style: TextStyle(
                                fontSize: 11,
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // عدد المرفقات
                      if (assignment.attachments.isNotEmpty)
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: Colors.blue.withAlpha((255 * 0.1).round()),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.attach_file,
                                color: Colors.blue.shade700,
                                size: 14,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                '${assignment.attachments.length}',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء حالة فارغة
  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد واجبات حالياً',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة واجب جديد',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed:
                () => showDialog(
                  context: context,
                  builder: (_) => const AssignmentFormDialog(),
                ),
            icon: const Icon(Icons.add),
            label: const Text('إضافة واجب جديد'),
          ),
        ],
      ),
    );
  }

  /// بناء حالة عدم وجود نتائج
  Widget _buildNoResultsState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.search_off, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لم يتم العثور على نتائج',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جرب تغيير معايير البحث أو التصفية',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// الحصول على أيقونة الحالة
  IconData _getStatusIcon(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return Icons.pending_actions;
      case AssignmentStatus.submitted:
        return Icons.check_circle;
      case AssignmentStatus.overdue:
        return Icons.warning;
      case AssignmentStatus.graded:
        return Icons.grade;
      case AssignmentStatus.returned:
        return Icons.refresh;
    }
  }

  /// الحصول على لون الحالة
  Color _getStatusColor(AssignmentStatus status) {
    switch (status) {
      case AssignmentStatus.pending:
        return Colors.orange;
      case AssignmentStatus.submitted:
        return Colors.green;
      case AssignmentStatus.overdue:
        return Colors.red;
      case AssignmentStatus.graded:
        return Colors.blue;
      case AssignmentStatus.returned:
        return Colors.purple;
    }
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(
    BuildContext context,
    WidgetRef ref,
    AssignmentModel assignment,
    String action,
  ) {
    switch (action) {
      case 'duplicate':
        _duplicateAssignment(context, ref, assignment);
        break;
      case 'submissions':
        _viewSubmissions(context, assignment);
        break;
      case 'export':
        _exportSingleAssignment(context, assignment);
        break;
    }
  }

  /// نسخ الواجب
  void _duplicateAssignment(
    BuildContext context,
    WidgetRef ref,
    AssignmentModel assignment,
  ) {
    final duplicatedAssignment = assignment.copyWith(
      id: '', // سيتم إنشاء ID جديد
      title: '${assignment.title} (نسخة)',
      dueDate: DateTime.now().add(const Duration(days: 7)),
    );

    showDialog(
      context: context,
      builder: (_) => AssignmentFormDialog(assignment: duplicatedAssignment),
    );
  }

  /// عرض التسليمات
  void _viewSubmissions(BuildContext context, AssignmentModel assignment) {
    // TODO: تنفيذ شاشة عرض التسليمات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('ميزة عرض التسليمات قيد التطوير')),
    );
  }

  /// تصدير واجب واحد
  void _exportSingleAssignment(
    BuildContext context,
    AssignmentModel assignment,
  ) {
    // TODO: تنفيذ تصدير واجب واحد
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التصدير قيد التطوير')));
  }

  /// عرض حوار الإحصائيات
  void _showStatisticsDialog(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ حوار الإحصائيات
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إحصائيات الواجبات'),
            content: const Text('ميزة الإحصائيات قيد التطوير'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  /// تصدير جميع الواجبات
  void _exportAssignments(BuildContext context, WidgetRef ref) {
    // TODO: تنفيذ تصدير جميع الواجبات
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التصدير قيد التطوير')));
  }
}
