import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/services/firebase_service.dart';

//======================================================================
// Providers for Class Management
// (مزودات الخدمة الخاصة بإدارة الفصول والطلاب داخلها)
//======================================================================

/// مزود Stream لجلب قائمة جميع الفصول الدراسية.
/// يعتمد على `firebaseServiceProvider` للوصول إلى `FirebaseService`.
final classesStreamProvider = StreamProvider.autoDispose<List<ClassModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getClassesStream();
});

/// مزود حالة لإدارة وتخزين معرّف الفصل الذي تم اختياره حاليًا في واجهة المستخدم.
/// قيمته الأولية `null`، مما يعني أنه لم يتم اختيار أي فصل بعد.
final selectedClassIdProvider = StateProvider<String?>((ref) => null);

/// مزود لجلب اسم الفصل المختار
final selectedClassNameProvider = Provider.autoDispose<String>((ref) {
  final selectedClassId = ref.watch(selectedClassIdProvider);
  final classesAsyncValue = ref.watch(classesStreamProvider);

  if (selectedClassId == null || classesAsyncValue.asData == null) {
    return 'الفصل';
  }

  final classes = classesAsyncValue.asData!.value;
  final selectedClass =
      classes.firstWhere((c) => c.id == selectedClassId, orElse: () => ClassModel(id: '', name: 'الفصل'));
  return selectedClass.name;
});

/// مزود Stream لجلب قائمة الطلاب المسجلين في فصل معين.
/// يستخدم `family` لتمرير معرّف الفصل (`classId`) كمعامل.
/// إذا كان `classId` فارغًا، فإنه يعيد stream فارغًا.
final classStudentsStreamProvider = StreamProvider.autoDispose.family<List<StudentModel>, String?>((ref, classId) {
  if (classId == null) {
    return Stream.value([]); // إرجاع قائمة فارغة إذا لم يتم اختيار فصل
  }
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getStudentsByClassStream(classId);
});
