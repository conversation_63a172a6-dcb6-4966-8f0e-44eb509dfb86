
import 'package:flutter/material.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/admin_screens/widgets/add_class_dialog.dart';
import 'package:school_management_system/admin_screens/widgets/edit_class_dialog.dart';

Future<void> showAddClassDialog(BuildContext context) {
  return showDialog(
    context: context,
    builder: (context) => const AddClassDialog(),
  );
}

Future<void> showEditClassDialog(BuildContext context, ClassModel classModel) {
  return showDialog(
    context: context,
    builder: (context) => EditClassDialog(classModel: classModel),
  );
}
