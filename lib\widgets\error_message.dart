
import 'package:flutter/material.dart';

/// ويدجت لعرض رسالة خطأ بشكل منسق
class ErrorMessage extends StatelessWidget {
  final String message; // نص رسالة الخطأ

  const ErrorMessage({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: Colors.red.shade700,
              size: 50,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ', // عنوان ثابت
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                color: Colors.red.shade800,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              message, // عرض تفاصيل الخطأ
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
