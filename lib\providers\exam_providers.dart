import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/exam_model.dart';
import 'package:school_management_system/models/exam_schedule_model.dart';
import 'package:school_management_system/models/exam_syllabus_model.dart';
import 'package:school_management_system/models/grade_entry_model.dart';
import 'package:school_management_system/models/exam_analytics_model.dart';
import 'package:school_management_system/providers/services_provider.dart';

//======================================================================
// Providers for Exam Management
//======================================================================

/// مزود تدفق جميع الامتحانات
final allExamsStreamProvider = StreamProvider.autoDispose<List<ExamModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getAllExamsStream();
});

/// مزود تدفق الامتحانات الجارية
final ongoingExamsStreamProvider = StreamProvider.autoDispose<List<ExamModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getOngoingExamsStream();
});

/// مزود تدفق الامتحانات القادمة
final upcomingExamsStreamProvider = StreamProvider.autoDispose<List<ExamModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getUpcomingExamsStream();
});

/// مزود تدفق الامتحانات حسب السنة الدراسية
final examsByAcademicYearProvider = StreamProvider.autoDispose.family<List<ExamModel>, String>((ref, academicYear) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getExamsByAcademicYear(academicYear);
});

/// مزود امتحان واحد بمعرفه
final examByIdProvider = FutureProvider.autoDispose.family<ExamModel?, String>((ref, examId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getExamById(examId);
});

/// مزود حالة إنشاء امتحان جديد
final examCreationStateProvider = StateNotifierProvider.autoDispose<ExamCreationNotifier, ExamCreationState>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return ExamCreationNotifier(firebaseService);
});

/// مزود حالة تحديث امتحان
final examUpdateStateProvider = StateNotifierProvider.autoDispose<ExamUpdateNotifier, ExamUpdateState>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return ExamUpdateNotifier(firebaseService);
});

/// مزود البحث في الامتحانات
final examSearchQueryProvider = StateProvider.autoDispose<String>((ref) => '');

/// مزود الامتحانات المفلترة حسب البحث
final filteredExamsProvider = Provider.autoDispose<List<ExamModel>>((ref) {
  final exams = ref.watch(allExamsStreamProvider).asData?.value ?? [];
  final searchQuery = ref.watch(examSearchQueryProvider).toLowerCase();

  if (searchQuery.isEmpty) {
    return exams;
  }

  return exams.where((exam) {
    return exam.name.toLowerCase().contains(searchQuery) ||
           exam.academicYear.toLowerCase().contains(searchQuery) ||
           exam.semester.toLowerCase().contains(searchQuery) ||
           exam.type.arabicName.toLowerCase().contains(searchQuery);
  }).toList();
});

/// مزود إحصائيات الامتحانات السريعة
final examStatsProvider = Provider.autoDispose<ExamStats>((ref) {
  final allExams = ref.watch(allExamsStreamProvider).asData?.value ?? [];
  final ongoingExams = ref.watch(ongoingExamsStreamProvider).asData?.value ?? [];
  final upcomingExams = ref.watch(upcomingExamsStreamProvider).asData?.value ?? [];

  return ExamStats(
    totalExams: allExams.length,
    ongoingExams: ongoingExams.length,
    upcomingExams: upcomingExams.length,
    completedExams: allExams.where((exam) => exam.isCompleted).length,
  );
});

//======================================================================
// State Notifiers for Exam Management
//======================================================================

/// حالة إنشاء امتحان جديد
class ExamCreationState {
  final bool isLoading;
  final String? error;
  final String? createdExamId;

  const ExamCreationState({
    this.isLoading = false,
    this.error,
    this.createdExamId,
  });

  ExamCreationState copyWith({
    bool? isLoading,
    String? error,
    String? createdExamId,
  }) {
    return ExamCreationState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      createdExamId: createdExamId ?? this.createdExamId,
    );
  }
}

/// مدير حالة إنشاء الامتحانات
class ExamCreationNotifier extends StateNotifier<ExamCreationState> {
  final dynamic _firebaseService;

  ExamCreationNotifier(this._firebaseService) : super(const ExamCreationState());

  /// إنشاء امتحان جديد
  Future<void> createExam(ExamModel exam) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final examId = await _firebaseService.createExam(exam);
      state = state.copyWith(
        isLoading: false,
        createdExamId: examId,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في إنشاء الامتحان: ${e.toString()}',
      );
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    state = const ExamCreationState();
  }
}

/// حالة تحديث امتحان
class ExamUpdateState {
  final bool isLoading;
  final String? error;
  final bool isUpdated;

  const ExamUpdateState({
    this.isLoading = false,
    this.error,
    this.isUpdated = false,
  });

  ExamUpdateState copyWith({
    bool? isLoading,
    String? error,
    bool? isUpdated,
  }) {
    return ExamUpdateState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isUpdated: isUpdated ?? this.isUpdated,
    );
  }
}

/// مدير حالة تحديث الامتحانات
class ExamUpdateNotifier extends StateNotifier<ExamUpdateState> {
  final dynamic _firebaseService;

  ExamUpdateNotifier(this._firebaseService) : super(const ExamUpdateState());

  /// تحديث امتحان
  Future<void> updateExam(String examId, ExamModel exam) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.updateExam(examId, exam);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحديث الامتحان: ${e.toString()}',
      );
    }
  }

  /// حذف امتحان
  Future<void> deleteExam(String examId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.deleteExam(examId);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في حذف الامتحان: ${e.toString()}',
      );
    }
  }

  /// تحديث حالة امتحان
  Future<void> updateExamStatus(String examId, ExamStatus status) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      await _firebaseService.updateExamStatus(examId, status);
      state = state.copyWith(
        isLoading: false,
        isUpdated: true,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'فشل في تحديث حالة الامتحان: ${e.toString()}',
      );
    }
  }

  /// إعادة تعيين الحالة
  void reset() {
    state = const ExamUpdateState();
  }
}

//======================================================================
// Data Models for Providers
//======================================================================

/// نموذج إحصائيات الامتحانات السريعة
class ExamStats {
  final int totalExams;
  final int ongoingExams;
  final int upcomingExams;
  final int completedExams;

  const ExamStats({
    required this.totalExams,
    required this.ongoingExams,
    required this.upcomingExams,
    required this.completedExams,
  });

  /// نسبة الامتحانات المكتملة
  double get completionPercentage {
    if (totalExams == 0) return 0.0;
    return (completedExams / totalExams) * 100;
  }

  /// نسبة الامتحانات الجارية
  double get ongoingPercentage {
    if (totalExams == 0) return 0.0;
    return (ongoingExams / totalExams) * 100;
  }

  /// نسبة الامتحانات القادمة
  double get upcomingPercentage {
    if (totalExams == 0) return 0.0;
    return (upcomingExams / totalExams) * 100;
  }

  @override
  String toString() {
    return 'ExamStats(total: $totalExams, ongoing: $ongoingExams, upcoming: $upcomingExams, completed: $completedExams)';
  }
}
