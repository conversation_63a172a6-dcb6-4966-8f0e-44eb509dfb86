import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/competition_model.dart';
import 'package:school_management_system/providers/content_providers.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';
import 'package:school_management_system/widgets/error_message.dart';

/// شاشة لعرض المسابقات العامة للطلاب والزوار باستخدام Riverpod
class CompetitionsScreen extends ConsumerWidget {
  const CompetitionsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // مشاهدة (watch) الـ provider لجلب بيانات المسابقات
    final competitionsAsyncValue = ref.watch(competitionsStreamProvider);

    return Scaffold(
      // لا نضيف AppBar هنا لأن الواجهة الرئيسية (PublicHomeScreen) توفره
      body: competitionsAsyncValue.when(
        // في حالة تحميل البيانات
        loading: () => const LoadingIndicator(),
        // في حالة حدوث خطأ
        error: (err, stack) => ErrorMessage(message: 'حدث خطأ: $err'),
        // في حالة نجاح جلب البيانات
        data: (competitions) {
          if (competitions.isEmpty) {
            return const Center(child: Text('لا توجد مسابقات متاحة حالياً.'));
          }

          // عرض القائمة
          return ListView.builder(
            padding: const EdgeInsets.all(16.0),
            itemCount: competitions.length,
            itemBuilder: (context, index) {
              final competition = competitions[index];
              return CustomCard(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان المسابقة
                    Text(
                      competition.title,
                      style: GoogleFonts.cairo(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    // تاريخ المسابقة
                    Row(
                      children: [
                        Icon(Icons.calendar_today, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        if (competition.startDate != null)
                          Text(
                            // تنسيق التاريخ ليعرض بشكل مقروء
                            'تبدأ في: ${DateFormat.yMMMMd('ar').format(competition.startDate!)}',
                            style: GoogleFonts.cairo(
                              fontSize: 14,
                              color: Colors.grey[700],
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    // وصف المسابقة
                    Text(
                      competition.description,
                      style: GoogleFonts.cairo(
                        fontSize: 15,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        },
      ),
    );
  }
}
