import 'package:flutter/material.dart';

/// نموذج بيانات لحصة دراسية واحدة في الجدول الزمني
/// يحتوي على جميع المعلومات اللازمة لعرض الحصة بشكل مفصل
class TimetableSession {
  final String subject; // اسم المادة
  final String? teacherId; // معرف المعلم
  final String? teacherName; // اسم المعلم (يتم جلبه من قاعدة البيانات)
  final String? classroom; // رقم الفصل أو القاعة
  final String? notes; // ملاحظات إضافية
  final Color? subjectColor; // لون المادة للتمييز البصري
  final String? subjectCode; // رمز المادة (اختياري)

  const TimetableSession({
    required this.subject,
    this.teacherId,
    this.teacherName,
    this.classroom,
    this.notes,
    this.subjectColor,
    this.subjectCode,
  });

  /// إنشاء كائن TimetableSession من بيانات Firestore
  /// يتعامل مع البيانات بشكل آمن ويوفر قيم افتراضية
  factory TimetableSession.fromMap(Map<String, dynamic> data) {
    return TimetableSession(
      subject: data['subject'] as String? ?? 'غير محدد',
      teacherId: data['teacherId'] as String?,
      teacherName: data['teacherName'] as String?,
      classroom: data['classroom'] as String?,
      notes: data['notes'] as String?,
      subjectColor:
          data['subjectColor'] != null
              ? Color(data['subjectColor'] as int)
              : null,
      subjectCode: data['subjectCode'] as String?,
    );
  }

  /// تحويل كائن TimetableSession إلى Map لحفظه في Firestore
  Map<String, dynamic> toMap() {
    return {
      'subject': subject,
      'teacherId': teacherId,
      'teacherName': teacherName,
      'classroom': classroom,
      'notes': notes,
      'subjectColor': subjectColor?.value,
      'subjectCode': subjectCode,
    };
  }

  /// إنشاء نسخة محدثة من الكائن مع تغيير بعض الخصائص
  TimetableSession copyWith({
    String? subject,
    String? teacherId,
    String? teacherName,
    String? classroom,
    String? notes,
    Color? subjectColor,
    String? subjectCode,
  }) {
    return TimetableSession(
      subject: subject ?? this.subject,
      teacherId: teacherId ?? this.teacherId,
      teacherName: teacherName ?? this.teacherName,
      classroom: classroom ?? this.classroom,
      notes: notes ?? this.notes,
      subjectColor: subjectColor ?? this.subjectColor,
      subjectCode: subjectCode ?? this.subjectCode,
    );
  }

  /// التحقق من وجود بيانات صالحة للحصة
  bool get isValid => subject.isNotEmpty && subject != 'غير محدد';

  /// الحصول على لون افتراضي للمادة بناءً على اسمها
  Color get defaultSubjectColor {
    if (subjectColor != null) return subjectColor!;

    // ألوان افتراضية للمواد الشائعة
    switch (subject.toLowerCase()) {
      case 'رياضيات':
      case 'الرياضيات':
        return const Color(0xFF2196F3); // أزرق
      case 'عربي':
      case 'اللغة العربية':
        return const Color(0xFF4CAF50); // أخضر
      case 'انجليزي':
      case 'اللغة الإنجليزية':
        return const Color(0xFFFF9800); // برتقالي
      case 'علوم':
      case 'العلوم':
        return const Color(0xFF9C27B0); // بنفسجي
      case 'تاريخ':
      case 'التاريخ':
        return const Color(0xFF795548); // بني
      case 'جغرافيا':
      case 'الجغرافيا':
        return const Color(0xFF607D8B); // رمادي مزرق
      case 'فيزياء':
      case 'الفيزياء':
        return const Color(0xFFE91E63); // وردي
      case 'كيمياء':
      case 'الكيمياء':
        return const Color(0xFF00BCD4); // سماوي
      case 'أحياء':
      case 'الأحياء':
        return const Color(0xFF8BC34A); // أخضر فاتح
      case 'تربية إسلامية':
      case 'الدين':
        return const Color(0xFF009688); // أخضر داكن
      case 'تربية رياضية':
      case 'الرياضة':
        return const Color(0xFFFF5722); // أحمر برتقالي
      case 'فنون':
      case 'التربية الفنية':
        return const Color(0xFFE91E63); // وردي
      case 'موسيقى':
      case 'التربية الموسيقية':
        return const Color(0xFF673AB7); // بنفسجي داكن
      default:
        return const Color(0xFF757575); // رمادي افتراضي
    }
  }

  @override
  String toString() {
    return 'TimetableSession(subject: $subject, teacherName: $teacherName, classroom: $classroom)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TimetableSession &&
        other.subject == subject &&
        other.teacherId == teacherId &&
        other.classroom == classroom;
  }

  @override
  int get hashCode {
    return subject.hashCode ^ teacherId.hashCode ^ classroom.hashCode;
  }
}

/// نموذج بيانات للجدول الزمني الكامل لفصل دراسي
/// يحتوي على جميع الحصص مرتبة حسب الأيام والفترات
class TimetableModel {
  final String classId; // معرف الفصل
  final String className; // اسم الفصل
  final Map<String, TimetableSession> sessions; // الحصص مفهرسة بـ "اليوم-الحصة"
  final DateTime lastUpdated; // تاريخ آخر تحديث

  // قائمة أيام الأسبوع الدراسية
  static const List<String> weekDays = [
    'الأحد',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
  ];

  // قائمة أوقات الحصص (يمكن تخصيصها حسب المدرسة)
  static const List<String> periodTimes = [
    '8:00 - 8:45', // الحصة الأولى
    '8:45 - 9:30', // الحصة الثانية
    '9:30 - 10:15', // الحصة الثالثة
    '10:35 - 11:20', // الحصة الرابعة (بعد الاستراحة)
    '11:20 - 12:05', // الحصة الخامسة
    '12:05 - 12:50', // الحصة السادسة
    '12:50 - 1:35', // الحصة السابعة
  ];

  const TimetableModel({
    required this.classId,
    required this.className,
    required this.sessions,
    required this.lastUpdated,
  });

  /// إنشاء كائن TimetableModel من بيانات Firestore
  factory TimetableModel.fromFirestore(
    String classId,
    String className,
    Map<String, dynamic> data,
  ) {
    final sessions = <String, TimetableSession>{};

    // تحويل كل حصة من البيانات الخام إلى كائن TimetableSession
    data.forEach((key, value) {
      if (key != 'lastUpdated' && value is Map<String, dynamic>) {
        sessions[key] = TimetableSession.fromMap(value);
      }
    });

    return TimetableModel(
      classId: classId,
      className: className,
      sessions: sessions,
      lastUpdated:
          data['lastUpdated'] != null
              ? DateTime.fromMillisecondsSinceEpoch(data['lastUpdated'])
              : DateTime.now(),
    );
  }

  /// الحصول على حصة معينة بناءً على اليوم ورقم الحصة
  TimetableSession? getSession(String day, int period) {
    return sessions['$day-$period'];
  }

  /// الحصول على جميع حصص يوم معين
  List<TimetableSession?> getDaySchedule(String day) {
    final daySchedule = <TimetableSession?>[];
    for (int i = 1; i <= 7; i++) {
      daySchedule.add(getSession(day, i));
    }
    return daySchedule;
  }

  /// الحصول على حصص اليوم الحالي
  List<TimetableSession?> getTodaySchedule() {
    final today = DateTime.now();
    final dayNames = [
      '',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    if (today.weekday >= 1 && today.weekday <= 5) {
      // أيام الدراسة (الأحد إلى الخميس)
      final dayName = dayNames[today.weekday == 7 ? 1 : today.weekday + 1];
      return getDaySchedule(dayName);
    }

    return []; // عطلة نهاية الأسبوع
  }

  /// الحصول على اسم اليوم الحالي بالعربية
  String get todayName {
    final today = DateTime.now();
    final dayNames = [
      '',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];

    if (today.weekday >= 1 && today.weekday <= 5) {
      return dayNames[today.weekday == 7 ? 1 : today.weekday + 1];
    }

    return 'عطلة';
  }

  List<String> get daysOfWeek => weekDays;

  /// التحقق من وجود حصص في الجدول
  bool get hasSchedule => sessions.isNotEmpty;

  /// الحصول على عدد الحصص الإجمالي
  int get totalSessions => sessions.length;

  /// الحصول على قائمة بجميع المواد الموجودة في الجدول
  Set<String> get allSubjects {
    return sessions.values.map((session) => session.subject).toSet();
  }

  /// الحصول على قائمة بجميع المعلمين في الجدول
  Set<String> get allTeachers {
    return sessions.values
        .where((session) => session.teacherName != null)
        .map((session) => session.teacherName!)
        .toSet();
  }

  /// البحث عن حصص مادة معينة
  List<MapEntry<String, TimetableSession>> findSubjectSessions(String subject) {
    return sessions.entries
        .where(
          (entry) =>
              entry.value.subject.toLowerCase().contains(subject.toLowerCase()),
        )
        .toList();
  }

  /// البحث عن حصص معلم معين
  List<MapEntry<String, TimetableSession>> findTeacherSessions(
    String teacherName,
  ) {
    return sessions.entries
        .where(
          (entry) =>
              entry.value.teacherName != null &&
              entry.value.teacherName!.toLowerCase().contains(
                teacherName.toLowerCase(),
              ),
        )
        .toList();
  }

  @override
  String toString() {
    return 'TimetableModel(classId: $classId, className: $className, totalSessions: $totalSessions)';
  }
}

/// نموذج بيانات لإحصائيات الجدول الزمني
/// يوفر معلومات مفيدة عن توزيع الحصص والمواد
class TimetableStats {
  final TimetableModel timetable;

  const TimetableStats(this.timetable);

  int get totalSessions =>
      TimetableModel.weekDays.length * TimetableModel.periodTimes.length;
  int get scheduledSessions => timetable.sessions.length;
  int get emptySessions => totalSessions - scheduledSessions;
  double get occupancyPercentage =>
      totalSessions > 0 ? (scheduledSessions / totalSessions) * 100 : 0.0;
  Map<String, int> get subjectDistribution => subjectCounts;
  Map<String, int> get teacherDistribution => teacherCounts;

  /// عدد الحصص لكل مادة
  Map<String, int> get subjectCounts {
    final counts = <String, int>{};
    for (final session in timetable.sessions.values) {
      counts[session.subject] = (counts[session.subject] ?? 0) + 1;
    }
    return counts;
  }

  /// عدد الحصص لكل معلم
  Map<String, int> get teacherCounts {
    final counts = <String, int>{};
    for (final session in timetable.sessions.values) {
      if (session.teacherName != null) {
        counts[session.teacherName!] = (counts[session.teacherName!] ?? 0) + 1;
      }
    }
    return counts;
  }

  /// عدد الحصص لكل يوم
  Map<String, int> get dailyCounts {
    final counts = <String, int>{};
    for (final day in TimetableModel.weekDays) {
      counts[day] =
          timetable.getDaySchedule(day).where((s) => s != null).length;
    }
    return counts;
  }

  /// المادة الأكثر تدريساً
  String? get mostFrequentSubject {
    final counts = subjectCounts;
    if (counts.isEmpty) return null;

    return counts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// المعلم الأكثر تدريساً
  String? get mostActiveTeacher {
    final counts = teacherCounts;
    if (counts.isEmpty) return null;

    return counts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// اليوم الأكثر ازدحاماً
  String? get busiestDay {
    final counts = dailyCounts;
    if (counts.isEmpty) return null;

    return counts.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// متوسط عدد الحصص اليومية
  double get averageDailySessions {
    final counts = dailyCounts;
    if (counts.isEmpty) return 0.0;

    final total = counts.values.reduce((a, b) => a + b);
    return total / counts.length;
  }
}
