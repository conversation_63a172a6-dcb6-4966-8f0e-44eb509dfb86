import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/request_management_model.dart';
import 'package:school_management_system/providers/request_management_providers.dart';
// import 'package:school_management_system/widgets/common/loading_widget.dart';
// import 'package:school_management_system/widgets/common/error_widget.dart';

/// شاشة إدارة الطلبات للإدارة
///
/// توفر واجهة شاملة لإدارة جميع طلبات أولياء الأمور والطلاب في النظام المدرسي
/// مع إمكانيات متقدمة للفلترة والبحث والموافقة والمتابعة
///
/// الميزات الرئيسية:
/// - عرض جميع الطلبات مع فلاتر متقدمة
/// - نظام موافقات متدرج حسب نوع الطلب
/// - إدارة المرفقات والوثائق
/// - تتبع شامل لحالة الطلبات
/// - إحصائيات وتقارير تفصيلية
/// - نظام تعليقات وملاحظات
/// - إشعارات وتذكيرات تلقائية
/// - واجهة سهلة الاستخدام ومتجاوبة
/// - دعم كامل للغة العربية
/// - تصدير البيانات والتقارير
class RequestManagementAdminScreen extends ConsumerStatefulWidget {
  const RequestManagementAdminScreen({super.key});

  @override
  ConsumerState<RequestManagementAdminScreen> createState() =>
      _RequestManagementAdminScreenState();
}

class _RequestManagementAdminScreenState
    extends ConsumerState<RequestManagementAdminScreen>
    with SingleTickerProviderStateMixin {
  /// تحكم في التبويبات
  late TabController _tabController;

  /// تحكم في البحث
  final TextEditingController _searchController = TextEditingController();

  /// معرف المسؤول الحالي (يجب الحصول عليه من نظام المصادقة)
  final String _currentAdminId = 'admin_001'; // TODO: الحصول من نظام المصادقة
  final String _currentAdminName =
      'مدير النظام'; // TODO: الحصول من نظام المصادقة

  @override
  void initState() {
    super.initState();

    /// إنشاء تحكم التبويبات مع 4 تبويبات رئيسية
    _tabController = TabController(length: 4, vsync: this);

    /// إضافة مستمع للبحث مع تأخير لتحسين الأداء
    _searchController.addListener(_onSearchChanged);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  /// معالج تغيير نص البحث
  void _onSearchChanged() {
    // تطبيق تأخير قصير لتحسين الأداء
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) {
        ref
            .read(requestFiltersProvider.notifier)
            .updateSearchText(_searchController.text);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      /// شريط التطبيق مع العنوان والإجراءات
      appBar: AppBar(
        title: const Text(
          'إدارة الطلبات',
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,

        /// التبويبات في شريط التطبيق
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          labelStyle: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 14,
          ),
          tabs: const [
            Tab(icon: Icon(Icons.inbox), text: 'جميع الطلبات'),
            Tab(icon: Icon(Icons.pending_actions), text: 'في انتظار الموافقة'),
            Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
            Tab(icon: Icon(Icons.settings), text: 'الإعدادات'),
          ],
        ),

        /// إجراءات شريط التطبيق
        actions: [
          /// زر الإشعارات
          IconButton(
            icon: const Icon(Icons.notifications),
            onPressed: _showNotifications,
            tooltip: 'الإشعارات',
          ),

          /// زر التحديث
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _refreshData,
            tooltip: 'تحديث البيانات',
          ),

          /// زر المزيد من الخيارات
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: _handleMenuAction,
            itemBuilder:
                (context) => [
                  const PopupMenuItem(
                    value: 'export',
                    child: ListTile(
                      leading: Icon(Icons.download),
                      title: Text('تصدير البيانات'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'settings',
                    child: ListTile(
                      leading: Icon(Icons.settings),
                      title: Text('إعدادات النظام'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                  const PopupMenuItem(
                    value: 'help',
                    child: ListTile(
                      leading: Icon(Icons.help),
                      title: Text('المساعدة'),
                      contentPadding: EdgeInsets.zero,
                    ),
                  ),
                ],
          ),
        ],
      ),

      /// محتوى الشاشة الرئيسي
      body: Column(
        children: [
          /// شريط البحث والفلاتر
          _buildSearchAndFiltersBar(),

          /// محتوى التبويبات
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                /// تبويب جميع الطلبات
                _buildAllRequestsTab(),

                /// تبويب الطلبات المعلقة للموافقة
                _buildPendingApprovalsTab(),

                /// تبويب الإحصائيات
                _buildStatisticsTab(),

                /// تبويب الإعدادات
                _buildSettingsTab(),
              ],
            ),
          ),
        ],
      ),

      /// زر الإجراء العائم لإنشاء طلب جديد
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _createNewRequest,
        icon: const Icon(Icons.add),
        label: const Text('طلب جديد'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  /// بناء شريط البحث والفلاتر
  Widget _buildSearchAndFiltersBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        border: Border(bottom: BorderSide(color: Colors.grey[300]!, width: 1)),
      ),
      child: Column(
        children: [
          /// شريط البحث
          Row(
            children: [
              /// حقل البحث
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: InputDecoration(
                    hintText: 'البحث في الطلبات...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon:
                        _searchController.text.isNotEmpty
                            ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                                ref
                                    .read(requestFiltersProvider.notifier)
                                    .updateSearchText('');
                              },
                            )
                            : null,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: Theme.of(context).primaryColor,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: Colors.white,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                ),
              ),

              const SizedBox(width: 12),

              /// زر الفلاتر المتقدمة
              ElevatedButton.icon(
                onPressed: _showAdvancedFilters,
                icon: const Icon(Icons.filter_list),
                label: const Text('فلاتر'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          /// فلاتر سريعة
          _buildQuickFilters(),
        ],
      ),
    );
  }

  /// بناء الفلاتر السريعة
  Widget _buildQuickFilters() {
    return Consumer(
      builder: (context, ref, child) {
        final filters = ref.watch(requestFiltersProvider);

        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              /// فلتر اليوم
              _buildQuickFilterChip(
                'اليوم',
                QuickFilterType.today,
                filters.startDate != null &&
                    _isSameDay(filters.startDate!, DateTime.now()),
              ),

              const SizedBox(width: 8),

              /// فلتر هذا الأسبوع
              _buildQuickFilterChip(
                'هذا الأسبوع',
                QuickFilterType.thisWeek,
                filters.startDate != null && _isThisWeek(filters.startDate!),
              ),

              const SizedBox(width: 8),

              /// فلتر هذا الشهر
              _buildQuickFilterChip(
                'هذا الشهر',
                QuickFilterType.thisMonth,
                filters.startDate != null && _isThisMonth(filters.startDate!),
              ),

              const SizedBox(width: 8),

              /// فلتر الطلبات المعلقة
              _buildQuickFilterChip(
                'معلقة',
                QuickFilterType.pending,
                filters.status == RequestStatus.submitted,
              ),

              const SizedBox(width: 8),

              /// فلتر الطلبات العاجلة
              _buildQuickFilterChip(
                'عاجلة',
                QuickFilterType.urgent,
                filters.urgentOnly,
              ),

              const SizedBox(width: 8),

              /// زر مسح جميع الفلاتر
              if (filters.hasActiveFilters)
                ActionChip(
                  label: const Text('مسح الفلاتر'),
                  onPressed: () {
                    ref.read(requestFiltersProvider.notifier).clearAllFilters();
                    _searchController.clear();
                  },
                  backgroundColor: Colors.red[50],
                  labelStyle: TextStyle(color: Colors.red[700]),
                  side: BorderSide(color: Colors.red[300]!),
                ),
            ],
          ),
        );
      },
    );
  }

  /// بناء رقاقة فلتر سريع
  Widget _buildQuickFilterChip(
    String label,
    QuickFilterType filterType,
    bool isSelected,
  ) {
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        if (selected) {
          ref
              .read(requestFiltersProvider.notifier)
              .applyQuickFilter(filterType);
        } else {
          ref.read(requestFiltersProvider.notifier).clearAllFilters();
        }
      },
      backgroundColor: Colors.white,
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
      labelStyle: TextStyle(
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey[700],
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
      side: BorderSide(
        color: isSelected ? Theme.of(context).primaryColor : Colors.grey[300]!,
      ),
    );
  }

  /// بناء تبويب جميع الطلبات
  Widget _buildAllRequestsTab() {
    return Consumer(
      builder: (context, ref, child) {
        // TODO: تنفيذ جلب جميع الطلبات مع الفلاتر
        return const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.inbox, size: 64, color: Colors.grey),
              SizedBox(height: 16),
              Text(
                'جميع الطلبات',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'سيتم عرض جميع الطلبات هنا',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ],
          ),
        );
      },
    );
  }

  /// بناء تبويب الطلبات المعلقة للموافقة
  Widget _buildPendingApprovalsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final pendingRequestsAsync = ref.watch(
          pendingApprovalRequestsProvider(_currentAdminId),
        );

        return pendingRequestsAsync.when(
          /// حالة التحميل
          loading:
              () => const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل الطلبات المعلقة...'),
                  ],
                ),
              ),

          /// حالة الخطأ
          error:
              (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red),
                    SizedBox(height: 16),
                    Text('فشل في تحميل الطلبات المعلقة'),
                    SizedBox(height: 8),
                    Text('خطأ في النظام'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed:
                          () => ref.refresh(
                            pendingApprovalRequestsProvider(_currentAdminId),
                          ),
                      child: Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),

          /// حالة البيانات
          data: (requests) {
            if (requests.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.check_circle_outline,
                      size: 64,
                      color: Colors.green,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'لا توجد طلبات معلقة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.grey,
                      ),
                    ),
                    SizedBox(height: 8),
                    Text(
                      'جميع الطلبات تمت معالجتها',
                      style: TextStyle(fontSize: 14, color: Colors.grey),
                    ),
                  ],
                ),
              );
            }

            return ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: requests.length,
              itemBuilder: (context, index) {
                final request = requests[index];
                return _buildRequestCard(request, showApprovalActions: true);
              },
            );
          },
        );
      },
    );
  }

  /// بناء تبويب الإحصائيات
  Widget _buildStatisticsTab() {
    return Consumer(
      builder: (context, ref, child) {
        final statisticsAsync = ref.watch(
          requestStatisticsProvider(
            const StatisticsParams(), // إحصائيات عامة
          ),
        );

        return statisticsAsync.when(
          /// حالة التحميل
          loading:
              () => const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text('جاري تحميل الإحصائيات...'),
                  ],
                ),
              ),

          /// حالة الخطأ
          error:
              (error, stackTrace) => Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error, size: 64, color: Colors.red),
                    SizedBox(height: 16),
                    Text('فشل في تحميل الإحصائيات'),
                    SizedBox(height: 8),
                    Text('خطأ في النظام'),
                    SizedBox(height: 16),
                    ElevatedButton(
                      onPressed:
                          () => ref.refresh(
                            requestStatisticsProvider(const StatisticsParams()),
                          ),
                      child: Text('إعادة المحاولة'),
                    ),
                  ],
                ),
              ),

          /// حالة البيانات
          data: (statistics) {
            return SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// إحصائيات عامة
                  _buildStatisticsOverview(statistics),

                  const SizedBox(height: 24),

                  /// توزيع الطلبات حسب النوع
                  _buildRequestTypeDistribution(statistics),

                  const SizedBox(height: 24),

                  /// توزيع الطلبات حسب الحالة
                  _buildRequestStatusDistribution(statistics),
                ],
              ),
            );
          },
        );
      },
    );
  }

  /// بناء تبويب الإعدادات
  Widget _buildSettingsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          /// إعدادات الإشعارات
          _buildSettingsSection('إعدادات الإشعارات', Icons.notifications, [
            _buildSettingsTile(
              'إشعارات الطلبات الجديدة',
              'تلقي إشعار عند وصول طلب جديد',
              true,
              (value) {
                // TODO: تنفيذ تحديث إعدادات الإشعارات
              },
            ),
            _buildSettingsTile(
              'إشعارات الطلبات العاجلة',
              'تلقي إشعار فوري للطلبات العاجلة',
              true,
              (value) {
                // TODO: تنفيذ تحديث إعدادات الإشعارات
              },
            ),
          ]),

          const SizedBox(height: 24),

          /// إعدادات النظام
          _buildSettingsSection('إعدادات النظام', Icons.settings, [
            _buildSettingsActionTile(
              'إدارة أنواع الطلبات',
              'تخصيص أنواع الطلبات المتاحة',
              Icons.category,
              () {
                // TODO: فتح شاشة إدارة أنواع الطلبات
              },
            ),
            _buildSettingsActionTile(
              'إدارة مراحل الموافقة',
              'تخصيص مراحل الموافقة لكل نوع طلب',
              Icons.approval,
              () {
                // TODO: فتح شاشة إدارة مراحل الموافقة
              },
            ),
            _buildSettingsActionTile(
              'إدارة الصلاحيات',
              'تحديد صلاحيات المستخدمين',
              Icons.security,
              () {
                // TODO: فتح شاشة إدارة الصلاحيات
              },
            ),
          ]),
        ],
      ),
    );
  }

  /// بناء بطاقة طلب
  Widget _buildRequestCard(
    RequestManagementModel request, {
    bool showApprovalActions = false,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: InkWell(
        onTap: () => _viewRequestDetails(request),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// رأس البطاقة
              Row(
                children: [
                  /// رقم الطلب
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      request.requestNumber,
                      style: TextStyle(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),

                  const Spacer(),

                  /// حالة الطلب
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getStatusColor(request.status).withOpacity(0.1),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Text(
                      request.statusDescription,
                      style: TextStyle(
                        color: _getStatusColor(request.status),
                        fontWeight: FontWeight.bold,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              /// عنوان الطلب
              Text(
                request.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 8),

              /// معلومات الطلب
              Row(
                children: [
                  Icon(Icons.person, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    request.requesterName,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),

                  const SizedBox(width: 16),

                  Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(request.submittedAt),
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),

              /// أزرار الموافقة (إذا كانت مطلوبة)
              if (showApprovalActions &&
                  request.currentApprovalStep != null) ...[
                const SizedBox(height: 16),
                Row(
                  children: [
                    /// زر الموافقة
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _approveRequest(request),
                        icon: const Icon(Icons.check),
                        label: const Text('موافقة'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),

                    const SizedBox(width: 12),

                    /// زر الرفض
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _rejectRequest(request),
                        icon: const Icon(Icons.close),
                        label: const Text('رفض'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  // ===================================================================
  // الدوال المساعدة والمعالجات
  // ===================================================================

  /// معالج إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'export':
        _exportData();
        break;
      case 'settings':
        _openSystemSettings();
        break;
      case 'help':
        _showHelp();
        break;
    }
  }

  /// عرض الإشعارات
  void _showNotifications() {
    // TODO: تنفيذ عرض الإشعارات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ عرض الإشعارات قريباً')),
    );
  }

  /// تحديث البيانات
  void _refreshData() {
    // تحديث جميع المزودات
    ref.invalidate(pendingApprovalRequestsProvider);
    ref.invalidate(requestStatisticsProvider);

    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم تحديث البيانات'),
        duration: Duration(seconds: 2),
      ),
    );
  }

  /// عرض الفلاتر المتقدمة
  void _showAdvancedFilters() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            expand: false,
            builder: (context, scrollController) {
              return Container(
                padding: const EdgeInsets.all(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// رأس النافذة
                    Row(
                      children: [
                        const Text(
                          'فلاتر متقدمة',
                          style: TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        IconButton(
                          onPressed: () => Navigator.pop(context),
                          icon: const Icon(Icons.close),
                        ),
                      ],
                    ),

                    const Divider(),

                    /// محتوى الفلاتر
                    Expanded(
                      child: SingleChildScrollView(
                        controller: scrollController,
                        child: const Column(
                          children: [
                            // TODO: تنفيذ واجهة الفلاتر المتقدمة
                            Center(
                              child: Text(
                                'سيتم تنفيذ الفلاتر المتقدمة قريباً',
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
    );
  }

  /// إنشاء طلب جديد
  void _createNewRequest() {
    // TODO: فتح شاشة إنشاء طلب جديد
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح شاشة إنشاء طلب جديد')),
    );
  }

  /// عرض تفاصيل الطلب
  void _viewRequestDetails(RequestManagementModel request) {
    // TODO: فتح شاشة تفاصيل الطلب
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('عرض تفاصيل الطلب: ${request.requestNumber}')),
    );
  }

  /// الموافقة على الطلب
  void _approveRequest(RequestManagementModel request) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الموافقة'),
            content: Text(
              'هل تريد الموافقة على الطلب ${request.requestNumber}؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _processApproval(request, true);
                },
                child: const Text('موافقة'),
              ),
            ],
          ),
    );
  }

  /// رفض الطلب
  void _rejectRequest(RequestManagementModel request) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الرفض'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('هل تريد رفض الطلب ${request.requestNumber}؟'),
                const SizedBox(height: 16),
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'سبب الرفض (اختياري)',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 3,
                  onChanged: (value) {
                    // TODO: حفظ سبب الرفض
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _processApproval(request, false);
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                ),
                child: const Text('رفض'),
              ),
            ],
          ),
    );
  }

  /// معالجة الموافقة أو الرفض
  Future<void> _processApproval(
    RequestManagementModel request,
    bool isApproved,
  ) async {
    final result = await ref
        .read(approvalProcessProvider.notifier)
        .processApproval(
          request.id,
          _currentAdminId,
          _currentAdminName,
          isApproved,
        );

    if (result && mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isApproved ? 'تمت الموافقة على الطلب بنجاح' : 'تم رفض الطلب بنجاح',
          ),
          backgroundColor: isApproved ? Colors.green : Colors.red,
        ),
      );
    }
  }

  /// تصدير البيانات
  void _exportData() {
    // TODO: تنفيذ تصدير البيانات
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم تنفيذ تصدير البيانات قريباً')),
    );
  }

  /// فتح إعدادات النظام
  void _openSystemSettings() {
    // TODO: فتح شاشة إعدادات النظام
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('سيتم فتح إعدادات النظام قريباً')),
    );
  }

  /// عرض المساعدة
  void _showHelp() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('المساعدة'),
            content: const SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    'نظام إدارة الطلبات',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 16),
                  Text('• استخدم التبويبات للتنقل بين الأقسام المختلفة'),
                  SizedBox(height: 8),
                  Text('• استخدم شريط البحث للبحث في الطلبات'),
                  SizedBox(height: 8),
                  Text('• استخدم الفلاتر السريعة لتصفية النتائج'),
                  SizedBox(height: 8),
                  Text('• اضغط على الطلب لعرض التفاصيل'),
                  SizedBox(height: 8),
                  Text('• استخدم أزرار الموافقة والرفض لمعالجة الطلبات'),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('حسناً'),
              ),
            ],
          ),
    );
  }

  // ===================================================================
  // دوال بناء المكونات المساعدة
  // ===================================================================

  /// بناء نظرة عامة على الإحصائيات
  Widget _buildStatisticsOverview(Map<String, dynamic> statistics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نظرة عامة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'إجمالي الطلبات',
                    statistics['totalRequests']?.toString() ?? '0',
                    Icons.inbox,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'مكتملة',
                    statistics['completedRequests']?.toString() ?? '0',
                    Icons.check_circle,
                    Colors.green,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'معلقة',
                    statistics['pendingRequests']?.toString() ?? '0',
                    Icons.pending,
                    Colors.orange,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildStatCard(
                    'مرفوضة',
                    statistics['rejectedRequests']?.toString() ?? '0',
                    Icons.cancel,
                    Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, size: 32, color: color),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(fontSize: 12, color: Colors.grey[600]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// بناء توزيع الطلبات حسب النوع
  Widget _buildRequestTypeDistribution(Map<String, dynamic> statistics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الطلبات حسب النوع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // TODO: تنفيذ رسم بياني لتوزيع الطلبات حسب النوع
            const Center(
              child: Text(
                'سيتم عرض الرسم البياني هنا',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء توزيع الطلبات حسب الحالة
  Widget _buildRequestStatusDistribution(Map<String, dynamic> statistics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'توزيع الطلبات حسب الحالة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // TODO: تنفيذ رسم بياني لتوزيع الطلبات حسب الحالة
            const Center(
              child: Text(
                'سيتم عرض الرسم البياني هنا',
                style: TextStyle(fontSize: 14, color: Colors.grey),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء قسم الإعدادات
  Widget _buildSettingsSection(
    String title,
    IconData icon,
    List<Widget> children,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(icon, color: Theme.of(context).primaryColor),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  /// بناء عنصر إعدادات مع مفتاح
  Widget _buildSettingsTile(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: Theme.of(context).primaryColor,
      contentPadding: EdgeInsets.zero,
    );
  }

  /// بناء عنصر إعدادات مع إجراء
  Widget _buildSettingsActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Theme.of(context).primaryColor),
      title: Text(title),
      subtitle: Text(subtitle),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: onTap,
      contentPadding: EdgeInsets.zero,
    );
  }

  // ===================================================================
  // دوال مساعدة للتاريخ والألوان
  // ===================================================================

  /// الحصول على لون الحالة
  Color _getStatusColor(RequestStatus status) {
    switch (status) {
      case RequestStatus.draft:
        return Colors.grey;
      case RequestStatus.submitted:
        return Colors.blue;
      case RequestStatus.underReview:
        return Colors.orange;
      case RequestStatus.pendingDocuments:
        return Colors.amber;
      case RequestStatus.pendingApproval:
        return Colors.purple;
      case RequestStatus.approved:
        return Colors.lightGreen;
      case RequestStatus.inProgress:
        return Colors.cyan;
      case RequestStatus.completed:
        return Colors.green;
      case RequestStatus.rejected:
        return Colors.red;
      case RequestStatus.cancelled:
        return Colors.grey;
      case RequestStatus.onHold:
        return Colors.brown;
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// التحقق من كون التاريخ في نفس اليوم
  bool _isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
        date1.month == date2.month &&
        date1.day == date2.day;
  }

  /// التحقق من كون التاريخ في هذا الأسبوع
  bool _isThisWeek(DateTime date) {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));

    return date.isAfter(startOfWeek.subtract(const Duration(days: 1))) &&
        date.isBefore(endOfWeek.add(const Duration(days: 1)));
  }

  /// التحقق من كون التاريخ في هذا الشهر
  bool _isThisMonth(DateTime date) {
    final now = DateTime.now();
    return date.year == now.year && date.month == now.month;
  }
}
