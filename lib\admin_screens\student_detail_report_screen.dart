import 'package:flutter/material.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/services/firebase_service.dart';
import 'package:school_management_system/services/pdf_export_service.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة لعرض تقرير مفصل لطالب واحد، تشمل معلوماته المالية والأكاديمية والحضور.
class StudentDetailReportScreen extends StatefulWidget {
  final StudentModel student;

  const StudentDetailReportScreen({super.key, required this.student});

  @override
  State<StudentDetailReportScreen> createState() => _StudentDetailReportScreenState();
}

class _StudentDetailReportScreenState extends State<StudentDetailReportScreen> {
  final FirebaseService _firebaseService = FirebaseService();
  final PdfExportService _pdfExportService = PdfExportService();

  late Future<Map<String, dynamic>> _reportDataFuture; // لتخزين نتيجة جلب البيانات

  @override
  void initState() {
    super.initState();
    // بدء عملية جلب البيانات عند تهيئة الشاشة
    _reportDataFuture = _fetchReportData();
  }

  /// دالة لجلب جميع بيانات التقرير (المالية، الدرجات، الحضور) بشكل متزامن.
  Future<Map<String, dynamic>> _fetchReportData() async {
    // استدعاء دوال الخدمة لجلب كل جزء من البيانات
    final financialData = await _firebaseService.getStudentFinancialDetails(widget.student.id);
    final gradesData = await _firebaseService.getStudentGrades(widget.student.id);
    final attendanceData = await _firebaseService.getStudentAttendance(widget.student.id);
    
    // إرجاع خريطة تحتوي على جميع البيانات المجمعة
    return {
      'financials': financialData,
      'grades': gradesData,
      'attendance': attendanceData,
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('تقرير الطالب: ${widget.student.name}'),
        actions: [
          // زر لتصدير التقرير المعروض كملف PDF
          IconButton(
            icon: const Icon(Icons.picture_as_pdf),
            onPressed: () async {
              // انتظار اكتمال جلب البيانات قبل محاولة التصدير
              final reportData = await _reportDataFuture;
              if (mounted) {
                // استدعاء خدمة تصدير PDF مع تمرير جميع البيانات اللازمة
                await _pdfExportService.generateAndDownloadPdf(
                  context,
                  widget.student,
                  financialData: reportData['financials'],
                  grades: (reportData['grades'] as List).cast<Map<String, dynamic>>(),
                  attendance: (reportData['attendance'] as List).cast<Map<String, dynamic>>(),
                );
              }
            },
            tooltip: 'تصدير كـ PDF',
          ),
        ],
      ),
      body: FutureBuilder<Map<String, dynamic>>(
        future: _reportDataFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const LoadingIndicator();
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ في تحميل البيانات: ${snapshot.error}'));
          }
          if (!snapshot.hasData) {
            return const Center(child: Text('لا توجد بيانات لعرضها.'));
          }

          final reportData = snapshot.data!;
          final financialData = reportData['financials'];
          final grades = reportData['grades'] as List;
          final attendance = reportData['attendance'] as List;

          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionTitle('المعلومات الأساسية'),
                Card(
                  child: ListTile(
                    title: Text(widget.student.name),
                    subtitle: Text('الرقم الأكاديمي: ${widget.student.studentNumber}'),
                  ),
                ),
                const SizedBox(height: 20),

                _buildSectionTitle('التقرير المالي'),
                _buildFinancialSummary(financialData),

                const SizedBox(height: 20),
                _buildSectionTitle('الدرجات'),
                _buildGradesSection(grades),

                const SizedBox(height: 20),
                _buildSectionTitle('الحضور والغياب'),
                _buildAttendanceSection(attendance),
              ],
            ),
          );
        },
      ),
    );
  }

  /// ويدجت لبناء عنوان لكل قسم في التقرير.
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
      ),
    );
  }

  /// ويدجت لعرض ملخص البيانات المالية.
  Widget _buildFinancialSummary(Map<String, dynamic> financialData) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildSummaryRow('إجمالي الرسوم:', '${financialData['totalAssigned']?.toStringAsFixed(2) ?? '0.00'}'),
            _buildSummaryRow('إجمالي المدفوع:', '${financialData['totalPaid']?.toStringAsFixed(2) ?? '0.00'}'),
            const Divider(),
            _buildSummaryRow('المبلغ المتبقي:', '${financialData['totalRemaining']?.toStringAsFixed(2) ?? '0.00'}', isBold: true),
          ],
        ),
      ),
    );
  }

  /// ويدجت لعرض قسم الدرجات.
  Widget _buildGradesSection(List grades) {
    if (grades.isEmpty) {
      return const Card(child: ListTile(title: Text('لا توجد درجات مسجلة.')));
    }
    return Card(
      child: Column(
        children: grades.map((grade) {
          return ListTile(
            title: Text(grade['subjectName'] ?? 'مادة غير محددة'),
            trailing: Text(
              '${grade['grade'] ?? 'N/A'}',
              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
            ),
          );
        }).toList(),
      ),
    );
  }

  /// ويدجت لعرض قسم الحضور والغياب.
  Widget _buildAttendanceSection(List attendance) {
    if (attendance.isEmpty) {
      return const Card(child: ListTile(title: Text('لا يوجد سجل حضور.')));
    }
    // حساب عدد أيام الحضور والغياب
    final presentCount = attendance.where((a) => a['status'] == 'حاضر').length;
    final absentCount = attendance.where((a) => a['status'] == 'غائب').length;
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildSummaryRow('أيام الحضور:', '$presentCount'),
            _buildSummaryRow('أيام الغياب:', '$absentCount'),
          ],
        ),
      ),
    );
  }

  /// ويدجت مساعد لبناء صف يحتوي على عنوان وقيمة.
  Widget _buildSummaryRow(String title, String value, {bool isBold = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(title, style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
          Text(value, style: TextStyle(fontWeight: isBold ? FontWeight.bold : FontWeight.normal)),
        ],
      ),
    );
  }
}
