import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/timetable_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/services/firebase_service.dart';

/// Provides the currently selected class ID for timetable management.
final selectedTimetableClassIdProvider = StateProvider<String?>((ref) => null);

/// Provides a stream of the timetable document for the selected class.
final timetableStreamProvider = StreamProvider.autoDispose
    .family<TimetableModel?, String>((ref, classId) {
  if (classId.isEmpty) {
    return Stream.value(null);
  }
  final firebaseService = ref.watch(firebaseServiceProvider);
  final classes = ref.watch(classesStreamProvider);
  final className = classes.asData?.value
          .firstWhere((c) => c.id == classId,
              orElse: () => ClassModel(id: '', name: ''))
          .name ??
      '';
  return firebaseService.getTimetableStream(classId).map((snapshot) {
    if (snapshot.exists) {
      return TimetableModel.fromFirestore(
          classId, className, snapshot.data() as Map<String, dynamic>);
    }
    return null;
  });
});

/// Provides a stream of the timetable document for the selected class.
final timetableDocumentStreamProvider =
    StreamProvider.autoDispose<DocumentSnapshot?>((
  ref,
) {
  final classId = ref.watch(selectedTimetableClassIdProvider);
  if (classId == null) {
    return Stream.value(null);
  }
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getTimetableStream(classId);
});

final timetableProvider =
    StreamProvider.autoDispose.family<TimetableModel, String>((ref, classId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  final classes = ref.watch(classesStreamProvider);
  final className = classes.asData?.value
          .firstWhere((c) => c.id == classId,
              orElse: () => ClassModel(id: '', name: ''))
          .name ??
      '';
  return firebaseService.getTimetableStream(classId).map((snapshot) {
    if (snapshot.exists) {
      return TimetableModel.fromFirestore(
          classId, className, snapshot.data() as Map<String, dynamic>);
    }
    // Return a default/empty TimetableModel if the document doesn't exist
    return TimetableModel(
      classId: classId,
      className: className,
      sessions: {},
      lastUpdated: DateTime.now(),
    );
  });
});

/// Provider for the timetable logic/controller.
final timetableControllerProvider = Provider((ref) {
  return TimetableController(ref: ref);
});

class TimetableController {
  final Ref _ref;
  TimetableController({required Ref ref}) : _ref = ref;

  FirebaseService get _firebaseService => _ref.read(firebaseServiceProvider);

  Future<void> updateSession(
    String classId,
    String day,
    int period,
    String? subject,
    String? teacherId,
  ) async {
    if (subject == null || teacherId == null) {
      // Clear the session
      await _firebaseService.clearTimetableSession(classId, day, period);
      return;
    }

    // Check for conflicts before updating
    final conflict = await _checkScheduleConflict(
      classId,
      day,
      period,
      teacherId,
    );
    if (conflict != null) {
      throw Exception(
        'تعارض: المعلم مشغول في حصة أخرى في فصل ${conflict['className']}',
      );
    }

    final sessionData = {'subject': subject, 'teacherId': teacherId};
    await _firebaseService.updateTimetableSession(
      classId,
      day,
      period,
      sessionData,
    );
  }

  Future<Map<String, dynamic>?> _checkScheduleConflict(
    String currentClassId,
    String day,
    int period,
    String teacherId,
  ) async {
    return _firebaseService.checkTeacherScheduleConflict(
      currentClassId,
      day,
      period,
      teacherId,
    );
  }
}

//======================================================================
// Providers for Student Timetable Screen - Enhanced Version
//======================================================================

/// مزود محسن لجلب بيانات الجدول الدراسي للطالب مع النموذج الجديد
/// يحول البيانات الخام إلى كائن TimetableModel منظم ومفيد
final studentTimetableProvider = FutureProvider.autoDispose
    .family<TimetableModel?, String>((ref, studentId) async {
      final firebaseService = ref.watch(firebaseServiceProvider);

      try {
        // جلب بيانات الطالب للحصول على معرف الفصل واسمه
        final studentStream = firebaseService.getStudentById(studentId);
        final student = await studentStream.first;

        if (student == null) {
          throw Exception('لم يتم العثور على بيانات الطالب');
        }
        final classId = student.classId;
        final className = student.studentClass;

        if (classId == null) {
          throw Exception('الطالب غير مسجل في فصل دراسي');
        }

        // جلب بيانات الجدول الزمني
        final timetableData = await firebaseService.getStudentTimetable(
          studentId,
        );

        if (timetableData.isEmpty) {
          return null; // لا يوجد جدول زمني
        }

        // تحويل البيانات إلى نموذج منظم
        return TimetableModel.fromFirestore(
          classId,
          className ?? 'فصل غير محدد',
          timetableData,
        );
      } catch (e) {
        // إعادة رمي الخطأ ليتم التعامل معه في الواجهة
        rethrow;
      }
    });

/// مزود لجلب بيانات الجدول الزمني مع أسماء المعلمين محملة مسبقاً
/// يقوم بجلب أسماء جميع المعلمين في الجدول دفعة واحدة لتحسين الأداء
final enrichedStudentTimetableProvider = FutureProvider.autoDispose
    .family<TimetableModel?, String>((ref, studentId) async {
      // جلب الجدول الأساسي أولاً
      final basicTimetable = await ref.watch(
        studentTimetableProvider(studentId).future,
      );

      if (basicTimetable == null) return null;

      final firebaseService = ref.watch(firebaseServiceProvider);
      final enrichedSessions = <String, TimetableSession>{};

      // جلب أسماء المعلمين لجميع الحصص
      for (final entry in basicTimetable.sessions.entries) {
        final session = entry.value;

        if (session.teacherId != null && session.teacherName == null) {
          // جلب اسم المعلم من قاعدة البيانات
          try {
            final teacherDoc = await firebaseService.getTeacherById(
              session.teacherId!,
            );
            final teacherName =
                teacherDoc.exists
                    ? (teacherDoc.data() as Map<String, dynamic>)['name']
                            as String? ??
                        'غير معروف'
                    : 'غير معروف';

            // إنشاء نسخة محدثة من الحصة مع اسم المعلم
            enrichedSessions[entry.key] = session.copyWith(
              teacherName: teacherName,
            );
          } catch (e) {
            // في حالة الخطأ، استخدم الحصة الأصلية
            enrichedSessions[entry.key] = session;
          }
        } else {
          // الحصة تحتوي على اسم المعلم بالفعل أو لا تحتاج معلم
          enrichedSessions[entry.key] = session;
        }
      }

      // إرجاع جدول زمني محدث مع أسماء المعلمين
      return TimetableModel(
        classId: basicTimetable.classId,
        className: basicTimetable.className,
        sessions: enrichedSessions,
        lastUpdated: basicTimetable.lastUpdated,
      );
    });

/// مزود لإحصائيات الجدول الزمني
/// يوفر معلومات مفيدة عن توزيع الحصص والمواد
final timetableStatsProvider = Provider.autoDispose
    .family<TimetableStats?, String>((ref, studentId) {
      final timetable =
          ref.watch(enrichedStudentTimetableProvider(studentId)).asData?.value;
      return timetable != null ? TimetableStats(timetable) : null;
    });

/// مزود لحصص اليوم الحالي
/// يعرض الحصص المجدولة لليوم الحالي فقط
final todayScheduleProvider = Provider.autoDispose
    .family<List<TimetableSession?>, String>((ref, studentId) {
      final timetable =
          ref.watch(enrichedStudentTimetableProvider(studentId)).asData?.value;
      return timetable?.getTodaySchedule() ?? [];
    });

/// مزود لاسم اليوم الحالي
/// يعرض اسم اليوم الحالي بالعربية أو "عطلة" في نهاية الأسبوع
final currentDayProvider = Provider<String>((ref) {
  final today = DateTime.now();
  final dayNames = [
    '',
    'الاثنين',
    'الثلاثاء',
    'الأربعاء',
    'الخميس',
    'الجمعة',
    'السبت',
    'الأحد',
  ];

  if (today.weekday >= 1 && today.weekday <= 5) {
    // أيام الدراسة (الأحد إلى الخميس)
    return dayNames[today.weekday == 7 ? 1 : today.weekday + 1];
  }

  return 'عطلة نهاية الأسبوع';
});

/// مزود للبحث في الجدول الزمني
/// يسمح بالبحث عن المواد أو المعلمين
final timetableSearchProvider = StateProvider<String>((ref) => '');

/// مزود للنتائج المفلترة بناءً على البحث
final filteredTimetableProvider = Provider.autoDispose.family<
  List<MapEntry<String, TimetableSession>>,
  String
>((ref, studentId) {
  final timetable =
      ref.watch(enrichedStudentTimetableProvider(studentId)).asData?.value;
  final searchQuery = ref.watch(timetableSearchProvider).toLowerCase().trim();

  if (timetable == null || searchQuery.isEmpty) {
    return timetable?.sessions.entries.toList() ?? [];
  }

  // البحث في أسماء المواد وأسماء المعلمين
  return timetable.sessions.entries.where((entry) {
    final session = entry.value;
    return session.subject.toLowerCase().contains(searchQuery) ||
        (session.teacherName?.toLowerCase().contains(searchQuery) ?? false) ||
        (session.classroom?.toLowerCase().contains(searchQuery) ?? false);
  }).toList();
});

/// مزود محسن لجلب اسم معلم معين (محتفظ به للتوافق العكسي)
/// يستخدم تخزين مؤقت لتحسين الأداء
final teacherNameProvider = FutureProvider.autoDispose.family<String, String>((
  ref,
  teacherId,
) async {
  final firebaseService = ref.watch(firebaseServiceProvider);

  try {
    final teacherDoc = await firebaseService.getTeacherById(teacherId);
    if (teacherDoc.exists) {
      final teacherData = teacherDoc.data() as Map<String, dynamic>;
      return teacherData['name'] as String? ?? 'غير معروف';
    }
    return 'غير معروف';
  } catch (e) {
    return 'خطأ في التحميل';
  }
});

/// مزود لحالة عرض الجدول (أسبوعي أم يومي)
final timetableViewModeProvider = StateProvider<TimetableViewMode>(
  (ref) => TimetableViewMode.weekly,
);

/// تعداد لأنماط عرض الجدول الزمني
enum TimetableViewMode {
  weekly, // عرض أسبوعي
  daily, // عرض يومي
  today, // عرض اليوم الحالي فقط
}
