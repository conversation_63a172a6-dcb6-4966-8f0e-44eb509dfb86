import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart'; // Correct source for class providers
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لتخزين نص البحث في شاشة التقارير.
final reportSearchQueryProvider = StateProvider<String>((ref) => '');

/// StreamProvider لجلب الطلاب بناءً على الصف المختار.
/// إذا لم يتم اختيار صف، فإنه يجلب جميع الطلاب.
final reportStudentsStreamProvider = StreamProvider.autoDispose<List<StudentModel>>((ref) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  final classId = ref.watch(selectedClassIdProvider);

  if (classId == null) {
    return firebaseService.getAllStudentsStream();
  } else {
    return firebaseService.getStudentsByClassStream(classId);
  }
});

/// Provider لتصفية قائمة الطلاب في شاشة التقارير بناءً على نص البحث.
final filteredReportStudentsProvider = Provider.autoDispose<List<StudentModel>>((ref) {
  final studentsAsyncValue = ref.watch(reportStudentsStreamProvider);
  final searchQuery = ref.watch(reportSearchQueryProvider).toLowerCase();

  return studentsAsyncValue.when(
    data: (students) {
      if (searchQuery.isEmpty) {
        return students;
      }
      return students.where((student) {
        return student.name.toLowerCase().contains(searchQuery);
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});

/// FutureProvider لجلب بيانات التقرير المجمع للصف.
/// يتم تنفيذه فقط عند توفير معرّف الصف.
final classReportProvider = FutureProvider.autoDispose.family<List<Map<String, dynamic>>, String>((ref, classId) {
  final firebaseService = ref.watch(firebaseServiceProvider);
  return firebaseService.getClassComprehensiveReportData(classId);
});
