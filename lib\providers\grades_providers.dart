import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart'; // Correct source for class providers
import 'package:school_management_system/providers/services_provider.dart';

/// Provider لتخزين نص البحث في شاشة الدرجات.
final gradesSearchQueryProvider = StateProvider<String>((ref) => '');

/// Provider لتصفية قائمة الطلاب في شاشة الدرجات بناءً على نص البحث.
final filteredGradesStudentsProvider = Provider.autoDispose<List<StudentModel>>((ref) {
  // مراقبة معرّف الفصل المختار
  final classId = ref.watch(selectedClassIdProvider);
  // إذا لم يتم اختيار فصل، لا تقم بإرجاع أي طلاب
  if (classId == null) return [];

  // جلب الطلاب بناءً على الفصل المختار
  final studentsAsyncValue = ref.watch(classStudentsStreamProvider(classId));
  final searchQuery = ref.watch(gradesSearchQueryProvider).toLowerCase();

  return studentsAsyncValue.when(
    data: (students) {
      if (searchQuery.isEmpty) {
        return students;
      }
      return students.where((student) {
        final nameMatches = student.name.toLowerCase().contains(searchQuery);
        final numberMatches = student.studentNumber.toLowerCase().contains(searchQuery);
        return nameMatches || numberMatches;
      }).toList();
    },
    loading: () => [],
    error: (_, __) => [],
  );
});
