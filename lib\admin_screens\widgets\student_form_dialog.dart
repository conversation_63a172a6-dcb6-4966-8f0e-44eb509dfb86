
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/student_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/services_provider.dart';

class StudentFormDialog extends ConsumerStatefulWidget {
  final StudentModel? student;
  const StudentFormDialog({super.key, this.student});

  @override
  ConsumerState<StudentFormDialog> createState() => _StudentFormDialogState();
}

class _StudentFormDialogState extends ConsumerState<StudentFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _numberController;
  late final TextEditingController _emailController;
  late final TextEditingController _passwordController;

  String? _selectedClassId;
  String? _selectedClassName;
  String? _selectedGender;
  Uint8List? _imageBytes;
  File? _imageFile; // Keep for mobile
  bool _isLoading = false; // تعريف متغير حالة التحميل
  bool get _isEditing => widget.student != null;

  @override
  void initState() {
    super.initState();
    final student = widget.student;
    _nameController = TextEditingController(text: student?.name);
    _numberController = TextEditingController(
        text: student?.studentNumber ??
            'S-${DateTime.now().millisecondsSinceEpoch}');
    _emailController = TextEditingController(text: student?.email);
    _passwordController = TextEditingController();
    _selectedClassId = student?.classId;
    _selectedClassName = student?.studentClass;
    _selectedGender = student?.gender;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _numberController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedFile =
        await ImagePicker().pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      final bytes = await pickedFile.readAsBytes();
      setState(() {
        _imageBytes = bytes;
        if (!kIsWeb) {
          _imageFile = File(pickedFile.path);
        }
      });
    }
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      _formKey.currentState!.save();
      final studentService = ref.read(firebaseServiceProvider); // الوصول الصحيح للخدمة
      try {
        setState(() => _isLoading = true);

        // ================== ملاحظة هامة لمرحلة التطوير ==================
        // تم تعطيل ميزة رفع الصور مؤقتًا لأنها تتطلب خطة Blaze المدفوعة
        // لحل مشكلة CORS عند الرفع من localhost.
        // لإعادة تفعيلها في المستقبل، قم بإزالة السطر التالي الذي يفرض
        // قيمة null، وأعد تمرير `_imageBytes` الأصلي.
        Uint8List? imageToUpload = null;
        // =================================================================

        if (widget.student == null) {
          await studentService.addStudent(
            _nameController.text,
            _numberController.text,
            _selectedClassId,
            _selectedClassName,
            _emailController.text,
            _passwordController.text,
            _selectedGender,
            imageToUpload, // تم التعديل هنا
          );
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تمت إضافة الطالب بنجاح')),
          );
        } else {
          await studentService.updateStudent(
            widget.student!.id,
            _nameController.text,
            _numberController.text,
            _selectedClassId,
            _selectedClassName,
            widget.student!.classId,
            _selectedGender,
            imageToUpload, // تم التعديل هنا
          );
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('تم تحديث بيانات الطالب بنجاح')),
          );
        }
        Navigator.of(context).pop();
      } catch (e) {
        print('Error in student form submission: $e');
        print('Stack trace: ${StackTrace.current}');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل حفظ بيانات الطالب: ${e.toString()}')),
        );
      } finally {
        if (mounted) { // التأكد من أن الويدجت لا يزال في الشجرة
          setState(() => _isLoading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_isEditing ? 'تعديل بيانات الطالب' : 'إضافة طالب جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (_imageBytes != null)
                Image.memory(_imageBytes!, height: 100)
              else if (widget.student?.imageUrl != null)
                Image.network(widget.student!.imageUrl!, height: 100),
              TextButton.icon(
                icon: const Icon(Icons.image),
                label: const Text('اختيار صورة'),
                onPressed: _pickImage,
              ),
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                validator: (v) => v!.isEmpty ? 'الاسم مطلوب' : null,
              ),
              TextFormField(
                controller: _numberController,
                decoration: const InputDecoration(labelText: 'الرقم الأكاديمي'),
                readOnly: true,
                validator: (v) => v!.isEmpty ? 'الرقم الأكاديمي مطلوب' : null,
              ),
              DropdownButtonFormField<String>(
                items: const [
                  DropdownMenuItem(value: 'ذكر', child: Text('ذكر')),
                  DropdownMenuItem(value: 'أنثى', child: Text('أنثى')),
                ],
                value: _selectedGender,
                onChanged: (value) {
                  setState(() {
                    _selectedGender = value;
                  });
                },
                decoration: const InputDecoration(
                  labelText: 'الجنس',
                  border: OutlineInputBorder(),
                ),
                validator: (v) => v == null ? 'الجنس مطلوب' : null,
              ),
              const SizedBox(height: 16),
              Consumer(
                builder: (context, ref, child) {
                  final classesAsyncValue = ref.watch(classesStreamProvider);
                  return classesAsyncValue.when(
                    data: (classes) {
                      final items = classes.map((classModel) {
                        return DropdownMenuItem<String>(
                          value: classModel.id,
                          child: Text(classModel.name),
                        );
                      }).toList();

                      return DropdownButtonFormField<String>(
                        items: items,
                        value: _selectedClassId,
                        onChanged: (value) {
                          setState(() {
                            _selectedClassId = value;
                            _selectedClassName = classes
                                .firstWhere((classModel) => classModel.id == value)
                                .name;
                          });
                        },
                        decoration: const InputDecoration(
                          labelText: 'الصف',
                          border: OutlineInputBorder(),
                        ),
                        validator: (v) => v == null ? 'الصف مطلوب' : null,
                      );
                    },
                    loading: () => const CircularProgressIndicator(),
                    error: (err, stack) => const Text('فشل تحميل الصفوف'),
                  );
                },
              ),
              if (!_isEditing)
                TextFormField(
                  controller: _emailController,
                  decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                  validator: (v) => v!.isEmpty || !v.contains('@') ? 'بريد إلكتروني غير صالح' : null,
                ),
              if (!_isEditing)
                TextFormField(
                  controller: _passwordController,
                  decoration: const InputDecoration(labelText: 'كلمة المرور'),
                  obscureText: true,
                  validator: (v) => v!.length < 6 ? 'كلمة المرور قصيرة جداً' : null,
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: _isLoading ? null : _submit, // تعطيل الزر أثناء التحميل
          child: _isLoading
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('حفظ'),
        ),
      ],
    );
  }
}
