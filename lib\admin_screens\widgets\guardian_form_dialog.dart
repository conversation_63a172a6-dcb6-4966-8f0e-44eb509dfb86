import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/guardian_model.dart';
import 'package:school_management_system/providers/services_provider.dart';
import 'package:school_management_system/utils/helpers.dart';

/// ديالوج لإضافة أو تعديل بيانات ولي أمر.
class GuardianFormDialog extends ConsumerStatefulWidget {
  final GuardianModel? guardian; // يكون null في حالة الإضافة

  const GuardianFormDialog({super.key, this.guardian});

  @override
  ConsumerState<GuardianFormDialog> createState() => _GuardianFormDialogState();
}

class _GuardianFormDialogState extends ConsumerState<GuardianFormDialog> {
  final _formKey = GlobalKey<FormState>();
  late final TextEditingController _nameController;
  late final TextEditingController _emailController;
  late final TextEditingController _phoneController;
  late final TextEditingController _passwordController;

  bool get _isEditMode => widget.guardian != null;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.guardian?.name ?? '');
    _emailController = TextEditingController(text: widget.guardian?.email ?? '');
    _phoneController =
        TextEditingController(text: widget.guardian?.phoneNumber ?? '');
    _passwordController = TextEditingController();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  Future<void> _submit() async {
    if (_formKey.currentState!.validate()) {
      try {
        final firebaseService = ref.read(firebaseServiceProvider);
        if (_isEditMode) {
          // وضع التعديل
          await firebaseService.updateGuardian(
            widget.guardian!.id,
            _nameController.text,
            _phoneController.text,
          );
          showSuccessSnackBar(context, 'تم تحديث بيانات ولي الأمر بنجاح.');
        } else {
          // وضع الإضافة
          await firebaseService.addGuardian(
            _nameController.text,
            _emailController.text,
            _phoneController.text,
            _passwordController.text,
          );
          showSuccessSnackBar(context, 'تمت إضافة ولي الأمر بنجاح.');
        }
        Navigator.of(context).pop(); // إغلاق الديالوج عند النجاح
      } catch (e) {
        showErrorSnackBar(context, 'حدث خطأ: ${e.toString()}');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
          _isEditMode ? 'تعديل بيانات ولي الأمر' : 'إضافة ولي أمر جديد'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                  validator: (v) => v!.isEmpty ? 'الاسم مطلوب' : null),
              if (!_isEditMode)
                TextFormField(
                    controller: _emailController,
                    decoration:
                        const InputDecoration(labelText: 'البريد الإلكتروني'),
                    validator: (v) =>
                        v!.isEmpty || !v.contains('@') ? 'بريد إلكتروني غير صالح' : null),
              TextFormField(
                  controller: _phoneController,
                  decoration: const InputDecoration(labelText: 'رقم الهاتف'),
                  validator: (v) => v!.isEmpty ? 'رقم الهاتف مطلوب' : null),
              if (!_isEditMode)
                TextFormField(
                    controller: _passwordController,
                    decoration: const InputDecoration(labelText: 'كلمة المرور'),
                    obscureText: true,
                    validator: (v) =>
                        v!.length < 6 ? 'كلمة المرور قصيرة جداً' : null),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('إلغاء')),
        ElevatedButton(
          onPressed: _submit,
          child: const Text('حفظ'),
        ),
      ],
    );
  }
}
