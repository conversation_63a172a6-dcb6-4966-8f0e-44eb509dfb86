import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/request_management_model.dart';

/// خدمة إدارة الطلبات المتقدمة
/// 
/// تقدم هذه الخدمة جميع الوظائف المطلوبة لإدارة طلبات أولياء الأمور والطلاب
/// في النظام المدرسي بما في ذلك إنشاء وتعديل ومعالجة الطلبات، وإدارة الموافقات
/// 
/// الميزات الرئيسية:
/// - إنشاء طلبات جديدة مع جميع التفاصيل المطلوبة
/// - نظام موافقات متدرج حسب نوع الطلب
/// - إدارة المرفقات والوثائق المطلوبة
/// - تتبع شامل لحالة الطلب عبر جميع المراحل
/// - نظام إشعارات تلقائية لجميع الأطراف
/// - إدارة الرسوم والمدفوعات
/// - تقييم جودة الخدمة
/// - تقارير وإحصائيات شاملة
/// - نظام تذكيرات ومتابعة
/// - ربط الطلبات بالأنظمة الأخرى
class RequestManagementService {
  /// مرجع مجموعة الطلبات في Firestore
  final CollectionReference _requestsCollection = 
      FirebaseFirestore.instance.collection('requests');
  
  /// مرجع مجموعة أرقام الطلبات للتسلسل
  final CollectionReference _requestNumbersCollection = 
      FirebaseFirestore.instance.collection('request_numbers');
  
  /// مرجع مجموعة المستخدمين للحصول على معلومات المستخدمين
  final CollectionReference _usersCollection = 
      FirebaseFirestore.instance.collection('users');
  
  /// مرجع مجموعة الطلاب للحصول على معلومات الطلاب
  final CollectionReference _studentsCollection = 
      FirebaseFirestore.instance.collection('students');

  // ===================================================================
  // دوال إنشاء وإدارة الطلبات
  // ===================================================================

  /// إنشاء طلب جديد
  /// 
  /// يقوم بإنشاء طلب جديد مع جميع التفاصيل المطلوبة وتعيين رقم مرجعي فريد
  /// ويحدد المراحل والموافقات المطلوبة حسب نوع الطلب
  /// 
  /// [requesterId] معرف مقدم الطلب
  /// [requesterName] اسم مقدم الطلب
  /// [requesterType] نوع مقدم الطلب (ولي أمر، طالب، موظف)
  /// [studentId] معرف الطالب المرتبط بالطلب (اختياري)
  /// [studentName] اسم الطالب المرتبط بالطلب (اختياري)
  /// [classId] معرف الصف الدراسي (اختياري)
  /// [className] اسم الصف الدراسي (اختياري)
  /// [requestType] نوع الطلب الرئيسي
  /// [category] فئة الطلب الفرعية
  /// [priority] أولوية الطلب
  /// [title] عنوان الطلب
  /// [description] وصف تفصيلي للطلب
  /// [reason] السبب أو المبرر للطلب
  /// [additionalDetails] تفاصيل إضافية (اختياري)
  /// [requestedCompletionDate] التاريخ المطلوب لإنجاز الطلب (اختياري)
  /// [requiredDocuments] قائمة الوثائق المطلوبة
  /// [isUrgent] هل الطلب عاجل؟
  /// [requiresSpecialAttention] هل يحتاج متابعة خاصة؟
  /// [tags] العلامات والتصنيفات
  /// [privacyLevel] مستوى الخصوصية
  /// [metadata] معلومات إضافية
  /// 
  /// يرجع معرف الطلب الجديد
  Future<String> createRequest({
    required String requesterId,
    required String requesterName,
    required RequesterType requesterType,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    required RequestType requestType,
    required RequestCategory category,
    required RequestPriority priority,
    required String title,
    required String description,
    required String reason,
    String? additionalDetails,
    DateTime? requestedCompletionDate,
    List<RequiredDocument> requiredDocuments = const [],
    bool isUrgent = false,
    bool requiresSpecialAttention = false,
    List<String> tags = const [],
    PrivacyLevel privacyLevel = PrivacyLevel.normal,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات المدخلة
      _validateRequestData(
        requesterId: requesterId,
        requesterName: requesterName,
        title: title,
        description: description,
        reason: reason,
      );

      // إنشاء رقم مرجعي فريد للطلب
      final requestNumber = await _generateRequestNumber(requestType);

      // تحديد المراحل والموافقات المطلوبة حسب نوع الطلب
      final approvalSteps = _getRequiredApprovalSteps(requestType, category, priority);
      
      // تقدير الوقت المطلوب لإنجاز الطلب
      final estimatedCompletionDate = _calculateEstimatedCompletionDate(
        requestType, 
        category, 
        priority,
        requestedCompletionDate,
      );

      // إنشاء معرف فريد للطلب
      final requestRef = _requestsCollection.doc();
      final requestId = requestRef.id;

      // إنشاء نموذج الطلب
      final request = RequestManagementModel(
        id: requestId,
        requestNumber: requestNumber,
        requesterId: requesterId,
        requesterName: requesterName,
        requesterType: requesterType,
        studentId: studentId,
        studentName: studentName,
        classId: classId,
        className: className,
        requestType: requestType,
        category: category,
        priority: priority,
        status: RequestStatus.submitted,
        currentStage: ProcessingStage.initial,
        title: title,
        description: description,
        reason: reason,
        additionalDetails: additionalDetails,
        submittedAt: DateTime.now(),
        requestedCompletionDate: requestedCompletionDate,
        estimatedCompletionDate: estimatedCompletionDate,
        lastUpdated: DateTime.now(),
        requiredDocuments: requiredDocuments,
        approvalSteps: approvalSteps,
        currentApprovalStep: approvalSteps.isNotEmpty ? approvalSteps.first : null,
        isUrgent: isUrgent,
        requiresSpecialAttention: requiresSpecialAttention,
        tags: tags,
        privacyLevel: privacyLevel,
        metadata: metadata,
      );

      // حفظ الطلب في قاعدة البيانات
      await requestRef.set(request.toMap());

      // إضافة إجراء إنشاء الطلب
      await _addRequestAction(
        requestId,
        ActionType.created,
        'تم إنشاء الطلب',
        requesterId,
        requesterName,
      );

      // إرسال إشعارات للمسؤولين المعنيين
      await _sendNotificationsForNewRequest(request);

      // جدولة التذكيرات إذا كان الطلب عاجلاً
      if (isUrgent || requiresSpecialAttention) {
        await _scheduleUrgentReminders(requestId);
      }

      return requestId;
    } catch (e) {
      throw Exception('فشل في إنشاء الطلب: $e');
    }
  }

  /// الحصول على طلبات المستخدم
  /// 
  /// يرجع جميع طلبات المستخدم مع إمكانية الفلترة
  /// 
  /// [userId] معرف المستخدم
  /// [startDate] تاريخ البداية للفلترة (اختياري)
  /// [endDate] تاريخ النهاية للفلترة (اختياري)
  /// [status] حالة الطلب للفلترة (اختياري)
  /// [requestType] نوع الطلب للفلترة (اختياري)
  /// [category] فئة الطلب للفلترة (اختياري)
  /// [limit] عدد الطلبات المطلوب إرجاعها (افتراضي 50)
  Stream<List<RequestManagementModel>> getUserRequests(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
    RequestStatus? status,
    RequestType? requestType,
    RequestCategory? category,
    int limit = 50,
  }) {
    try {
      Query query = _requestsCollection
          .where('requesterId', isEqualTo: userId)
          .orderBy('submittedAt', descending: true)
          .limit(limit);

      // تطبيق فلاتر إضافية
      if (startDate != null) {
        query = query.where('submittedAt', isGreaterThanOrEqualTo: startDate);
      }
      
      if (endDate != null) {
        query = query.where('submittedAt', isLessThanOrEqualTo: endDate);
      }
      
      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }
      
      if (requestType != null) {
        query = query.where('requestType', isEqualTo: requestType.toString());
      }
      
      if (category != null) {
        query = query.where('category', isEqualTo: category.toString());
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => RequestManagementModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      throw Exception('فشل في جلب طلبات المستخدم: $e');
    }
  }

  /// الحصول على تفاصيل طلب محدد
  /// 
  /// [requestId] معرف الطلب
  /// يرجع تفاصيل الطلب أو null إذا لم يوجد
  Future<RequestManagementModel?> getRequest(String requestId) async {
    try {
      final doc = await _requestsCollection.doc(requestId).get();
      
      if (doc.exists) {
        return RequestManagementModel.fromFirestore(doc);
      }
      
      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل الطلب: $e');
    }
  }

  /// تحديث حالة الطلب
  /// 
  /// يقوم بتحديث حالة الطلب وإضافة الإجراءات المناسبة
  /// 
  /// [requestId] معرف الطلب
  /// [newStatus] الحالة الجديدة
  /// [updatedBy] معرف من قام بالتحديث
  /// [updatedByName] اسم من قام بالتحديث
  /// [notes] ملاحظات على التحديث (اختياري)
  Future<void> updateRequestStatus(
    String requestId,
    RequestStatus newStatus,
    String updatedBy,
    String updatedByName, {
    String? notes,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('الطلب غير موجود');
      }

      // تحديد المرحلة الجديدة حسب الحالة
      final newStage = _getStageForStatus(newStatus);

      // تحديث الطلب
      await _requestsCollection.doc(requestId).update({
        'status': newStatus.toString(),
        'currentStage': newStage.toString(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
        'lastUpdatedBy': updatedBy,
      });

      // إضافة إجراء تغيير الحالة
      await _addRequestAction(
        requestId,
        ActionType.statusChanged,
        'تم تغيير حالة الطلب إلى: ${newStatus.toString()}',
        updatedBy,
        updatedByName,
        notes: notes,
      );

      // إرسال إشعارات للمستخدم
      await _sendStatusUpdateNotification(request, newStatus);

      // تحديث تاريخ الإنجاز إذا كان الطلب مكتملاً
      if (newStatus == RequestStatus.completed) {
        await _requestsCollection.doc(requestId).update({
          'actualCompletionDate': Timestamp.fromDate(DateTime.now()),
        });
      }
    } catch (e) {
      throw Exception('فشل في تحديث حالة الطلب: $e');
    }
  }
