import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:school_management_system/models/request_management_model.dart';

/// خدمة إدارة الطلبات المتقدمة
///
/// تقدم هذه الخدمة جميع الوظائف المطلوبة لإدارة طلبات أولياء الأمور والطلاب
/// في النظام المدرسي بما في ذلك إنشاء وتعديل ومعالجة الطلبات، وإدارة الموافقات
///
/// الميزات الرئيسية:
/// - إنشاء طلبات جديدة مع جميع التفاصيل المطلوبة
/// - نظام موافقات متدرج حسب نوع الطلب
/// - إدارة المرفقات والوثائق المطلوبة
/// - تتبع شامل لحالة الطلب عبر جميع المراحل
/// - نظام إشعارات تلقائية لجميع الأطراف
/// - إدارة الرسوم والمدفوعات
/// - تقييم جودة الخدمة
/// - تقارير وإحصائيات شاملة
/// - نظام تذكيرات ومتابعة
/// - ربط الطلبات بالأنظمة الأخرى
class RequestManagementService {
  /// مرجع مجموعة الطلبات في Firestore
  final CollectionReference _requestsCollection = FirebaseFirestore.instance
      .collection('requests');

  /// مرجع مجموعة أرقام الطلبات للتسلسل
  final CollectionReference _requestNumbersCollection = FirebaseFirestore
      .instance
      .collection('request_numbers');

  /// مرجع مجموعة المستخدمين للحصول على معلومات المستخدمين
  final CollectionReference _usersCollection = FirebaseFirestore.instance
      .collection('users');

  /// مرجع مجموعة الطلاب للحصول على معلومات الطلاب
  final CollectionReference _studentsCollection = FirebaseFirestore.instance
      .collection('students');

  // ===================================================================
  // دوال إنشاء وإدارة الطلبات
  // ===================================================================

  /// إنشاء طلب جديد
  ///
  /// يقوم بإنشاء طلب جديد مع جميع التفاصيل المطلوبة وتعيين رقم مرجعي فريد
  /// ويحدد المراحل والموافقات المطلوبة حسب نوع الطلب
  ///
  /// [requesterId] معرف مقدم الطلب
  /// [requesterName] اسم مقدم الطلب
  /// [requesterType] نوع مقدم الطلب (ولي أمر، طالب، موظف)
  /// [studentId] معرف الطالب المرتبط بالطلب (اختياري)
  /// [studentName] اسم الطالب المرتبط بالطلب (اختياري)
  /// [classId] معرف الصف الدراسي (اختياري)
  /// [className] اسم الصف الدراسي (اختياري)
  /// [requestType] نوع الطلب الرئيسي
  /// [category] فئة الطلب الفرعية
  /// [priority] أولوية الطلب
  /// [title] عنوان الطلب
  /// [description] وصف تفصيلي للطلب
  /// [reason] السبب أو المبرر للطلب
  /// [additionalDetails] تفاصيل إضافية (اختياري)
  /// [requestedCompletionDate] التاريخ المطلوب لإنجاز الطلب (اختياري)
  /// [requiredDocuments] قائمة الوثائق المطلوبة
  /// [isUrgent] هل الطلب عاجل؟
  /// [requiresSpecialAttention] هل يحتاج متابعة خاصة؟
  /// [tags] العلامات والتصنيفات
  /// [privacyLevel] مستوى الخصوصية
  /// [metadata] معلومات إضافية
  ///
  /// يرجع معرف الطلب الجديد
  Future<String> createRequest({
    required String requesterId,
    required String requesterName,
    required RequesterType requesterType,
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    required RequestType requestType,
    required RequestCategory category,
    required RequestPriority priority,
    required String title,
    required String description,
    required String reason,
    String? additionalDetails,
    DateTime? requestedCompletionDate,
    List<RequiredDocument> requiredDocuments = const [],
    bool isUrgent = false,
    bool requiresSpecialAttention = false,
    List<String> tags = const [],
    PrivacyLevel privacyLevel = PrivacyLevel.normal,
    Map<String, dynamic> metadata = const {},
  }) async {
    try {
      // التحقق من صحة البيانات المدخلة
      _validateRequestData(
        requesterId: requesterId,
        requesterName: requesterName,
        title: title,
        description: description,
        reason: reason,
      );

      // إنشاء رقم مرجعي فريد للطلب
      final requestNumber = await _generateRequestNumber(requestType);

      // تحديد المراحل والموافقات المطلوبة حسب نوع الطلب
      final approvalSteps = _getRequiredApprovalSteps(
        requestType,
        category,
        priority,
      );

      // تقدير الوقت المطلوب لإنجاز الطلب
      final estimatedCompletionDate = _calculateEstimatedCompletionDate(
        requestType,
        category,
        priority,
        requestedCompletionDate,
      );

      // إنشاء معرف فريد للطلب
      final requestRef = _requestsCollection.doc();
      final requestId = requestRef.id;

      // إنشاء نموذج الطلب
      final request = RequestManagementModel(
        id: requestId,
        requestNumber: requestNumber,
        requesterId: requesterId,
        requesterName: requesterName,
        requesterType: requesterType,
        studentId: studentId,
        studentName: studentName,
        classId: classId,
        className: className,
        requestType: requestType,
        category: category,
        priority: priority,
        status: RequestStatus.submitted,
        currentStage: ProcessingStage.initial,
        title: title,
        description: description,
        reason: reason,
        additionalDetails: additionalDetails,
        submittedAt: DateTime.now(),
        requestedCompletionDate: requestedCompletionDate,
        estimatedCompletionDate: estimatedCompletionDate,
        lastUpdated: DateTime.now(),
        requiredDocuments: requiredDocuments,
        approvalSteps: approvalSteps,
        currentApprovalStep:
            approvalSteps.isNotEmpty ? approvalSteps.first : null,
        isUrgent: isUrgent,
        requiresSpecialAttention: requiresSpecialAttention,
        tags: tags,
        privacyLevel: privacyLevel,
        metadata: metadata,
      );

      // حفظ الطلب في قاعدة البيانات
      await requestRef.set(request.toMap());

      // إضافة إجراء إنشاء الطلب
      await _addRequestAction(
        requestId,
        ActionType.created,
        'تم إنشاء الطلب',
        requesterId,
        requesterName,
      );

      // إرسال إشعارات للمسؤولين المعنيين
      await _sendNotificationsForNewRequest(request);

      // جدولة التذكيرات إذا كان الطلب عاجلاً
      if (isUrgent || requiresSpecialAttention) {
        await _scheduleUrgentReminders(requestId);
      }

      return requestId;
    } catch (e) {
      throw Exception('فشل في إنشاء الطلب: $e');
    }
  }

  /// الحصول على طلبات المستخدم
  ///
  /// يرجع جميع طلبات المستخدم مع إمكانية الفلترة
  ///
  /// [userId] معرف المستخدم
  /// [startDate] تاريخ البداية للفلترة (اختياري)
  /// [endDate] تاريخ النهاية للفلترة (اختياري)
  /// [status] حالة الطلب للفلترة (اختياري)
  /// [requestType] نوع الطلب للفلترة (اختياري)
  /// [category] فئة الطلب للفلترة (اختياري)
  /// [limit] عدد الطلبات المطلوب إرجاعها (افتراضي 50)
  Stream<List<RequestManagementModel>> getUserRequests(
    String userId, {
    DateTime? startDate,
    DateTime? endDate,
    RequestStatus? status,
    RequestType? requestType,
    RequestCategory? category,
    int limit = 50,
  }) {
    try {
      Query query = _requestsCollection
          .where('requesterId', isEqualTo: userId)
          .orderBy('submittedAt', descending: true)
          .limit(limit);

      // تطبيق فلاتر إضافية
      if (startDate != null) {
        query = query.where('submittedAt', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('submittedAt', isLessThanOrEqualTo: endDate);
      }

      if (status != null) {
        query = query.where('status', isEqualTo: status.toString());
      }

      if (requestType != null) {
        query = query.where('requestType', isEqualTo: requestType.toString());
      }

      if (category != null) {
        query = query.where('category', isEqualTo: category.toString());
      }

      return query.snapshots().map((snapshot) {
        return snapshot.docs
            .map((doc) => RequestManagementModel.fromFirestore(doc))
            .toList();
      });
    } catch (e) {
      throw Exception('فشل في جلب طلبات المستخدم: $e');
    }
  }

  /// الحصول على تفاصيل طلب محدد
  ///
  /// [requestId] معرف الطلب
  /// يرجع تفاصيل الطلب أو null إذا لم يوجد
  Future<RequestManagementModel?> getRequest(String requestId) async {
    try {
      final doc = await _requestsCollection.doc(requestId).get();

      if (doc.exists) {
        return RequestManagementModel.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      throw Exception('فشل في جلب تفاصيل الطلب: $e');
    }
  }

  /// تحديث حالة الطلب
  ///
  /// يقوم بتحديث حالة الطلب وإضافة الإجراءات المناسبة
  ///
  /// [requestId] معرف الطلب
  /// [newStatus] الحالة الجديدة
  /// [updatedBy] معرف من قام بالتحديث
  /// [updatedByName] اسم من قام بالتحديث
  /// [notes] ملاحظات على التحديث (اختياري)
  Future<void> updateRequestStatus(
    String requestId,
    RequestStatus newStatus,
    String updatedBy,
    String updatedByName, {
    String? notes,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('الطلب غير موجود');
      }

      // تحديد المرحلة الجديدة حسب الحالة
      final newStage = _getStageForStatus(newStatus);

      // تحديث الطلب
      await _requestsCollection.doc(requestId).update({
        'status': newStatus.toString(),
        'currentStage': newStage.toString(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
        'lastUpdatedBy': updatedBy,
      });

      // إضافة إجراء تغيير الحالة
      await _addRequestAction(
        requestId,
        ActionType.statusChanged,
        'تم تغيير حالة الطلب إلى: ${_getStatusDescription(newStatus)}',
        updatedBy,
        updatedByName,
        notes: notes,
      );

      // إرسال إشعارات للمستخدم
      await _sendStatusUpdateNotification(request, newStatus);

      // تحديث تاريخ الإنجاز إذا كان الطلب مكتملاً
      if (newStatus == RequestStatus.completed) {
        await _requestsCollection.doc(requestId).update({
          'actualCompletionDate': Timestamp.fromDate(DateTime.now()),
        });
      }
    } catch (e) {
      throw Exception('فشل في تحديث حالة الطلب: $e');
    }
  }

  /// إضافة تعليق على الطلب
  ///
  /// [requestId] معرف الطلب
  /// [comment] نص التعليق
  /// [authorId] معرف كاتب التعليق
  /// [authorName] اسم كاتب التعليق
  /// [authorRole] دور كاتب التعليق
  /// [isInternal] هل التعليق داخلي؟
  Future<void> addComment(
    String requestId,
    String comment,
    String authorId,
    String authorName,
    String authorRole, {
    bool isInternal = false,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('الطلب غير موجود');
      }

      final newComment = RequestComment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        comment: comment,
        authorId: authorId,
        authorName: authorName,
        authorRole: authorRole,
        createdAt: DateTime.now(),
        isInternal: isInternal,
      );

      final updatedComments = List<RequestComment>.from(request.comments)
        ..add(newComment);

      await _requestsCollection.doc(requestId).update({
        'comments': updatedComments.map((c) => c.toMap()).toList(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
        'lastUpdatedBy': authorId,
      });

      // إضافة إجراء إضافة التعليق
      await _addRequestAction(
        requestId,
        ActionType.commentAdded,
        'تم إضافة تعليق',
        authorId,
        authorName,
      );

      // إرسال إشعار إذا لم يكن التعليق داخلياً
      if (!isInternal) {
        await _sendCommentNotification(request, newComment);
      }
    } catch (e) {
      throw Exception('فشل في إضافة التعليق: $e');
    }
  }

  /// رفع مرفق للطلب
  ///
  /// [requestId] معرف الطلب
  /// [fileName] اسم الملف
  /// [fileUrl] رابط الملف
  /// [documentType] نوع الوثيقة
  /// [fileType] نوع الملف
  /// [fileSizeBytes] حجم الملف بالبايت
  /// [uploadedBy] معرف من قام بالرفع
  /// [notes] ملاحظات على المرفق
  Future<void> uploadAttachment(
    String requestId,
    String fileName,
    String fileUrl,
    DocumentType documentType,
    String fileType,
    int fileSizeBytes,
    String uploadedBy, {
    String? notes,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('الطلب غير موجود');
      }

      final attachment = RequestAttachment(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        fileName: fileName,
        fileUrl: fileUrl,
        documentType: documentType,
        fileType: fileType,
        fileSizeBytes: fileSizeBytes,
        uploadedAt: DateTime.now(),
        uploadedBy: uploadedBy,
        notes: notes,
      );

      final updatedAttachments = List<RequestAttachment>.from(
        request.attachments,
      )..add(attachment);

      await _requestsCollection.doc(requestId).update({
        'attachments': updatedAttachments.map((a) => a.toMap()).toList(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
        'lastUpdatedBy': uploadedBy,
      });

      // إضافة إجراء رفع الوثيقة
      await _addRequestAction(
        requestId,
        ActionType.documentUploaded,
        'تم رفع وثيقة: $fileName',
        uploadedBy,
        'المستخدم',
      );

      // التحقق من اكتمال الوثائق المطلوبة
      await _checkDocumentCompleteness(requestId);
    } catch (e) {
      throw Exception('فشل في رفع المرفق: $e');
    }
  }

  /// الحصول على الطلبات المعلقة للموافقة
  ///
  /// [approverId] معرف المسؤول عن الموافقة
  /// [limit] عدد الطلبات المطلوب إرجاعها
  Stream<List<RequestManagementModel>> getPendingApprovalRequests(
    String approverId, {
    int limit = 50,
  }) {
    try {
      return _requestsCollection
          .where('currentApprovalStep.approverId', isEqualTo: approverId)
          .where(
            'currentApprovalStep.status',
            isEqualTo: ApprovalStatus.pending.toString(),
          )
          .orderBy('submittedAt', descending: true)
          .limit(limit)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs
                .map((doc) => RequestManagementModel.fromFirestore(doc))
                .toList();
          });
    } catch (e) {
      throw Exception('فشل في جلب الطلبات المعلقة: $e');
    }
  }

  /// الموافقة على طلب أو رفضه
  ///
  /// [requestId] معرف الطلب
  /// [approverId] معرف المسؤول عن الموافقة
  /// [approverName] اسم المسؤول عن الموافقة
  /// [isApproved] هل تمت الموافقة؟
  /// [notes] ملاحظات الموافقة أو الرفض
  Future<void> processApproval(
    String requestId,
    String approverId,
    String approverName,
    bool isApproved, {
    String? notes,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) {
        throw Exception('الطلب غير موجود');
      }

      if (request.currentApprovalStep == null) {
        throw Exception('لا توجد موافقة مطلوبة لهذا الطلب');
      }

      if (request.currentApprovalStep!.approverId != approverId) {
        throw Exception('غير مخول للموافقة على هذا الطلب');
      }

      // تحديث خطوة الموافقة الحالية
      final updatedApprovalStep = request.currentApprovalStep!.copyWith(
        status: isApproved ? ApprovalStatus.approved : ApprovalStatus.rejected,
        actionDate: DateTime.now(),
        notes: notes,
      );

      // تحديث قائمة خطوات الموافقة
      final updatedApprovalSteps =
          request.approvalSteps.map((step) {
            if (step.id == updatedApprovalStep.id) {
              return updatedApprovalStep;
            }
            return step;
          }).toList();

      // تحديد الخطوة التالية أو إنهاء عملية الموافقة
      ApprovalStep? nextApprovalStep;
      RequestStatus newStatus;

      if (isApproved) {
        // البحث عن الخطوة التالية
        final remainingSteps =
            updatedApprovalSteps
                .where((step) => step.status == ApprovalStatus.pending)
                .toList();

        if (remainingSteps.isNotEmpty) {
          // هناك خطوات موافقة أخرى
          remainingSteps.sort((a, b) => a.order.compareTo(b.order));
          nextApprovalStep = remainingSteps.first;
          newStatus = RequestStatus.pendingApproval;
        } else {
          // تمت جميع الموافقات
          newStatus = RequestStatus.approved;
        }
      } else {
        // تم رفض الطلب
        newStatus = RequestStatus.rejected;
      }

      // تحديث الطلب
      await _requestsCollection.doc(requestId).update({
        'approvalSteps': updatedApprovalSteps.map((s) => s.toMap()).toList(),
        'currentApprovalStep': nextApprovalStep?.toMap(),
        'status': newStatus.toString(),
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
        'lastUpdatedBy': approverId,
      });

      // إضافة إجراء الموافقة أو الرفض
      await _addRequestAction(
        requestId,
        isApproved ? ActionType.approved : ActionType.rejected,
        isApproved ? 'تمت الموافقة على الطلب' : 'تم رفض الطلب',
        approverId,
        approverName,
        notes: notes,
      );

      // إرسال إشعارات
      await _sendApprovalNotification(request, isApproved, notes);
    } catch (e) {
      throw Exception('فشل في معالجة الموافقة: $e');
    }
  }

  /// الحصول على إحصائيات الطلبات
  ///
  /// [startDate] تاريخ البداية (اختياري)
  /// [endDate] تاريخ النهاية (اختياري)
  /// يرجع إحصائيات شاملة للطلبات
  Future<Map<String, dynamic>> getRequestStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _requestsCollection;

      if (startDate != null) {
        query = query.where('submittedAt', isGreaterThanOrEqualTo: startDate);
      }

      if (endDate != null) {
        query = query.where('submittedAt', isLessThanOrEqualTo: endDate);
      }

      final snapshot = await query.get();
      final requests =
          snapshot.docs
              .map((doc) => RequestManagementModel.fromFirestore(doc))
              .toList();

      return _calculateRequestStatistics(requests);
    } catch (e) {
      throw Exception('فشل في حساب إحصائيات الطلبات: $e');
    }
  }

  // ===================================================================
  // الدوال المساعدة الخاصة
  // ===================================================================

  /// التحقق من صحة بيانات الطلب
  void _validateRequestData({
    required String requesterId,
    required String requesterName,
    required String title,
    required String description,
    required String reason,
  }) {
    if (requesterId.trim().isEmpty) {
      throw Exception('معرف مقدم الطلب مطلوب');
    }

    if (requesterName.trim().isEmpty) {
      throw Exception('اسم مقدم الطلب مطلوب');
    }

    if (title.trim().isEmpty) {
      throw Exception('عنوان الطلب مطلوب');
    }

    if (description.trim().isEmpty) {
      throw Exception('وصف الطلب مطلوب');
    }

    if (reason.trim().isEmpty) {
      throw Exception('سبب الطلب مطلوب');
    }
  }

  /// إنشاء رقم مرجعي فريد للطلب
  Future<String> _generateRequestNumber(RequestType requestType) async {
    try {
      final year = DateTime.now().year;
      final typeCode = _getRequestTypeCode(requestType);

      // الحصول على العداد الحالي لهذا النوع والسنة
      final counterDoc =
          await _requestNumbersCollection.doc('${typeCode}_$year').get();

      int counter = 1;
      if (counterDoc.exists) {
        counter = (counterDoc.data() as Map<String, dynamic>)['counter'] ?? 1;
        counter++;
      }

      // تحديث العداد
      await _requestNumbersCollection.doc('${typeCode}_$year').set({
        'counter': counter,
        'lastUpdated': Timestamp.fromDate(DateTime.now()),
      });

      // إنشاء الرقم المرجعي
      return '$typeCode-$year-${counter.toString().padLeft(4, '0')}';
    } catch (e) {
      // في حالة الفشل، استخدام timestamp
      return 'REQ-${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// الحصول على رمز نوع الطلب
  String _getRequestTypeCode(RequestType requestType) {
    switch (requestType) {
      case RequestType.certificate:
        return 'CERT';
      case RequestType.document:
        return 'DOC';
      case RequestType.meeting:
        return 'MEET';
      case RequestType.complaint:
        return 'COMP';
      case RequestType.suggestion:
        return 'SUGG';
      case RequestType.leave:
        return 'LEAVE';
      case RequestType.transfer:
        return 'TRANS';
      case RequestType.enrollment:
        return 'ENRL';
      case RequestType.withdrawal:
        return 'WITH';
      case RequestType.transcript:
        return 'TRAN';
      case RequestType.recommendation:
        return 'REC';
      case RequestType.verification:
        return 'VER';
      case RequestType.other:
        return 'OTHER';
    }
  }

  /// تحديد المراحل والموافقات المطلوبة حسب نوع الطلب
  List<ApprovalStep> _getRequiredApprovalSteps(
    RequestType requestType,
    RequestCategory category,
    RequestPriority priority,
  ) {
    final List<ApprovalStep> steps = [];

    // تحديد الموافقات حسب نوع الطلب
    switch (requestType) {
      case RequestType.certificate:
      case RequestType.transcript:
        steps.add(
          ApprovalStep(
            id: 'academic_approval',
            name: 'موافقة أكاديمية',
            description: 'موافقة من القسم الأكاديمي',
            order: 1,
            approverId: 'academic_officer',
            approverName: 'مسؤول أكاديمي',
            approverRole: 'academic_officer',
          ),
        );
        break;

      case RequestType.meeting:
        steps.add(
          ApprovalStep(
            id: 'admin_approval',
            name: 'موافقة إدارية',
            description: 'موافقة من الإدارة',
            order: 1,
            approverId: 'admin_officer',
            approverName: 'مسؤول إداري',
            approverRole: 'admin_officer',
          ),
        );
        break;

      case RequestType.complaint:
        steps.add(
          ApprovalStep(
            id: 'disciplinary_review',
            name: 'مراجعة تأديبية',
            description: 'مراجعة من اللجنة التأديبية',
            order: 1,
            approverId: 'disciplinary_officer',
            approverName: 'مسؤول تأديبي',
            approverRole: 'disciplinary_officer',
          ),
        );
        break;

      default:
        // موافقة عامة للطلبات الأخرى
        steps.add(
          ApprovalStep(
            id: 'general_approval',
            name: 'موافقة عامة',
            description: 'موافقة عامة من الإدارة',
            order: 1,
            approverId: 'general_officer',
            approverName: 'مسؤول عام',
            approverRole: 'general_officer',
          ),
        );
    }

    // إضافة موافقة إضافية للطلبات العاجلة أو الحرجة
    if (priority == RequestPriority.urgent ||
        priority == RequestPriority.critical) {
      steps.add(
        ApprovalStep(
          id: 'director_approval',
          name: 'موافقة المدير',
          description: 'موافقة من مدير المدرسة',
          order: steps.length + 1,
          approverId: 'school_director',
          approverName: 'مدير المدرسة',
          approverRole: 'director',
        ),
      );
    }

    return steps;
  }

  /// تقدير الوقت المطلوب لإنجاز الطلب
  DateTime _calculateEstimatedCompletionDate(
    RequestType requestType,
    RequestCategory category,
    RequestPriority priority,
    DateTime? requestedDate,
  ) {
    final now = DateTime.now();
    int estimatedDays = 7; // افتراضي أسبوع واحد

    // تحديد الوقت حسب نوع الطلب
    switch (requestType) {
      case RequestType.certificate:
      case RequestType.transcript:
        estimatedDays = 5; // 5 أيام عمل
        break;
      case RequestType.document:
        estimatedDays = 3; // 3 أيام عمل
        break;
      case RequestType.meeting:
        estimatedDays = 2; // يومان
        break;
      case RequestType.complaint:
        estimatedDays = 10; // 10 أيام للتحقيق
        break;
      case RequestType.transfer:
      case RequestType.enrollment:
        estimatedDays = 14; // أسبوعان
        break;
      default:
        estimatedDays = 7;
    }

    // تعديل الوقت حسب الأولوية
    switch (priority) {
      case RequestPriority.urgent:
        estimatedDays = (estimatedDays * 0.5).round();
        break;
      case RequestPriority.critical:
        estimatedDays = (estimatedDays * 0.3).round();
        break;
      case RequestPriority.low:
        estimatedDays = (estimatedDays * 1.5).round();
        break;
      default:
        break;
    }

    // التأكد من الحد الأدنى يوم واحد
    if (estimatedDays < 1) estimatedDays = 1;

    final estimatedDate = now.add(Duration(days: estimatedDays));

    // إذا كان هناك تاريخ مطلوب، استخدم الأقرب
    if (requestedDate != null) {
      return requestedDate.isBefore(estimatedDate)
          ? requestedDate
          : estimatedDate;
    }

    return estimatedDate;
  }

  /// تحديد المرحلة حسب الحالة
  ProcessingStage _getStageForStatus(RequestStatus status) {
    switch (status) {
      case RequestStatus.draft:
      case RequestStatus.submitted:
        return ProcessingStage.initial;
      case RequestStatus.underReview:
        return ProcessingStage.review;
      case RequestStatus.pendingDocuments:
        return ProcessingStage.documentation;
      case RequestStatus.pendingApproval:
        return ProcessingStage.approval;
      case RequestStatus.approved:
      case RequestStatus.inProgress:
        return ProcessingStage.processing;
      case RequestStatus.completed:
        return ProcessingStage.completion;
      case RequestStatus.rejected:
      case RequestStatus.cancelled:
      case RequestStatus.onHold:
        return ProcessingStage.initial;
    }
  }

  /// الحصول على وصف الحالة
  String _getStatusDescription(RequestStatus status) {
    switch (status) {
      case RequestStatus.draft:
        return 'مسودة';
      case RequestStatus.submitted:
        return 'مُقدم';
      case RequestStatus.underReview:
        return 'قيد المراجعة';
      case RequestStatus.pendingDocuments:
        return 'في انتظار المستندات';
      case RequestStatus.pendingApproval:
        return 'في انتظار الموافقة';
      case RequestStatus.approved:
        return 'موافق عليه';
      case RequestStatus.inProgress:
        return 'قيد التنفيذ';
      case RequestStatus.completed:
        return 'مكتمل';
      case RequestStatus.rejected:
        return 'مرفوض';
      case RequestStatus.cancelled:
        return 'ملغي';
      case RequestStatus.onHold:
        return 'معلق';
    }
  }

  /// إضافة إجراء للطلب
  Future<void> _addRequestAction(
    String requestId,
    ActionType actionType,
    String description,
    String performedBy,
    String performedByName, {
    String? notes,
  }) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) return;

      final action = RequestAction(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        actionType: actionType,
        description: description,
        performedBy: performedBy,
        performedByName: performedByName,
        performedAt: DateTime.now(),
        notes: notes,
      );

      final updatedActions = List<RequestAction>.from(request.actions)
        ..add(action);

      await _requestsCollection.doc(requestId).update({
        'actions': updatedActions.map((a) => a.toMap()).toList(),
      });
    } catch (e) {
      print('خطأ في إضافة الإجراء: $e');
    }
  }

  /// إرسال إشعارات للطلب الجديد
  Future<void> _sendNotificationsForNewRequest(
    RequestManagementModel request,
  ) async {
    try {
      // TODO: تنفيذ إرسال الإشعارات
      print('تم إرسال إشعار للطلب الجديد: ${request.requestNumber}');
    } catch (e) {
      print('خطأ في إرسال الإشعارات: $e');
    }
  }

  /// جدولة التذكيرات العاجلة
  Future<void> _scheduleUrgentReminders(String requestId) async {
    try {
      // TODO: تنفيذ جدولة التذكيرات
      print('تم جدولة تذكيرات عاجلة للطلب: $requestId');
    } catch (e) {
      print('خطأ في جدولة التذكيرات: $e');
    }
  }

  /// إرسال إشعار تحديث الحالة
  Future<void> _sendStatusUpdateNotification(
    RequestManagementModel request,
    RequestStatus newStatus,
  ) async {
    try {
      // TODO: تنفيذ إرسال إشعار تحديث الحالة
      print('تم إرسال إشعار تحديث الحالة للطلب: ${request.requestNumber}');
    } catch (e) {
      print('خطأ في إرسال إشعار تحديث الحالة: $e');
    }
  }

  /// إرسال إشعار التعليق
  Future<void> _sendCommentNotification(
    RequestManagementModel request,
    RequestComment comment,
  ) async {
    try {
      // TODO: تنفيذ إرسال إشعار التعليق
      print('تم إرسال إشعار تعليق للطلب: ${request.requestNumber}');
    } catch (e) {
      print('خطأ في إرسال إشعار التعليق: $e');
    }
  }

  /// التحقق من اكتمال الوثائق
  Future<void> _checkDocumentCompleteness(String requestId) async {
    try {
      final request = await getRequest(requestId);
      if (request == null) return;

      if (request.hasAllRequiredDocuments) {
        await updateRequestStatus(
          requestId,
          RequestStatus.underReview,
          'system',
          'النظام',
          notes: 'تم استكمال جميع الوثائق المطلوبة',
        );
      }
    } catch (e) {
      print('خطأ في التحقق من اكتمال الوثائق: $e');
    }
  }

  /// إرسال إشعار الموافقة
  Future<void> _sendApprovalNotification(
    RequestManagementModel request,
    bool isApproved,
    String? notes,
  ) async {
    try {
      // TODO: تنفيذ إرسال إشعار الموافقة
      print('تم إرسال إشعار موافقة للطلب: ${request.requestNumber}');
    } catch (e) {
      print('خطأ في إرسال إشعار الموافقة: $e');
    }
  }

  /// حساب إحصائيات الطلبات
  Map<String, dynamic> _calculateRequestStatistics(
    List<RequestManagementModel> requests,
  ) {
    if (requests.isEmpty) {
      return {
        'totalRequests': 0,
        'completedRequests': 0,
        'pendingRequests': 0,
        'rejectedRequests': 0,
        'averageCompletionTime': 0.0,
        'requestsByType': <String, int>{},
        'requestsByStatus': <String, int>{},
      };
    }

    int totalRequests = requests.length;
    int completedRequests = 0;
    int pendingRequests = 0;
    int rejectedRequests = 0;
    double totalCompletionTime = 0.0;
    int completedWithTime = 0;

    Map<String, int> requestsByType = {};
    Map<String, int> requestsByStatus = {};

    for (final request in requests) {
      // حساب الحالات
      switch (request.status) {
        case RequestStatus.completed:
          completedRequests++;
          if (request.actualCompletionDate != null) {
            final completionTime =
                request.actualCompletionDate!
                    .difference(request.submittedAt)
                    .inDays
                    .toDouble();
            totalCompletionTime += completionTime;
            completedWithTime++;
          }
          break;
        case RequestStatus.rejected:
        case RequestStatus.cancelled:
          rejectedRequests++;
          break;
        default:
          pendingRequests++;
      }

      // حساب الأنواع
      final typeKey = request.requestType.toString();
      requestsByType[typeKey] = (requestsByType[typeKey] ?? 0) + 1;

      // حساب الحالات
      final statusKey = request.status.toString();
      requestsByStatus[statusKey] = (requestsByStatus[statusKey] ?? 0) + 1;
    }

    final averageCompletionTime =
        completedWithTime > 0 ? totalCompletionTime / completedWithTime : 0.0;

    return {
      'totalRequests': totalRequests,
      'completedRequests': completedRequests,
      'pendingRequests': pendingRequests,
      'rejectedRequests': rejectedRequests,
      'averageCompletionTime': averageCompletionTime,
      'requestsByType': requestsByType,
      'requestsByStatus': requestsByStatus,
      'completionRate':
          totalRequests > 0 ? (completedRequests / totalRequests) * 100 : 0.0,
      'rejectionRate':
          totalRequests > 0 ? (rejectedRequests / totalRequests) * 100 : 0.0,
    };
  }
}
