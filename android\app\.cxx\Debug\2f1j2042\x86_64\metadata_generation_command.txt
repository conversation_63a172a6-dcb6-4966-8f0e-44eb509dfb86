                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=23
-DANDROID_PLATFORM=android-23
-DANDROID_ABI=x86_64
-DCMAKE_ANDROID_ARCH_ABI=x86_64
-DANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=F:\baha\Baha\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=F:\baha\Baha\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\Users\<USER>\StudioProjects\school_management_system\build\app\intermediates\cxx\Debug\2f1j2042\obj\x86_64
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\Users\<USER>\StudioProjects\school_management_system\build\app\intermediates\cxx\Debug\2f1j2042\obj\x86_64
-DCMAKE_BUILD_TYPE=Debug
-BC:\Users\<USER>\StudioProjects\school_management_system\android\app\.cxx\Debug\2f1j2042\x86_64
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2