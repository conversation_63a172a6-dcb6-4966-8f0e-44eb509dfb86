import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:school_management_system/models/note_model.dart';
import 'package:school_management_system/models/user_model.dart';
import 'package:school_management_system/widgets/custom_card.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

/// شاشة عرض ملاحظات الطلاب والرد عليها، مع ميزات محسنة للرد والبحث وتتبع الحالة.
class StudentFeedbackScreen extends StatefulWidget {
  const StudentFeedbackScreen({super.key});

  @override
  State<StudentFeedbackScreen> createState() => _StudentFeedbackScreenState();
}

class _StudentFeedbackScreenState extends State<StudentFeedbackScreen> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  String _searchQuery = ''; // لتخزين نص البحث المدخل من المستخدم

  /// دالة لفتح نافذة حوارية (Dialog) للمسؤول لكتابة رد على ملاحظة الطالب.
  /// عند إرسال الرد، يتم تحديث مستند الملاحظة في Firestore.
  void _showReplyDialog(NoteModel note) {
    final replyController = TextEditingController();
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text('الرد على ملاحظة: ${note.title}'),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: replyController,
              decoration: const InputDecoration(labelText: 'اكتب ردك هنا...'),
              maxLines: 3,
              validator: (value) => value!.isEmpty ? 'الرد لا يمكن أن يكون فارغاً' : null,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('إلغاء'),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  try {
                    // تحديث مستند الملاحظة بالرد الجديد وتغيير حالتها
                    await _firestore.collection('notes').doc(note.id).update({
                      'reply': replyController.text,
                      'isRead': true, // تمييزها كمقروءة
                      'repliedAt': Timestamp.now(),
                    });
                    Navigator.of(context).pop();
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('تم إرسال الرد بنجاح.')),
                    );
                  } catch (e) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('فشل إرسال الرد: $e')),
                    );
                  }
                }
              },
              child: const Text('إرسال الرد'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // --- حقل البحث ---
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: TextField(
            onChanged: (value) {
              setState(() {
                _searchQuery = value.toLowerCase();
              });
            },
            decoration: const InputDecoration(
              labelText: 'ابحث في الملاحظات...',
              prefixIcon: Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.all(Radius.circular(12.0)),
              ),
            ),
          ),
        ),
        Expanded(
          child: StreamBuilder<QuerySnapshot>(
            // 1. جلب جميع الملاحظات الموجهة للإدارة (بدون ترتيب لتجنب الحاجة إلى فهرس).
            stream: _firestore
                .collection('notes')
                .where('receiverId', isEqualTo: 'admin')
                .snapshots(),
            builder: (context, noteSnapshot) {
              if (noteSnapshot.hasError) {
                return const Center(child: Text('حدث خطأ أثناء جلب البيانات.'));
              }
              if (noteSnapshot.connectionState == ConnectionState.waiting) {
                return const LoadingIndicator();
              }
              if (!noteSnapshot.hasData || noteSnapshot.data!.docs.isEmpty) {
                return const Center(child: Text('لا توجد ملاحظات من الطلاب حالياً.'));
              }

              var notes = noteSnapshot.data!.docs;

              // الترتيب اليدوي للبيانات حسب التاريخ (الأحدث أولاً)
              notes.sort((a, b) {
                var aTimestamp = (a.data() as Map<String, dynamic>)['timestamp'] as Timestamp;
                var bTimestamp = (b.data() as Map<String, dynamic>)['timestamp'] as Timestamp;
                return bTimestamp.compareTo(aTimestamp);
              });

              // --- تطبيق البحث على النتائج (على الواجهة) ---
              if (_searchQuery.isNotEmpty) {
                notes = notes.where((doc) {
                  final data = doc.data() as Map<String, dynamic>;
                  final title = data['title']?.toLowerCase() ?? '';
                  final content = data['content']?.toLowerCase() ?? '';
                  return title.contains(_searchQuery) || content.contains(_searchQuery);
                }).toList();
              }
              
              if (notes.isEmpty) {
                return const Center(child: Text('لم يتم العثور على نتائج للبحث.'));
              }

              // 2. استخراج IDs الطلاب المرسلين للملاحظات (مع إزالة التكرار) لتحسين الأداء.
              final userIds = notes.map((doc) => doc['senderId'] as String).toSet().toList();

              if (userIds.isEmpty) {
                return const Center(child: Text('لا توجد ملاحظات مرتبطة بطلاب.'));
              }

              // 3. جلب بيانات هؤلاء الطلاب دفعة واحدة باستخدام `whereIn` لتجنب الاستعلامات المتكررة.
              return FutureBuilder<QuerySnapshot>(
                future: _firestore.collection('users').where(FieldPath.documentId, whereIn: userIds).get(),
                builder: (context, userSnapshot) {
                  if (userSnapshot.connectionState == ConnectionState.waiting) {
                    return const LoadingIndicator();
                  }
                  if (userSnapshot.hasError || !userSnapshot.hasData) {
                    return const Center(child: Text('فشل في جلب بيانات الطلاب.'));
                  }

                  // 4. إنشاء خريطة (Map) لأسماء الطلاب لتسهيل الوصول إليها بسرعة داخل ListView.
                  final usersMap = {for (var doc in userSnapshot.data!.docs) doc.id: UserModel.fromMap(doc.data() as Map<String, dynamic>, doc.id)};

                  return ListView.builder(
                    padding: const EdgeInsets.all(16.0),
                    itemCount: notes.length,
                    itemBuilder: (context, index) {
                      final noteDoc = notes[index];
                      final note = NoteModel.fromMap(noteDoc.data() as Map<String, dynamic>, noteDoc.id);
                      final studentName = usersMap[note.senderId]?.name ?? 'طالب غير معروف';
                      final isRead = note.isRead;

                      // استخدام بطاقة مخصصة مع تغيير اللون بناءً على حالة القراءة.
                      return CustomCard(
                        color: isRead ? Colors.white : Colors.blue.shade50,
                        child: ExpansionTile(
                          leading: Icon(isRead ? Icons.drafts : Icons.mark_email_unread, color: isRead ? Colors.grey : Colors.blue),
                          title: Text(
                            '${note.title} - من: $studentName',
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: isRead ? FontWeight.normal : FontWeight.bold,
                            ),
                          ),
                          subtitle: Text(
                            DateFormat('yyyy/MM/dd - hh:mm a', 'ar').format(note.timestamp),
                            style: Theme.of(context).textTheme.bodySmall,
                          ),
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(note.content, style: const TextStyle(fontSize: 16)),
                                  const Divider(height: 20),
                                  // عرض الرد إذا كان موجودًا، أو زر الرد إذا لم يكن هناك رد.
                                  if (note.reply != null && note.reply!.isNotEmpty)
                                    Container(
                                      width: double.infinity,
                                      padding: const EdgeInsets.all(12),
                                      decoration: BoxDecoration(
                                        color: Colors.grey.shade200,
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          const Text('الرد:', style: TextStyle(fontWeight: FontWeight.bold)),
                                          const SizedBox(height: 4),
                                          Text(note.reply!),
                                          if (note.repliedAt != null)
                                            Padding(
                                              padding: const EdgeInsets.only(top: 8.0),
                                              child: Text(
                                                'تاريخ الرد: ${DateFormat('yyyy/MM/dd', 'ar').format(note.repliedAt!)}',
                                                style: Theme.of(context).textTheme.bodySmall,
                                              ),
                                            ),
                                        ],
                                      ),
                                    )
                                  else
                                    Align(
                                      alignment: Alignment.centerLeft,
                                      child: ElevatedButton.icon(
                                        icon: const Icon(Icons.reply),
                                        label: const Text('الرد على الملاحظة'),
                                        onPressed: () => _showReplyDialog(note),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}
