import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// تعداد لحالات الحضور المختلفة
/// يوفر طريقة منظمة للتعامل مع حالات الحضور
enum AttendanceStatus {
  present('حاضر'),
  absent('غائب'),
  late('متأخر'),
  excused('إجازة'),
  sick('مريض'),
  unknown('غير مسجل');

  const AttendanceStatus(this.arabicName);
  final String arabicName;

  /// الحصول على لون الحالة للعرض البصري
  Color get color {
    switch (this) {
      case AttendanceStatus.present:
        return const Color(0xFF4CAF50); // أخضر
      case AttendanceStatus.absent:
        return const Color(0xFFE53E3E); // أحمر
      case AttendanceStatus.late:
        return const Color(0xFFFF9800); // برتقالي
      case AttendanceStatus.excused:
        return const Color(0xFF2196F3); // أزرق
      case AttendanceStatus.sick:
        return const Color(0xFF9C27B0); // بنفسجي
      case AttendanceStatus.unknown:
        return const Color(0xFF757575); // رمادي
    }
  }

  /// الحصول على أيقونة الحالة
  IconData get icon {
    switch (this) {
      case AttendanceStatus.present:
        return Icons.check_circle;
      case AttendanceStatus.absent:
        return Icons.cancel;
      case AttendanceStatus.late:
        return Icons.access_time;
      case AttendanceStatus.excused:
        return Icons.event_available;
      case AttendanceStatus.sick:
        return Icons.local_hospital;
      case AttendanceStatus.unknown:
        return Icons.help_outline;
    }
  }

  /// تحويل النص العربي إلى enum
  static AttendanceStatus fromArabic(String arabicName) {
    for (AttendanceStatus status in AttendanceStatus.values) {
      if (status.arabicName == arabicName) {
        return status;
      }
    }
    return AttendanceStatus.unknown;
  }

  /// تحويل النص الإنجليزي إلى enum
  static AttendanceStatus fromEnglish(String englishName) {
    switch (englishName.toLowerCase()) {
      case 'present':
        return AttendanceStatus.present;
      case 'absent':
        return AttendanceStatus.absent;
      case 'late':
        return AttendanceStatus.late;
      case 'excused':
        return AttendanceStatus.excused;
      case 'sick':
        return AttendanceStatus.sick;
      default:
        return AttendanceStatus.unknown;
    }
  }
}

/// نموذج بيانات لسجل حضور يوم واحد
/// يحتوي على جميع المعلومات المتعلقة بحضور الطالب في يوم معين
class AttendanceRecord {
  final String id; // معرف السجل
  final DateTime date; // تاريخ اليوم
  final AttendanceStatus status; // حالة الحضور
  final String? notes; // ملاحظات إضافية
  final DateTime? checkInTime; // وقت الوصول
  final DateTime? checkOutTime; // وقت المغادرة
  final String? recordedBy; // من قام بتسجيل الحضور
  final DateTime createdAt; // وقت إنشاء السجل
  final DateTime? updatedAt; // وقت آخر تحديث

  const AttendanceRecord({
    required this.id,
    required this.date,
    required this.status,
    this.notes,
    this.checkInTime,
    this.checkOutTime,
    this.recordedBy,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء كائن AttendanceRecord من بيانات Firestore
  factory AttendanceRecord.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};

    return AttendanceRecord(
      id: doc.id,
      date: (data['date'] as Timestamp? ?? Timestamp.now()).toDate(),
      status: AttendanceStatus.fromArabic(
        data['status'] as String? ?? 'غير مسجل',
      ),
      notes: data['notes'] as String?,
      checkInTime:
          data['checkInTime'] != null
              ? (data['checkInTime'] as Timestamp).toDate()
              : null,
      checkOutTime:
          data['checkOutTime'] != null
              ? (data['checkOutTime'] as Timestamp).toDate()
              : null,
      recordedBy: data['recordedBy'] as String?,
      createdAt: (data['createdAt'] as Timestamp? ?? Timestamp.now()).toDate(),
      updatedAt:
          data['updatedAt'] != null
              ? (data['updatedAt'] as Timestamp).toDate()
              : null,
    );
  }

  /// إنشاء كائن AttendanceRecord من Map (للتوافق مع الكود الحالي)
  factory AttendanceRecord.fromMap(Map<String, dynamic> data) {
    return AttendanceRecord(
      id: data['id'] as String? ?? '',
      date:
          data['date'] is Timestamp
              ? (data['date'] as Timestamp).toDate()
              : data['date'] as DateTime? ?? DateTime.now(),
      status: AttendanceStatus.fromArabic(
        data['status'] as String? ?? 'غير مسجل',
      ),
      notes: data['notes'] as String?,
      checkInTime:
          data['checkInTime'] is Timestamp
              ? (data['checkInTime'] as Timestamp).toDate()
              : data['checkInTime'] as DateTime?,
      checkOutTime:
          data['checkOutTime'] is Timestamp
              ? (data['checkOutTime'] as Timestamp).toDate()
              : data['checkOutTime'] as DateTime?,
      recordedBy: data['recordedBy'] as String?,
      createdAt:
          data['createdAt'] is Timestamp
              ? (data['createdAt'] as Timestamp).toDate()
              : data['createdAt'] as DateTime? ?? DateTime.now(),
      updatedAt:
          data['updatedAt'] is Timestamp
              ? (data['updatedAt'] as Timestamp).toDate()
              : data['updatedAt'] as DateTime?,
    );
  }

  /// تحويل كائن AttendanceRecord إلى Map لحفظه في Firestore
  Map<String, dynamic> toMap() {
    return {
      'date': Timestamp.fromDate(date),
      'status': status.arabicName,
      'notes': notes,
      'checkInTime':
          checkInTime != null ? Timestamp.fromDate(checkInTime!) : null,
      'checkOutTime':
          checkOutTime != null ? Timestamp.fromDate(checkOutTime!) : null,
      'recordedBy': recordedBy,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  /// إنشاء نسخة محدثة من السجل
  AttendanceRecord copyWith({
    String? id,
    DateTime? date,
    AttendanceStatus? status,
    String? notes,
    DateTime? checkInTime,
    DateTime? checkOutTime,
    String? recordedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AttendanceRecord(
      id: id ?? this.id,
      date: date ?? this.date,
      status: status ?? this.status,
      notes: notes ?? this.notes,
      checkInTime: checkInTime ?? this.checkInTime,
      checkOutTime: checkOutTime ?? this.checkOutTime,
      recordedBy: recordedBy ?? this.recordedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// التحقق من كون اليوم يوم دراسي (ليس عطلة نهاية أسبوع)
  bool get isSchoolDay {
    // الأحد = 7, الاثنين = 1, ..., الخميس = 4
    return date.weekday >= 1 && date.weekday <= 4 || date.weekday == 7;
  }

  /// التحقق من كون الطالب متأخراً (وصل بعد الساعة 8:00)
  bool get isLateArrival {
    if (checkInTime == null) return false;
    final schoolStartTime = DateTime(
      checkInTime!.year,
      checkInTime!.month,
      checkInTime!.day,
      8, // الساعة 8:00 صباحاً
      0,
    );
    return checkInTime!.isAfter(schoolStartTime);
  }

  /// حساب مدة التأخير بالدقائق
  int get lateMinutes {
    if (!isLateArrival || checkInTime == null) return 0;
    final schoolStartTime = DateTime(
      checkInTime!.year,
      checkInTime!.month,
      checkInTime!.day,
      8,
      0,
    );
    return checkInTime!.difference(schoolStartTime).inMinutes;
  }

  /// حساب مدة البقاء في المدرسة
  Duration? get stayDuration {
    if (checkInTime == null || checkOutTime == null) return null;
    return checkOutTime!.difference(checkInTime!);
  }

  @override
  String toString() {
    return 'AttendanceRecord(date: $date, status: ${status.arabicName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AttendanceRecord &&
        other.id == id &&
        other.date == date &&
        other.status == status;
  }

  @override
  int get hashCode {
    return id.hashCode ^ date.hashCode ^ status.hashCode;
  }
}

/// نموذج بيانات لإحصائيات الحضور
/// يوفر تحليلات مفيدة عن سجل حضور الطالب
class AttendanceStats {
  final List<AttendanceRecord> records;
  final DateTime? startDate;
  final DateTime? endDate;

  const AttendanceStats({required this.records, this.startDate, this.endDate});

  /// إجمالي عدد الأيام المسجلة
  int get totalDays => records.length;

  /// عدد أيام الحضور
  int get presentDays =>
      records.where((r) => r.status == AttendanceStatus.present).length;

  /// عدد أيام الغياب
  int get absentDays =>
      records.where((r) => r.status == AttendanceStatus.absent).length;

  /// عدد أيام التأخير
  int get lateDays =>
      records.where((r) => r.status == AttendanceStatus.late).length;

  /// عدد أيام الإجازة
  int get excusedDays =>
      records.where((r) => r.status == AttendanceStatus.excused).length;

  /// عدد أيام المرض
  int get sickDays =>
      records.where((r) => r.status == AttendanceStatus.sick).length;

  /// نسبة الحضور (من 0 إلى 1)
  double get attendanceRate {
    if (totalDays == 0) return 0.0;
    return (presentDays + lateDays) / totalDays;
  }

  /// نسبة الحضور كنسبة مئوية
  double get attendancePercentage => attendanceRate * 100;

  /// نسبة الغياب
  double get absenceRate {
    if (totalDays == 0) return 0.0;
    return absentDays / totalDays;
  }

  /// نسبة الغياب كنسبة مئوية
  double get absencePercentage => absenceRate * 100;

  /// متوسط وقت الوصول (بالدقائق من منتصف الليل)
  double? get averageArrivalTime {
    final recordsWithCheckIn =
        records.where((r) => r.checkInTime != null).toList();
    if (recordsWithCheckIn.isEmpty) return null;

    final totalMinutes = recordsWithCheckIn
        .map((r) => r.checkInTime!.hour * 60 + r.checkInTime!.minute)
        .reduce((a, b) => a + b);

    return totalMinutes / recordsWithCheckIn.length;
  }

  /// عدد مرات التأخير
  int get lateArrivals => records.where((r) => r.isLateArrival).length;

  /// متوسط دقائق التأخير
  double get averageLateMinutes {
    final lateRecords = records.where((r) => r.isLateArrival).toList();
    if (lateRecords.isEmpty) return 0.0;

    final totalLateMinutes = lateRecords
        .map((r) => r.lateMinutes)
        .reduce((a, b) => a + b);

    return totalLateMinutes / lateRecords.length;
  }

  /// الحصول على سجلات شهر معين
  List<AttendanceRecord> getRecordsForMonth(int year, int month) {
    return records.where((record) {
      return record.date.year == year && record.date.month == month;
    }).toList();
  }

  /// الحصول على سجلات أسبوع معين
  List<AttendanceRecord> getRecordsForWeek(DateTime weekStart) {
    final weekEnd = weekStart.add(const Duration(days: 6));
    return records.where((record) {
      return record.date.isAfter(weekStart.subtract(const Duration(days: 1))) &&
          record.date.isBefore(weekEnd.add(const Duration(days: 1)));
    }).toList();
  }

  /// الحصول على إحصائيات شهرية
  Map<String, int> getMonthlyStats(int year, int month) {
    final monthRecords = getRecordsForMonth(year, month);
    return {
      'present':
          monthRecords
              .where((r) => r.status == AttendanceStatus.present)
              .length,
      'absent':
          monthRecords.where((r) => r.status == AttendanceStatus.absent).length,
      'late':
          monthRecords.where((r) => r.status == AttendanceStatus.late).length,
      'excused':
          monthRecords
              .where((r) => r.status == AttendanceStatus.excused)
              .length,
      'sick':
          monthRecords.where((r) => r.status == AttendanceStatus.sick).length,
    };
  }

  /// الحصول على أكثر الأيام غياباً في الأسبوع
  String get mostAbsentWeekday {
    final weekdayAbsences = <int, int>{};

    for (final record in records) {
      if (record.status == AttendanceStatus.absent) {
        weekdayAbsences[record.date.weekday] =
            (weekdayAbsences[record.date.weekday] ?? 0) + 1;
      }
    }

    if (weekdayAbsences.isEmpty) return 'لا يوجد';

    final mostAbsentDay =
        weekdayAbsences.entries.reduce((a, b) => a.value > b.value ? a : b).key;

    const weekdays = [
      '',
      'الاثنين',
      'الثلاثاء',
      'الأربعاء',
      'الخميس',
      'الجمعة',
      'السبت',
      'الأحد',
    ];
    return weekdays[mostAbsentDay];
  }

  /// تقييم الحضور (ممتاز، جيد، مقبول، ضعيف)
  String get attendanceGrade {
    final percentage = attendancePercentage;
    if (percentage >= 95) return 'ممتاز';
    if (percentage >= 85) return 'جيد جداً';
    if (percentage >= 75) return 'جيد';
    if (percentage >= 65) return 'مقبول';
    return 'ضعيف';
  }

  /// لون تقييم الحضور
  Color get attendanceGradeColor {
    final percentage = attendancePercentage;
    if (percentage >= 95) return Colors.green;
    if (percentage >= 85) return Colors.lightGreen;
    if (percentage >= 75) return Colors.orange;
    if (percentage >= 65) return Colors.deepOrange;
    return Colors.red;
  }

  /// الحصول على اتجاه الحضور (تحسن، تراجع، ثابت)
  String get attendanceTrend {
    if (records.length < 10) return 'غير كافي للتحليل';

    final recentRecords = records.take(10).toList();
    final olderRecords = records.skip(10).take(10).toList();

    final recentAttendanceRate =
        recentRecords
            .where(
              (r) =>
                  r.status == AttendanceStatus.present ||
                  r.status == AttendanceStatus.late,
            )
            .length /
        recentRecords.length;

    final olderAttendanceRate =
        olderRecords
            .where(
              (r) =>
                  r.status == AttendanceStatus.present ||
                  r.status == AttendanceStatus.late,
            )
            .length /
        olderRecords.length;

    final difference = recentAttendanceRate - olderAttendanceRate;

    if (difference > 0.1) return 'تحسن ملحوظ';
    if (difference > 0.05) return 'تحسن طفيف';
    if (difference < -0.1) return 'تراجع ملحوظ';
    if (difference < -0.05) return 'تراجع طفيف';
    return 'مستقر';
  }
}
