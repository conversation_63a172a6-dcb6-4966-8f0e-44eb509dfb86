import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/models/behavior_evaluation_model.dart';
import 'package:school_management_system/services/behavior_evaluation_service.dart';

/// مزودات حالة نظام تقييم السلوك
///
/// تحتوي هذه الملف على جميع مزودات الحالة المطلوبة لإدارة تقييمات السلوك
/// في التطبيق، بما في ذلك جلب البيانات، إدارة الفلاتر، والإحصائيات
///
/// المزودات المتوفرة:
/// - مزود خدمة تقييم السلوك
/// - مزود تقييمات الطالب مع الفلترة
/// - مزود إحصائيات السلوك
/// - مزود التقييمات المطلوب متابعتها
/// - مزودات الفلاتر والبحث
/// - مزودات إدارة الحالة للنماذج
/// - مزودات النقاط والمكافآت
/// - مزودات التقارير والتحليلات

// ===================================================================
// مزودات الخدمات الأساسية
// ===================================================================

/// مزود خدمة تقييم السلوك
///
/// يوفر مثيل واحد من خدمة تقييم السلوك لاستخدامه في جميع أنحاء التطبيق
final behaviorEvaluationServiceProvider = Provider<BehaviorEvaluationService>((
  ref,
) {
  return BehaviorEvaluationService();
});

// ===================================================================
// مزودات البيانات الرئيسية
// ===================================================================

/// مزود تقييمات السلوك للطالب
///
/// يجلب جميع تقييمات السلوك للطالب المحدد مع إمكانية الفلترة
///
/// [studentId] معرف الطالب
/// [filters] فلاتر البحث (اختياري)
final studentBehaviorEvaluationsProvider = StreamProvider.family<
  List<BehaviorEvaluationModel>,
  StudentEvaluationParams
>((ref, params) {
  final service = ref.watch(behaviorEvaluationServiceProvider);

  return service.getStudentEvaluations(
    params.studentId,
    startDate: params.startDate,
    endDate: params.endDate,
    behaviorType: params.behaviorType,
    category: params.category,
    limit: params.limit,
  );
});

/// مزود تفاصيل تقييم محدد
///
/// يجلب تفاصيل تقييم سلوك محدد
///
/// [evaluationId] معرف التقييم
final behaviorEvaluationDetailsProvider =
    FutureProvider.family<BehaviorEvaluationModel?, String>((
      ref,
      evaluationId,
    ) async {
      final service = ref.watch(behaviorEvaluationServiceProvider);
      return await service.getEvaluation(evaluationId);
    });

/// مزود إحصائيات السلوك للطالب
///
/// يحسب ويجلب إحصائيات شاملة لسلوك الطالب
///
/// [params] معاملات الإحصائيات (معرف الطالب، التواريخ)
final studentBehaviorStatisticsProvider =
    FutureProvider.family<BehaviorStatistics, BehaviorStatisticsParams>((
      ref,
      params,
    ) async {
      final service = ref.watch(behaviorEvaluationServiceProvider);

      return await service.getStudentBehaviorStatistics(
        params.studentId,
        startDate: params.startDate,
        endDate: params.endDate,
      );
    });

/// مزود نقاط السلوك الحالية للطالب
///
/// يجلب إجمالي نقاط السلوك الحالية للطالب
///
/// [studentId] معرف الطالب
final studentBehaviorPointsProvider = FutureProvider.family<int, String>((
  ref,
  studentId,
) async {
  final service = ref.watch(behaviorEvaluationServiceProvider);
  return await service.getCurrentBehaviorPoints(studentId);
});

/// مزود التقييمات المطلوب متابعتها
///
/// يجلب التقييمات التي تحتاج متابعة في التاريخ المحدد
///
/// [date] التاريخ المحدد للمتابعة (افتراضي اليوم)
final evaluationsRequiringFollowUpProvider =
    StreamProvider.family<List<BehaviorEvaluationModel>, DateTime?>((
      ref,
      date,
    ) {
      final service = ref.watch(behaviorEvaluationServiceProvider);
      return service.getEvaluationsRequiringFollowUp(date: date);
    });

// ===================================================================
// مزودات الفلاتر والبحث
// ===================================================================

/// مزود فلاتر تقييمات السلوك
///
/// يدير حالة الفلاتر المطبقة على تقييمات السلوك
final behaviorEvaluationFiltersProvider = StateNotifierProvider<
  BehaviorEvaluationFiltersNotifier,
  BehaviorEvaluationFilters
>((ref) {
  return BehaviorEvaluationFiltersNotifier();
});

/// مزود نص البحث في التقييمات
///
/// يدير نص البحث المدخل من المستخدم
final behaviorEvaluationSearchQueryProvider = StateProvider<String>(
  (ref) => '',
);

/// مزود التقييمات المفلترة
///
/// يطبق الفلاتر ونص البحث على قائمة التقييمات
final filteredBehaviorEvaluationsProvider = Provider.family<
  List<BehaviorEvaluationModel>,
  List<BehaviorEvaluationModel>
>((ref, evaluations) {
  final filters = ref.watch(behaviorEvaluationFiltersProvider);
  final searchQuery = ref.watch(behaviorEvaluationSearchQueryProvider);

  return _applyFiltersAndSearch(evaluations, filters, searchQuery);
});

// ===================================================================
// مزودات إدارة النماذج
// ===================================================================

/// مزود حالة نموذج إنشاء تقييم جديد
///
/// يدير حالة النموذج أثناء إنشاء تقييم سلوك جديد
final createEvaluationFormProvider = StateNotifierProvider<
  CreateEvaluationFormNotifier,
  CreateEvaluationFormState
>((ref) {
  return CreateEvaluationFormNotifier();
});

/// مزود حالة تحديث التقييم
///
/// يدير حالة التحديث للتقييمات الموجودة
final updateEvaluationProvider =
    StateNotifierProvider<UpdateEvaluationNotifier, AsyncValue<void>>((ref) {
      final service = ref.watch(behaviorEvaluationServiceProvider);
      return UpdateEvaluationNotifier(service);
    });

// ===================================================================
// مزودات التحليلات والتقارير
// ===================================================================

/// مزود تحليل اتجاهات السلوك
///
/// يحلل اتجاهات السلوك للطالب عبر فترة زمنية
final behaviorTrendsProvider =
    FutureProvider.family<BehaviorTrends, BehaviorTrendsParams>((
      ref,
      params,
    ) async {
      final service = ref.watch(behaviorEvaluationServiceProvider);

      // جلب التقييمات للفترة المحددة
      final evaluations =
          await service
              .getStudentEvaluations(
                params.studentId,
                startDate: params.startDate,
                endDate: params.endDate,
              )
              .first;

      return _analyzeBehaviorTrends(evaluations, params.interval);
    });

/// مزود مقارنة السلوك بين الطلاب
///
/// يقارن أداء السلوك بين عدة طلاب
final behaviorComparisonProvider =
    FutureProvider.family<BehaviorComparison, List<String>>((
      ref,
      studentIds,
    ) async {
      final service = ref.watch(behaviorEvaluationServiceProvider);

      final Map<String, BehaviorStatistics> studentStats = {};

      for (final studentId in studentIds) {
        final stats = await service.getStudentBehaviorStatistics(studentId);
        studentStats[studentId] = stats;
      }

      return BehaviorComparison(studentStats);
    });

// ===================================================================
// الفئات المساعدة
// ===================================================================

/// معاملات جلب تقييمات الطالب
class StudentEvaluationParams {
  final String studentId;
  final DateTime? startDate;
  final DateTime? endDate;
  final BehaviorType? behaviorType;
  final BehaviorCategory? category;
  final int limit;

  const StudentEvaluationParams({
    required this.studentId,
    this.startDate,
    this.endDate,
    this.behaviorType,
    this.category,
    this.limit = 50,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is StudentEvaluationParams &&
          runtimeType == other.runtimeType &&
          studentId == other.studentId &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          behaviorType == other.behaviorType &&
          category == other.category &&
          limit == other.limit;

  @override
  int get hashCode =>
      studentId.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      behaviorType.hashCode ^
      category.hashCode ^
      limit.hashCode;
}

/// معاملات إحصائيات السلوك
class BehaviorStatisticsParams {
  final String studentId;
  final DateTime? startDate;
  final DateTime? endDate;

  const BehaviorStatisticsParams({
    required this.studentId,
    this.startDate,
    this.endDate,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BehaviorStatisticsParams &&
          runtimeType == other.runtimeType &&
          studentId == other.studentId &&
          startDate == other.startDate &&
          endDate == other.endDate;

  @override
  int get hashCode =>
      studentId.hashCode ^ startDate.hashCode ^ endDate.hashCode;
}

/// فلاتر تقييمات السلوك
class BehaviorEvaluationFilters {
  final DateTime? startDate;
  final DateTime? endDate;
  final BehaviorType? behaviorType;
  final BehaviorCategory? category;
  final SeverityLevel? severityLevel;
  final OverallRating? overallRating;
  final EvaluationStatus? status;
  final String? evaluatorId;

  const BehaviorEvaluationFilters({
    this.startDate,
    this.endDate,
    this.behaviorType,
    this.category,
    this.severityLevel,
    this.overallRating,
    this.status,
    this.evaluatorId,
  });

  BehaviorEvaluationFilters copyWith({
    DateTime? startDate,
    DateTime? endDate,
    BehaviorType? behaviorType,
    BehaviorCategory? category,
    SeverityLevel? severityLevel,
    OverallRating? overallRating,
    EvaluationStatus? status,
    String? evaluatorId,
  }) {
    return BehaviorEvaluationFilters(
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      behaviorType: behaviorType ?? this.behaviorType,
      category: category ?? this.category,
      severityLevel: severityLevel ?? this.severityLevel,
      overallRating: overallRating ?? this.overallRating,
      status: status ?? this.status,
      evaluatorId: evaluatorId ?? this.evaluatorId,
    );
  }

  /// إزالة جميع الفلاتر
  BehaviorEvaluationFilters clear() {
    return const BehaviorEvaluationFilters();
  }

  /// التحقق من وجود فلاتر نشطة
  bool get hasActiveFilters {
    return startDate != null ||
        endDate != null ||
        behaviorType != null ||
        category != null ||
        severityLevel != null ||
        overallRating != null ||
        status != null ||
        evaluatorId != null;
  }
}

// ===================================================================
// مدير حالة الفلاتر
// ===================================================================

/// مدير حالة فلاتر تقييمات السلوك
class BehaviorEvaluationFiltersNotifier
    extends StateNotifier<BehaviorEvaluationFilters> {
  BehaviorEvaluationFiltersNotifier()
    : super(const BehaviorEvaluationFilters());

  /// تحديث فلتر التاريخ
  void updateDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  /// تحديث فلتر نوع السلوك
  void updateBehaviorType(BehaviorType? behaviorType) {
    state = state.copyWith(behaviorType: behaviorType);
  }

  /// تحديث فلتر فئة السلوك
  void updateCategory(BehaviorCategory? category) {
    state = state.copyWith(category: category);
  }

  /// تحديث فلتر مستوى الشدة
  void updateSeverityLevel(SeverityLevel? severityLevel) {
    state = state.copyWith(severityLevel: severityLevel);
  }

  /// تحديث فلتر التقييم العام
  void updateOverallRating(OverallRating? overallRating) {
    state = state.copyWith(overallRating: overallRating);
  }

  /// تحديث فلتر حالة التقييم
  void updateStatus(EvaluationStatus? status) {
    state = state.copyWith(status: status);
  }

  /// تحديث فلتر المُقيِّم
  void updateEvaluator(String? evaluatorId) {
    state = state.copyWith(evaluatorId: evaluatorId);
  }

  /// إزالة جميع الفلاتر
  void clearFilters() {
    state = state.clear();
  }
}

// ===================================================================
// مدير حالة نموذج إنشاء التقييم
// ===================================================================

/// حالة نموذج إنشاء التقييم
class CreateEvaluationFormState {
  final String studentId;
  final String studentName;
  final String classId;
  final String className;
  final String evaluatorId;
  final String evaluatorName;
  final String evaluatorRole;
  final EvaluationType evaluationType;
  final BehaviorCategory category;
  final BehaviorType behaviorType;
  final SeverityLevel severityLevel;
  final OverallRating overallRating;
  final int pointsAwarded;
  final String title;
  final String description;
  final String context;
  final String location;
  final DateTime incidentDateTime;
  final Map<BehaviorCriteria, CriteriaRating> criteriaRatings;
  final List<BehaviorAction> actionsTaken;
  final List<String> recommendations;
  final String? subjectId;
  final String? subjectName;
  final BehaviorImprovementPlan? improvementPlan;
  final bool requiresFollowUp;
  final DateTime? followUpDate;
  final List<String> tags;
  final String? evaluatorNotes;
  final PrivacyLevel privacyLevel;
  final bool isValid;
  final bool isSubmitting;
  final String? errorMessage;

  CreateEvaluationFormState({
    this.studentId = '',
    this.studentName = '',
    this.classId = '',
    this.className = '',
    this.evaluatorId = '',
    this.evaluatorName = '',
    this.evaluatorRole = '',
    this.evaluationType = EvaluationType.daily,
    this.category = BehaviorCategory.discipline,
    this.behaviorType = BehaviorType.neutral,
    this.severityLevel = SeverityLevel.low,
    this.overallRating = OverallRating.acceptable,
    this.pointsAwarded = 0,
    this.title = '',
    this.description = '',
    this.context = '',
    this.location = '',
    DateTime? incidentDateTime,
    this.criteriaRatings = const {},
    this.actionsTaken = const [],
    this.recommendations = const [],
    this.subjectId,
    this.subjectName,
    this.improvementPlan,
    this.requiresFollowUp = false,
    this.followUpDate,
    this.tags = const [],
    this.evaluatorNotes,
    this.privacyLevel = PrivacyLevel.general,
    this.isValid = false,
    this.isSubmitting = false,
    this.errorMessage,
  }) : incidentDateTime = incidentDateTime ?? DateTime.now();

  CreateEvaluationFormState copyWith({
    String? studentId,
    String? studentName,
    String? classId,
    String? className,
    String? evaluatorId,
    String? evaluatorName,
    String? evaluatorRole,
    EvaluationType? evaluationType,
    BehaviorCategory? category,
    BehaviorType? behaviorType,
    SeverityLevel? severityLevel,
    OverallRating? overallRating,
    int? pointsAwarded,
    String? title,
    String? description,
    String? context,
    String? location,
    DateTime? incidentDateTime,
    Map<BehaviorCriteria, CriteriaRating>? criteriaRatings,
    List<BehaviorAction>? actionsTaken,
    List<String>? recommendations,
    String? subjectId,
    String? subjectName,
    BehaviorImprovementPlan? improvementPlan,
    bool? requiresFollowUp,
    DateTime? followUpDate,
    List<String>? tags,
    String? evaluatorNotes,
    PrivacyLevel? privacyLevel,
    bool? isValid,
    bool? isSubmitting,
    String? errorMessage,
  }) {
    return CreateEvaluationFormState(
      studentId: studentId ?? this.studentId,
      studentName: studentName ?? this.studentName,
      classId: classId ?? this.classId,
      className: className ?? this.className,
      evaluatorId: evaluatorId ?? this.evaluatorId,
      evaluatorName: evaluatorName ?? this.evaluatorName,
      evaluatorRole: evaluatorRole ?? this.evaluatorRole,
      evaluationType: evaluationType ?? this.evaluationType,
      category: category ?? this.category,
      behaviorType: behaviorType ?? this.behaviorType,
      severityLevel: severityLevel ?? this.severityLevel,
      overallRating: overallRating ?? this.overallRating,
      pointsAwarded: pointsAwarded ?? this.pointsAwarded,
      title: title ?? this.title,
      description: description ?? this.description,
      context: context ?? this.context,
      location: location ?? this.location,
      incidentDateTime: incidentDateTime ?? this.incidentDateTime,
      criteriaRatings: criteriaRatings ?? this.criteriaRatings,
      actionsTaken: actionsTaken ?? this.actionsTaken,
      recommendations: recommendations ?? this.recommendations,
      subjectId: subjectId ?? this.subjectId,
      subjectName: subjectName ?? this.subjectName,
      improvementPlan: improvementPlan ?? this.improvementPlan,
      requiresFollowUp: requiresFollowUp ?? this.requiresFollowUp,
      followUpDate: followUpDate ?? this.followUpDate,
      tags: tags ?? this.tags,
      evaluatorNotes: evaluatorNotes ?? this.evaluatorNotes,
      privacyLevel: privacyLevel ?? this.privacyLevel,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      errorMessage: errorMessage,
    );
  }
}

/// مدير حالة نموذج إنشاء التقييم
class CreateEvaluationFormNotifier
    extends StateNotifier<CreateEvaluationFormState> {
  CreateEvaluationFormNotifier() : super(CreateEvaluationFormState());

  /// تحديث معلومات الطالب
  void updateStudentInfo(
    String studentId,
    String studentName,
    String classId,
    String className,
  ) {
    state = state.copyWith(
      studentId: studentId,
      studentName: studentName,
      classId: classId,
      className: className,
    );
    _validateForm();
  }

  /// تحديث معلومات المُقيِّم
  void updateEvaluatorInfo(
    String evaluatorId,
    String evaluatorName,
    String evaluatorRole,
  ) {
    state = state.copyWith(
      evaluatorId: evaluatorId,
      evaluatorName: evaluatorName,
      evaluatorRole: evaluatorRole,
    );
    _validateForm();
  }

  /// تحديث تفاصيل التقييم
  void updateEvaluationDetails({
    EvaluationType? evaluationType,
    BehaviorCategory? category,
    BehaviorType? behaviorType,
    SeverityLevel? severityLevel,
    OverallRating? overallRating,
    int? pointsAwarded,
    String? title,
    String? description,
    String? context,
    String? location,
    DateTime? incidentDateTime,
  }) {
    state = state.copyWith(
      evaluationType: evaluationType,
      category: category,
      behaviorType: behaviorType,
      severityLevel: severityLevel,
      overallRating: overallRating,
      pointsAwarded: pointsAwarded,
      title: title,
      description: description,
      context: context,
      location: location,
      incidentDateTime: incidentDateTime,
    );
    _validateForm();
  }

  /// تحديث تقييمات المعايير
  void updateCriteriaRatings(
    Map<BehaviorCriteria, CriteriaRating> criteriaRatings,
  ) {
    state = state.copyWith(criteriaRatings: criteriaRatings);
    _validateForm();
  }

  /// إضافة إجراء
  void addAction(BehaviorAction action) {
    final updatedActions = List<BehaviorAction>.from(state.actionsTaken)
      ..add(action);
    state = state.copyWith(actionsTaken: updatedActions);
  }

  /// إزالة إجراء
  void removeAction(int index) {
    final updatedActions = List<BehaviorAction>.from(state.actionsTaken)
      ..removeAt(index);
    state = state.copyWith(actionsTaken: updatedActions);
  }

  /// تحديث التوصيات
  void updateRecommendations(List<String> recommendations) {
    state = state.copyWith(recommendations: recommendations);
  }

  /// إعادة تعيين النموذج
  void resetForm() {
    state = CreateEvaluationFormState();
  }

  /// التحقق من صحة النموذج
  void _validateForm() {
    final isValid =
        state.studentId.isNotEmpty &&
        state.evaluatorId.isNotEmpty &&
        state.title.isNotEmpty &&
        state.description.isNotEmpty &&
        state.context.isNotEmpty &&
        state.location.isNotEmpty &&
        state.criteriaRatings.isNotEmpty;

    state = state.copyWith(isValid: isValid);
  }
}

// ===================================================================
// مدير حالة تحديث التقييم
// ===================================================================

/// مدير حالة تحديث التقييم
class UpdateEvaluationNotifier extends StateNotifier<AsyncValue<void>> {
  final BehaviorEvaluationService _service;

  UpdateEvaluationNotifier(this._service) : super(const AsyncValue.data(null));

  /// تحديث تقييم موجود
  Future<void> updateEvaluation(
    String evaluationId,
    String updatedBy,
    Map<String, dynamic> updates,
  ) async {
    state = const AsyncValue.loading();

    try {
      await _service.updateEvaluation(evaluationId, updatedBy, updates);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// إنهاء التقييم
  Future<void> finalizeEvaluation(
    String evaluationId,
    String finalizedBy,
  ) async {
    state = const AsyncValue.loading();

    try {
      await _service.finalizeEvaluation(evaluationId, finalizedBy);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }

  /// حذف التقييم
  Future<void> deleteEvaluation(String evaluationId, String deletedBy) async {
    state = const AsyncValue.loading();

    try {
      await _service.deleteEvaluation(evaluationId, deletedBy);
      state = const AsyncValue.data(null);
    } catch (error, stackTrace) {
      state = AsyncValue.error(error, stackTrace);
    }
  }
}

// ===================================================================
// فئات التحليلات والتقارير
// ===================================================================

/// معاملات تحليل اتجاهات السلوك
class BehaviorTrendsParams {
  final String studentId;
  final DateTime startDate;
  final DateTime endDate;
  final TrendInterval interval;

  const BehaviorTrendsParams({
    required this.studentId,
    required this.startDate,
    required this.endDate,
    required this.interval,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is BehaviorTrendsParams &&
          runtimeType == other.runtimeType &&
          studentId == other.studentId &&
          startDate == other.startDate &&
          endDate == other.endDate &&
          interval == other.interval;

  @override
  int get hashCode =>
      studentId.hashCode ^
      startDate.hashCode ^
      endDate.hashCode ^
      interval.hashCode;
}

/// فترات التحليل
enum TrendInterval {
  daily, // يومي
  weekly, // أسبوعي
  monthly, // شهري
}

/// اتجاهات السلوك
class BehaviorTrends {
  final List<TrendPoint> points;
  final TrendDirection overallTrend;
  final double improvementRate;
  final Map<BehaviorCategory, TrendDirection> categoryTrends;

  const BehaviorTrends({
    required this.points,
    required this.overallTrend,
    required this.improvementRate,
    required this.categoryTrends,
  });
}

/// نقطة في اتجاه السلوك
class TrendPoint {
  final DateTime date;
  final double averageRating;
  final int totalPoints;
  final int evaluationCount;

  const TrendPoint({
    required this.date,
    required this.averageRating,
    required this.totalPoints,
    required this.evaluationCount,
  });
}

/// اتجاه التطور
enum TrendDirection {
  improving, // تحسن
  declining, // تراجع
  stable, // مستقر
}

/// مقارنة السلوك بين الطلاب
class BehaviorComparison {
  final Map<String, BehaviorStatistics> studentStats;

  const BehaviorComparison(this.studentStats);

  /// الطالب الأفضل أداءً
  String? get bestPerformingStudent {
    if (studentStats.isEmpty) return null;

    return studentStats.entries
        .reduce((a, b) => a.value.averageRating > b.value.averageRating ? a : b)
        .key;
  }

  /// متوسط الأداء العام
  double get overallAverage {
    if (studentStats.isEmpty) return 0.0;

    final totalRating = studentStats.values
        .map((stats) => stats.averageRating)
        .reduce((a, b) => a + b);

    return totalRating / studentStats.length;
  }
}

// ===================================================================
// الدوال المساعدة
// ===================================================================

/// تطبيق الفلاتر والبحث على قائمة التقييمات
List<BehaviorEvaluationModel> _applyFiltersAndSearch(
  List<BehaviorEvaluationModel> evaluations,
  BehaviorEvaluationFilters filters,
  String searchQuery,
) {
  var filteredEvaluations = evaluations;

  // تطبيق فلاتر التاريخ
  if (filters.startDate != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.incidentDateTime.isAfter(filters.startDate!))
            .toList();
  }

  if (filters.endDate != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.incidentDateTime.isBefore(filters.endDate!))
            .toList();
  }

  // تطبيق فلاتر نوع السلوك
  if (filters.behaviorType != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.behaviorType == filters.behaviorType)
            .toList();
  }

  // تطبيق فلاتر الفئة
  if (filters.category != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.category == filters.category)
            .toList();
  }

  // تطبيق فلاتر مستوى الشدة
  if (filters.severityLevel != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.severityLevel == filters.severityLevel)
            .toList();
  }

  // تطبيق فلاتر التقييم العام
  if (filters.overallRating != null) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.overallRating == filters.overallRating)
            .toList();
  }

  // تطبيق فلاتر الحالة
  if (filters.status != null) {
    filteredEvaluations =
        filteredEvaluations.where((e) => e.status == filters.status).toList();
  }

  // تطبيق فلاتر المُقيِّم
  if (filters.evaluatorId != null && filters.evaluatorId!.isNotEmpty) {
    filteredEvaluations =
        filteredEvaluations
            .where((e) => e.evaluatorId == filters.evaluatorId)
            .toList();
  }

  // تطبيق البحث النصي
  if (searchQuery.isNotEmpty) {
    final query = searchQuery.toLowerCase();
    filteredEvaluations =
        filteredEvaluations
            .where(
              (e) =>
                  e.title.toLowerCase().contains(query) ||
                  e.description.toLowerCase().contains(query) ||
                  e.context.toLowerCase().contains(query) ||
                  e.studentName.toLowerCase().contains(query) ||
                  e.evaluatorName.toLowerCase().contains(query),
            )
            .toList();
  }

  return filteredEvaluations;
}

/// تحليل اتجاهات السلوك
BehaviorTrends _analyzeBehaviorTrends(
  List<BehaviorEvaluationModel> evaluations,
  TrendInterval interval,
) {
  if (evaluations.isEmpty) {
    return const BehaviorTrends(
      points: [],
      overallTrend: TrendDirection.stable,
      improvementRate: 0.0,
      categoryTrends: {},
    );
  }

  // تجميع البيانات حسب الفترة الزمنية
  final Map<DateTime, List<BehaviorEvaluationModel>> groupedData = {};

  for (final evaluation in evaluations) {
    final groupDate = _getGroupDate(evaluation.incidentDateTime, interval);
    groupedData.putIfAbsent(groupDate, () => []).add(evaluation);
  }

  // إنشاء نقاط الاتجاه
  final points =
      groupedData.entries.map((entry) {
          final evaluationsInPeriod = entry.value;
          final averageRating =
              evaluationsInPeriod
                  .map((e) => e.averageCriteriaRating)
                  .reduce((a, b) => a + b) /
              evaluationsInPeriod.length;
          final totalPoints = evaluationsInPeriod
              .map((e) => e.pointsAwarded)
              .reduce((a, b) => a + b);

          return TrendPoint(
            date: entry.key,
            averageRating: averageRating,
            totalPoints: totalPoints,
            evaluationCount: evaluationsInPeriod.length,
          );
        }).toList()
        ..sort((a, b) => a.date.compareTo(b.date));

  // تحديد الاتجاه العام
  final overallTrend = _calculateOverallTrend(points);

  // حساب معدل التحسن
  final improvementRate = _calculateImprovementRate(points);

  // تحليل اتجاهات الفئات
  final categoryTrends = _analyzeCategoryTrends(evaluations);

  return BehaviorTrends(
    points: points,
    overallTrend: overallTrend,
    improvementRate: improvementRate,
    categoryTrends: categoryTrends,
  );
}

/// الحصول على تاريخ المجموعة حسب الفترة
DateTime _getGroupDate(DateTime date, TrendInterval interval) {
  switch (interval) {
    case TrendInterval.daily:
      return DateTime(date.year, date.month, date.day);
    case TrendInterval.weekly:
      final weekStart = date.subtract(Duration(days: date.weekday - 1));
      return DateTime(weekStart.year, weekStart.month, weekStart.day);
    case TrendInterval.monthly:
      return DateTime(date.year, date.month);
  }
}

/// حساب الاتجاه العام
TrendDirection _calculateOverallTrend(List<TrendPoint> points) {
  if (points.length < 2) return TrendDirection.stable;

  final firstHalf = points.take(points.length ~/ 2);
  final secondHalf = points.skip(points.length ~/ 2);

  final firstAverage =
      firstHalf.map((p) => p.averageRating).reduce((a, b) => a + b) /
      firstHalf.length;
  final secondAverage =
      secondHalf.map((p) => p.averageRating).reduce((a, b) => a + b) /
      secondHalf.length;

  const threshold = 0.1; // عتبة التغيير المعنوي

  if (secondAverage - firstAverage > threshold) {
    return TrendDirection.improving;
  } else if (firstAverage - secondAverage > threshold) {
    return TrendDirection.declining;
  } else {
    return TrendDirection.stable;
  }
}

/// حساب معدل التحسن
double _calculateImprovementRate(List<TrendPoint> points) {
  if (points.length < 2) return 0.0;

  final firstPoint = points.first;
  final lastPoint = points.last;

  if (firstPoint.averageRating == 0) return 0.0;

  return ((lastPoint.averageRating - firstPoint.averageRating) /
          firstPoint.averageRating) *
      100;
}

/// تحليل اتجاهات الفئات
Map<BehaviorCategory, TrendDirection> _analyzeCategoryTrends(
  List<BehaviorEvaluationModel> evaluations,
) {
  final Map<BehaviorCategory, TrendDirection> categoryTrends = {};

  for (final category in BehaviorCategory.values) {
    final categoryEvaluations =
        evaluations.where((e) => e.category == category).toList();

    if (categoryEvaluations.length < 2) {
      categoryTrends[category] = TrendDirection.stable;
      continue;
    }

    // تقسيم إلى نصفين وحساب المتوسط
    final midPoint = categoryEvaluations.length ~/ 2;
    final firstHalf = categoryEvaluations.take(midPoint);
    final secondHalf = categoryEvaluations.skip(midPoint);

    final firstAverage =
        firstHalf.map((e) => e.averageCriteriaRating).reduce((a, b) => a + b) /
        firstHalf.length;
    final secondAverage =
        secondHalf.map((e) => e.averageCriteriaRating).reduce((a, b) => a + b) /
        secondHalf.length;

    const threshold = 0.1;

    if (secondAverage - firstAverage > threshold) {
      categoryTrends[category] = TrendDirection.improving;
    } else if (firstAverage - secondAverage > threshold) {
      categoryTrends[category] = TrendDirection.declining;
    } else {
      categoryTrends[category] = TrendDirection.stable;
    }
  }

  return categoryTrends;
}
