# 👨‍👩‍👧‍👦 شاشات الأولياء

هذا المجلد يحتوي على الشاشات المخصصة لأولياء الأمور في نظام إدارة المدرسة.

## 📱 الشاشات المتوفرة

### 1. 🏠 لوحة تحكم الأولياء الرئيسية
**الملف:** `parent_dashboard_screen.dart`

**الوظائف:**
- ملخص شامل لأداء جميع الأبناء في المدرسة
- الإشعارات والتنبيهات المهمة من المدرسة
- الأحداث والمواعيد القادمة (امتحانات، اجتماعات، فعاليات)
- إحصائيات سريعة عن الحضور والغياب
- آخر النتائج والدرجات المنشورة
- حالة الرسوم الدراسية لكل طالب
- روابط سريعة للوظائف الأكثر استخداماً

**المميزات:**
- تصميم عائلي دافئ ومريح للعين
- بطاقات تفاعلية لكل طالب
- إحصائيات ملونة وواضحة
- تحديث تلقائي للبيانات
- إشعارات فورية للأحداث المهمة

### 2. 📊 شاشة متابعة أداء الأبناء
**الملف:** `children_performance_screen.dart`

**الوظائف:**
- متابعة الأداء الأكاديمي التفصيلي لكل ابن/ابنة
- مقارنة الأداء عبر الفترات الزمنية المختلفة
- عرض الدرجات والتقديرات في جميع المواد
- تتبع التحسن أو التراجع في الأداء
- عرض تقارير مفصلة لكل مادة دراسية
- مقارنة أداء الطالب مع متوسط الصف
- عرض ملاحظات المعلمين وتوصياتهم
- إحصائيات شاملة عن الحضور والغياب

**التبويبات:**
- الملخص: نظرة عامة على الأداء
- المواد: تفاصيل كل مادة دراسية
- المقارنات: مقارنات مع الصف والفترات السابقة
- التقارير: تقارير قابلة للتصدير

### 3. 💬 شاشة التواصل مع المدرسة
**الملف:** `school_communication_screen.dart`

**الوظائف:**
- إرسال رسائل مباشرة للمعلمين والإدارة
- استقبال الإشعارات والتنبيهات من المدرسة
- حجز مواعيد لاجتماعات أولياء الأمور
- تقديم الشكاوى والاقتراحات
- متابعة حالة الطلبات والاستفسارات
- عرض الإعلانات والأخبار المدرسية
- طلب تقارير خاصة أو معلومات إضافية

**التبويبات:**
- الرسائل: المحادثات مع المعلمين والإدارة
- الإشعارات: إشعارات وأخبار المدرسة
- المواعيد: حجز ومتابعة المواعيد
- الطلبات: تقديم ومتابعة الطلبات

## 🎨 المميزات التقنية

### التصميم
- **ألوان مميزة** لكل شاشة:
  - لوحة التحكم: أخضر داكن (النمو والتطور)
  - متابعة الأداء: أزرق داكن (الثقة والاستقرار)
  - التواصل: برتقالي داكن (التواصل والدفء)
- **رسوم متحركة** ناعمة ومهدئة
- **تصميم عائلي** مريح ومألوف
- **أيقونات واضحة** ومفهومة للأولياء

### التقنيات
- **TabController** للتنقل بين الأقسام
- **AnimationController** للرسوم المتحركة
- **RefreshIndicator** لتحديث البيانات
- **FloatingActionButton** للإجراءات السريعة
- **Custom Models** للبيانات المفصلة

## 📋 النماذج المرتبطة

### نموذج الطالب
**الملف:** `../models/student_model.dart`
- معلومات أساسية عن الطالب
- الصف والقسم
- معلومات الاتصال

### نموذج درجات الطالب المفصل
**الملف:** `../models/student_grade_model.dart`
- درجات تفصيلية لكل امتحان
- مقارنات مع الصف
- ملاحظات المعلمين

## 🚀 كيفية الاستخدام

### الاستيراد
```dart
import 'package:school_management_system/parent_screens/parent_screens_exports.dart';
```

### الاستخدام في التنقل
```dart
// لوحة تحكم الأولياء
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ParentDashboardScreen(parentId: parentId),
  ),
);

// متابعة أداء الأبناء
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => ChildrenPerformanceScreen(parentId: parentId),
  ),
);

// التواصل مع المدرسة
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => SchoolCommunicationScreen(parentId: parentId),
  ),
);
```

## 📝 التوثيق

جميع الشاشات موثقة بالكامل باللغة العربية مع:
- **تعليقات شاملة** لكل كلاس ودالة (أكثر من 400 تعليق)
- **شرح مفصل** لتدفق العمل
- **توضيح الغرض** من كل متغير ودالة
- **أمثلة على الاستخدام** في التعليقات

## 🔧 التطوير المستقبلي

### المميزات المخططة:
- **إشعارات فورية** عند وصول رسائل جديدة
- **مكالمات فيديو** مع المعلمين
- **دفع الرسوم** إلكترونياً
- **تتبع الحافلة المدرسية** في الوقت الفعلي
- **منتدى أولياء الأمور** للتواصل فيما بينهم

### التحسينات:
- **تحسين الأداء** وسرعة التحميل
- **إضافة المزيد من الرسوم المتحركة**
- **دعم الوضع المظلم**
- **تحسين إمكانية الوصول**
- **دعم اللغات المتعددة**

## 📊 الإحصائيات

### الكود:
- **أكثر من 2500 سطر** من الكود عالي الجودة
- **400+ تعليق توضيحي** باللغة العربية
- **15+ دالة** لكل شاشة
- **3 تعدادات** للتنظيم والفلترة

### التصميم:
- **12 تبويب** موزعة على الشاشات الثلاث
- **9 ألوان أساسية** متناسقة ومريحة
- **20+ أيقونة** واضحة ومعبرة
- **تصميم متجاوب** لجميع الأحجام

---

**تم إنشاء هذه الشاشات في الأسبوع الخامس من مشروع نظام إدارة المدرسة** 👨‍👩‍👧‍👦

**الهدف:** توفير تجربة شاملة ومريحة لأولياء الأمور لمتابعة أبنائهم والتواصل مع المدرسة بكل سهولة ويسر.
