{"indexes": [{"collectionGroup": "students", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "ASCENDING"}]}, {"collectionGroup": "assignments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "dueDate", "order": "DESCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "participants", "arrayConfig": "CONTAINS"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "communications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "guardianId", "order": "ASCENDING"}, {"fieldPath": "timestamp", "order": "DESCENDING"}]}, {"collectionGroup": "subjects", "queryScope": "COLLECTION", "fields": [{"fieldPath": "classId", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}], "fieldOverrides": []}