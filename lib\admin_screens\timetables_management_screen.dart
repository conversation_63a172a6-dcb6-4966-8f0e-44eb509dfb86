import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:school_management_system/admin_screens/widgets/timetable_view.dart';
import 'package:school_management_system/models/class_model.dart';
import 'package:school_management_system/models/timetable_model.dart';
import 'package:school_management_system/providers/class_provider.dart';
import 'package:school_management_system/providers/timetable_providers.dart';
import 'package:school_management_system/services/pdf_export_service.dart';
import 'package:school_management_system/widgets/error_message.dart';
import 'package:school_management_system/widgets/loading_indicator.dart';

class TimetablesManagementScreen extends ConsumerWidget {
  const TimetablesManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedClassId = ref.watch(selectedTimetableClassIdProvider);
    final classesAsync = ref.watch(classesStreamProvider);
    final classes = ref.watch(classesStreamProvider);
    final selectedClass = classes.asData?.value.firstWhere(
          (c) => c.id == selectedClassId,
          orElse: () => ClassModel(id: '', name: 'Unknown'),
        );
    final timetableAsync = ref.watch(timetableStreamProvider(selectedClassId ?? ''));

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الجداول الدراسية'),
        automaticallyImplyLeading: false,
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        elevation: 2,
        actions: [
          // زر الإحصائيات
          if (selectedClassId != null)
            IconButton(
              icon: const Icon(Icons.analytics),
              tooltip: 'إحصائيات الجدول',
              onPressed: () => _showTimetableStatistics(context, ref, selectedClassId),
            ),
          // زر التصدير
          if (selectedClassId != null)
            IconButton(
              icon: const Icon(Icons.download),
              tooltip: 'تصدير الجدول',
              onPressed: () => _exportTimetable(context, ref, selectedClassId),
            ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: classesAsync.when(
              data: (classes) => DropdownButtonFormField<String>(
                hint: const Text('اختر فصلاً لعرض أو تعديل جدوله'),
                value: selectedClassId,
                onChanged: (value) => ref.read(selectedTimetableClassIdProvider.notifier).state = value,
                items: classes
                    .map(
                      (doc) => DropdownMenuItem(
                        value: doc.id,
                        child: Text(doc.name),
                      ),
                    )
                    .toList(),
              ),
              loading: () => const LoadingIndicator(),
              error: (err, stack) => ErrorMessage(message: err.toString()),
            ),
          ),
          const Divider(),
          Expanded(
            child: selectedClassId == null
                ? const Center(child: Text('الرجاء اختيار فصل'))
                : TimetableView(classId: selectedClassId),
          ),
        ],
      ),
    );
  }

  /// عرض إحصائيات الجدول الزمني
  void _showTimetableStatistics(BuildContext context, WidgetRef ref, String classId) {
    final timetableAsync = ref.watch(timetableStreamProvider(classId));
    final classes = ref.watch(classesStreamProvider);
    final className = classes.asData?.value
            .firstWhere((c) => c.id == classId,
                orElse: () => ClassModel(id: '', name: ''))
            .name ??
        '';

    showDialog(
      context: context,
      builder: (context) {
        return timetableAsync.when(
          data: (timetable) {
            if (timetable == null) {
              return AlertDialog(
                title: const Text('خطأ'),
                content: const Text('لا يوجد جدول زمني لهذا الفصل.'),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('حسناً'),
                  ),
                ],
              );
            }
            final stats = TimetableStats(timetable);
            return AlertDialog(
              title: Text('إحصائيات جدول $className'),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatRow('إجمالي الحصص:', '${stats.totalSessions}'),
                    _buildStatRow('الحصص المجدولة:', '${stats.scheduledSessions}'),
                    _buildStatRow('الحصص الفارغة:', '${stats.emptySessions}'),
                    _buildStatRow('نسبة الإشغال:',
                        '${stats.occupancyPercentage.toStringAsFixed(1)}%'),
                    const Divider(),
                    const Text('توزيع المواد:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    ...stats.subjectDistribution.entries.map(
                      (entry) =>
                          _buildStatRow('${entry.key}:', '${entry.value} حصة'),
                    ),
                    const Divider(),
                    const Text('توزيع المعلمين:',
                        style: TextStyle(fontWeight: FontWeight.bold)),
                    ...stats.teacherDistribution.entries.map(
                      (entry) =>
                          _buildStatRow('${entry.key}:', '${entry.value} حصة'),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('حسناً'),
                ),
              ],
            );
          },
          loading: () => const AlertDialog(
            content: Row(
              children: [
                CircularProgressIndicator(),
                SizedBox(width: 16),
                Text('جاري تحميل الإحصائيات...'),
              ],
            ),
          ),
          error: (error, stack) => AlertDialog(
            title: const Text('خطأ'),
            content: Text('حدث خطأ في تحميل الإحصائيات: $error'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('حسناً'),
              ),
            ],
          ),
        );
      },
    );
  }

  /// عرض حوار الإحصائيات
  void _showStatsDialog(BuildContext context, TimetableStats stats, String className) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('إحصائيات جدول $className'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildStatRow('إجمالي الحصص:', '${stats.totalSessions}'),
              _buildStatRow('الحصص المجدولة:', '${stats.scheduledSessions}'),
              _buildStatRow('الحصص الفارغة:', '${stats.emptySessions}'),
              _buildStatRow('نسبة الإشغال:', '${stats.occupancyPercentage.toStringAsFixed(1)}%'),
              const Divider(),
              const Text('توزيع المواد:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...stats.subjectDistribution.entries.map(
                (entry) => _buildStatRow('${entry.key}:', '${entry.value} حصة'),
              ),
              const Divider(),
              const Text('توزيع المعلمين:', style: TextStyle(fontWeight: FontWeight.bold)),
              ...stats.teacherDistribution.entries.map(
                (entry) => _buildStatRow('${entry.key}:', '${entry.value} حصة'),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// بناء صف إحصائية
  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  /// عرض حوار التحميل
  void _showLoadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const AlertDialog(
        content: Row(
          children: [
            CircularProgressIndicator(),
            SizedBox(width: 16),
            Text('جاري تحميل الإحصائيات...'),
          ],
        ),
      ),
    );
  }

  /// عرض حوار الخطأ
  void _showErrorDialog(BuildContext context, String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('خطأ'),
        content: Text('حدث خطأ في تحميل الإحصائيات: $error'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('حسناً'),
          ),
        ],
      ),
    );
  }

  /// تصدير الجدول الزمني
  void _exportTimetable(BuildContext context, WidgetRef ref, String classId) async {
    try {
      final timetable = await ref.read(timetableStreamProvider(classId).future);
      final classes = await ref.read(classesStreamProvider.future);
      final selectedClass = classes.firstWhere(
        (c) => c.id == classId,
        orElse: () => ClassModel(id: '', name: 'Unknown'),
      );

      if (timetable == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('لا يمكن تصدير جدول فارغ')),
        );
        return;
      }

      // معالجة البيانات للتصدير
      final Map<String, List<String>> processedTimetable = {};
      final days = timetable.daysOfWeek;
      const periods = 7;

      for (var day in days) {
        processedTimetable[day] = [];
        for (int i = 1; i <= periods; i++) {
          final session = timetable.getSession(day, i);
          if (session != null) {
            processedTimetable[day]!
                .add('${session.subject}\n${session.teacherName ?? ''}');
          } else {
            processedTimetable[day]!.add('');
          }
        }
      }

      final pdfService = PdfExportService();
      await pdfService.exportTimetableToPdf(
        className: selectedClass.name,
        timetableData: processedTimetable,
        days: days,
        periods: periods,
      );

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تصدير الجدول بنجاح')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('خطأ في التصدير: ${e.toString()}')),
      );
    }
  }
}
